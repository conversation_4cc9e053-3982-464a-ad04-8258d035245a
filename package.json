{"name": "aug-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 4001", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@tanstack/react-query": "^5.83.0", "@tanstack/react-table": "^8.21.3", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.446.0", "next": "14.2.15", "next-themes": "^0.3.0", "react": "^18", "react-dom": "^18", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.60.0", "recharts": "^3.1.0", "sonner": "^1.7.4", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.76", "zustand": "^4.5.7"}, "devDependencies": {"@tanstack/react-query-devtools": "^5.83.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "eslint": "^8", "eslint-config-next": "14.2.15", "eslint-config-prettier": "^9.1.2", "eslint-plugin-prettier": "^5.5.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}