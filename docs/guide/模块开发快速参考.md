# 模块开发快速参考

新模块开发的快速参考卡片，包含关键步骤和代码模板。

## 🚀 快速开始检查清单

### 📋 开发前准备
- [ ] 明确业务需求和功能范围
- [ ] 设计数据结构和实体关系
- [ ] 确定 API 接口规范
- [ ] 规划用户界面和交互流程

### 🏗️ 基础架构（必须完成）
- [ ] 创建类型定义文件 `src/types/api/{module}.ts`
- [ ] 更新 API 端点常量 `src/constants/api.ts`
- [ ] 创建业务常量 `src/constants/{module}.ts`
- [ ] 更新类型导出 `src/types/api/index.ts`

### 📡 数据层（核心功能）
- [ ] 实现 API 函数 `src/lib/api/{module}.ts`
- [ ] 创建自定义 hooks `src/hooks/use-{module}.ts`
- [ ] 更新 API 导出 `src/lib/api/index.ts`

### 🎨 UI层（用户界面）
- [ ] 创建表格列定义 `src/components/tables/columns/{module}-columns.tsx`
- [ ] 创建表单组件 `src/components/forms/{module}-form-dialog.tsx`
- [ ] 创建页面组件 `src/app/{module}/page.tsx`
- [ ] 更新表格导出 `src/components/tables/index.ts`

### 🔗 集成配置（最后步骤）
- [ ] 添加路由常量 `src/constants/routes.ts`
- [ ] 更新导航菜单 `src/config/navigation.ts`
- [ ] 测试所有功能
- [ ] 提交代码

## 📝 代码模板速查

### 类型定义模板
```typescript
// src/types/api/{module}.ts
export interface EntityBase {
  name: string;
  description?: string;
}

export type EntityCreate = EntityBase;
export type EntityUpdate = Partial<EntityBase>;

export interface EntityRead extends EntityBase {
  id: number;
  created_at: string;
  updated_at: string;
}

export interface EntityResponse {
  success: boolean;
  data: EntityRead;
  message: string;
}
```

### API 函数模板
```typescript
// src/lib/api/{module}.ts
export async function getEntities(params?: QueryParams) {
  const response = await apiClient.get<EntityListResponse>(
    API_ENDPOINTS.ENTITIES.LIST,
    { params }
  );
  return response.data;
}

export async function createEntity(data: EntityCreate): Promise<EntityRead> {
  const response = await apiClient.post<EntityResponse>(
    API_ENDPOINTS.ENTITIES.LIST,
    data
  );
  return response.data.data!;
}
```

### Hooks 模板
```typescript
// src/hooks/use-{module}.ts
export function useEntities(params?: QueryParams) {
  return useApiQuery(
    ['entities', 'list', params],
    () => getEntities(params),
    { staleTime: 2 * 60 * 1000 }
  );
}

export function useCreateEntity() {
  return useApiMutation(
    (data: EntityCreate) => createEntity(data),
    {
      successMessage: '创建成功',
      invalidateQueries: [['entities', 'list']],
    }
  );
}
```

### 表单组件模板
```typescript
// src/components/forms/{module}-form-dialog.tsx
export function EntityFormDialog({ open, onOpenChange, entity }) {
  const form = useForm<EntityFormData>({
    resolver: zodResolver(entitySchema),
  });

  const createMutation = useCreateEntity();

  return (
    <FormDialog
      open={open}
      onOpenChange={onOpenChange}
      title={entity ? '编辑' : '新增'}
      onConfirm={form.handleSubmit(handleSubmit)}
    >
      <FormField label="名称" required>
        <Input
          {...form.register('name')}
          className={form.formState.errors.name ? 'border-destructive' : ''}
        />
        {form.formState.errors.name && (
          <p className="text-sm text-destructive mt-1">
            {form.formState.errors.name.message}
          </p>
        )}
      </FormField>
    </FormDialog>
  );
}
```

### 页面组件模板
```typescript
// src/app/{module}/page.tsx
export default function EntityManagePage() {
  const [filters, setFilters] = useState({ name: '' });
  const [createOpen, setCreateOpen] = useState(false);
  
  const { data, isLoading } = useEntities(filters);
  
  return (
    <MainLayout>
      <PageErrorBoundary>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold">实体管理</h1>
            <Button onClick={() => setCreateOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              新增
            </Button>
          </div>
          
          <DataStateWrapper
            isLoading={isLoading}
            isEmpty={!data?.data?.length}
          >
            <CompactDataTable
              columns={columns}
              data={data?.data || []}
            />
          </DataStateWrapper>
          
          <EntityFormDialog
            open={createOpen}
            onOpenChange={setCreateOpen}
          />
        </div>
      </PageErrorBoundary>
    </MainLayout>
  );
}
```

## ⚠️ 常见错误速查

### TypeScript 错误
```typescript
// ❌ 错误：使用 any
const variant = colors[status] as any;

// ✅ 正确：明确类型
const variant = colors[status] as 'success' | 'secondary';
```

### 导入错误
```typescript
// ❌ 错误：路径不对
import { PageErrorBoundary } from '@/components/error-boundary';

// ✅ 正确：使用正确路径
import { PageErrorBoundary } from '@/components/providers/error-boundary';
```

### 表单错误
```typescript
// ❌ 错误：Input 不支持 error 属性
<Input error={errors.name?.message} />

// ✅ 正确：按项目规范处理
<Input className={errors.name ? 'border-destructive' : ''} />
{errors.name && <p className="text-sm text-destructive mt-1">{errors.name.message}</p>}
```

## 🛠️ 调试命令速查

```bash
# 类型检查
npm run type-check

# 代码检查
npm run lint

# 构建测试
npm run build

# 开发启动
npm run dev
```

## 📁 文件创建顺序

1. **类型定义** → `src/types/api/{module}.ts`
2. **常量配置** → `src/constants/{module}.ts` + 更新 `api.ts`
3. **API 函数** → `src/lib/api/{module}.ts`
4. **自定义 Hooks** → `src/hooks/use-{module}.ts`
5. **表格列** → `src/components/tables/columns/{module}-columns.tsx`
6. **表单组件** → `src/components/forms/{module}-form-dialog.tsx`
7. **页面组件** → `src/app/{module}/page.tsx`
8. **路由配置** → 更新 `routes.ts` 和 `navigation.ts`

## 🎯 质量检查要点

### 编译检查
- [ ] TypeScript 编译无错误
- [ ] ESLint 检查通过
- [ ] 所有导入路径正确

### 功能检查
- [ ] CRUD 操作正常
- [ ] 表单验证工作
- [ ] 错误处理完善
- [ ] 空状态显示正确

### 用户体验检查
- [ ] 加载状态显示
- [ ] 操作反馈及时
- [ ] 错误提示友好
- [ ] 响应式设计适配

## 🚀 效率提升技巧

1. **复制现有模块**：以标签管理模块为模板
2. **批量替换**：使用编辑器的批量替换功能
3. **增量开发**：每完成一个文件就测试
4. **及时提交**：避免大批量修改

---

**使用本参考卡片可以快速、准确地开发新模块！** ⚡
