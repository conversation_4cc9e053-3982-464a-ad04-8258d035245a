# 模块开发标准流程

基于标签管理模块的开发经验，总结出的完整模块开发标准流程和最佳实践。

## 🎯 开发流程概览

```mermaid
graph TD
    A[需求分析] --> B[架构设计]
    B --> C[类型定义]
    C --> D[API层实现]
    D --> E[数据层开发]
    E --> F[UI层开发]
    F --> G[集成测试]
    G --> H[优化迭代]
```

## 📋 详细开发步骤

### 第一阶段：基础架构搭建

#### 1. 需求分析和架构设计
- **业务理解**：明确模块的业务场景和核心功能
- **数据建模**：设计实体关系和数据结构
- **功能规划**：列出CRUD操作和高级功能需求
- **交互设计**：确定用户界面和操作流程

#### 2. 类型定义 (`src/types/api/{module}.ts`)
```typescript
// 基础实体类型
export interface EntityBase {
  name: string;
  description?: string;
}

// CRUD操作类型
export type EntityCreate = EntityBase;
export type EntityUpdate = Partial<EntityBase>;

export interface EntityRead extends EntityBase {
  id: number;
  created_at: string;
  updated_at: string;
}

// 业务特定类型
export interface EntityWithStats extends EntityRead {
  usage_count: number;
}

// API响应类型
export interface EntityResponse {
  success: boolean;
  data: EntityRead;
  message: string;
}
```

#### 3. 常量配置
**API端点** (`src/constants/api.ts`)：
```typescript
ENTITIES: {
  LIST: '/api/v1/admin/entities',
  DETAIL: (id: string) => `/api/v1/admin/entities/${id}`,
  BATCH: '/api/v1/admin/entities/batch',
}
```

**业务常量** (`src/constants/{module}.ts`)：
```typescript
// 状态映射
export const EntityStatusNames = {
  active: '激活',
  inactive: '停用',
};

// 验证规则
export const ENTITY_VALIDATION = {
  NAME_MIN_LENGTH: 1,
  NAME_MAX_LENGTH: 50,
};

// 消息文本
export const ENTITY_MESSAGES = {
  CREATE_SUCCESS: '创建成功',
  UPDATE_SUCCESS: '更新成功',
  DELETE_CONFIRM: '确定要删除吗？',
};
```

### 第二阶段：数据层实现

#### 4. API函数 (`src/lib/api/{module}.ts`)
```typescript
// 基础CRUD操作
export async function getEntities(params?: QueryParams) {
  const response = await apiClient.get<EntityListResponse>(
    API_ENDPOINTS.ENTITIES.LIST,
    { params }
  );
  return response.data;
}

export async function createEntity(data: EntityCreate): Promise<EntityRead> {
  const response = await apiClient.post<EntityResponse>(
    API_ENDPOINTS.ENTITIES.LIST,
    data
  );
  return response.data.data!;
}

// 批量操作
export async function batchUpdateEntities(data: BatchUpdate): Promise<EntityRead[]> {
  const response = await apiClient.post<BatchResponse>(
    API_ENDPOINTS.ENTITIES.BATCH,
    data
  );
  return response.data.data!;
}
```

#### 5. 自定义Hooks (`src/hooks/use-{module}.ts`)
```typescript
// 数据查询
export function useEntities(params?: QueryParams) {
  return useApiQuery(
    ['entities', 'list', params],
    () => getEntities(params),
    {
      staleTime: 2 * 60 * 1000, // 2分钟缓存
    }
  );
}

// 数据变更
export function useCreateEntity() {
  return useApiMutation(
    (data: EntityCreate) => createEntity(data),
    {
      successMessage: ENTITY_MESSAGES.CREATE_SUCCESS,
      errorMessage: ENTITY_MESSAGES.CREATE_FAILED,
      invalidateQueries: [['entities', 'list']],
    }
  );
}
```

### 第三阶段：UI层开发

#### 6. 表格列定义 (`src/components/tables/columns/{module}-columns.tsx`)
```typescript
export function createEntityColumns({
  onEdit,
  onDelete,
  onStatusToggle,
}: EntityColumnsProps): ColumnDef<EntityRead>[] {
  return [
    {
      accessorKey: 'name',
      header: '名称',
      cell: ({ row }) => {
        const entity = row.original;
        return (
          <div className="font-medium">{entity.name}</div>
        );
      },
    },
    {
      accessorKey: 'status',
      header: '状态',
      cell: ({ row }) => {
        const status = row.getValue('status') as string;
        return (
          <Badge variant={status === 'active' ? 'success' : 'secondary'}>
            {EntityStatusNames[status]}
          </Badge>
        );
      },
    },
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }) => (
        <EntityActionsDropdown
          entity={row.original}
          onEdit={onEdit}
          onDelete={onDelete}
          onStatusToggle={onStatusToggle}
        />
      ),
    },
  ];
}
```

#### 7. 表单组件 (`src/components/forms/{module}-form-dialog.tsx`)
```typescript
export function EntityFormDialog({
  open,
  onOpenChange,
  entity,
}: EntityFormDialogProps) {
  const form = useForm<EntityFormData>({
    resolver: zodResolver(entitySchema),
    defaultValues: {
      name: '',
      description: '',
    },
  });

  const createMutation = useCreateEntity();
  const updateMutation = useUpdateEntity();

  const handleSubmit = async (data: EntityFormData) => {
    if (entity) {
      await updateMutation.mutateAsync({ id: entity.id.toString(), data });
    } else {
      await createMutation.mutateAsync(data);
    }
    onOpenChange(false);
  };

  return (
    <FormDialog
      open={open}
      onOpenChange={onOpenChange}
      title={entity ? '编辑' : '新增'}
      onConfirm={form.handleSubmit(handleSubmit)}
    >
      <FormField label="名称" required>
        <Input
          {...form.register('name')}
          className={form.formState.errors.name ? 'border-destructive' : ''}
        />
        {form.formState.errors.name && (
          <p className="text-sm text-destructive mt-1">
            {form.formState.errors.name.message}
          </p>
        )}
      </FormField>
    </FormDialog>
  );
}
```

#### 8. 页面组件 (`src/app/{module}/page.tsx`)
```typescript
export default function EntityManagePage() {
  // 状态管理
  const [filters, setFilters] = useState({
    name: '',
    status: undefined,
  });
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  
  // API hooks
  const { data, isLoading } = useEntities(filters);
  const deleteMutation = useDeleteEntity();
  
  // 事件处理
  const handleCreate = () => setCreateOpen(true);
  const handleEdit = (entity) => {
    setEditingEntity(entity);
    setEditOpen(true);
  };
  
  return (
    <MainLayout>
      <PageErrorBoundary>
        <div className="space-y-6">
          {/* 页面标题和统计 */}
          <PageHeader />
          
          {/* 操作栏 */}
          <ActionBar onCreate={handleCreate} />
          
          {/* 数据表格 */}
          <DataStateWrapper
            isLoading={isLoading}
            isEmpty={!data?.data?.length}
            emptyTitle="暂无数据"
          >
            <CompactDataTable
              columns={columns}
              data={data?.data || []}
            />
          </DataStateWrapper>
          
          {/* 弹框组件 */}
          <EntityFormDialog />
          <ConfirmDialog />
        </div>
      </PageErrorBoundary>
    </MainLayout>
  );
}
```

### 第四阶段：集成配置

#### 9. 路由和导航配置
**路由常量** (`src/constants/routes.ts`)：
```typescript
ENTITIES: '/entities',
```

**导航菜单** (`src/config/navigation.ts`)：
```typescript
{
  title: '实体管理',
  href: '/entities',
  icon: EntityIcon,
}
```

#### 10. 导出配置
更新所有相关的 `index.ts` 文件：
```typescript
// src/types/api/index.ts
export * from './entities';

// src/lib/api/index.ts  
export * from './entities';

// src/components/tables/index.ts
export { createEntityColumns } from './columns/entity-columns';
```

## ⚠️ 关键注意事项

### 1. 类型安全
```typescript
// ❌ 避免使用any
const variant = statusColors[status] as any;

// ✅ 明确类型定义
const variant = statusColors[status] as 'success' | 'secondary';
```

### 2. 表单错误处理
```typescript
// ✅ 按项目规范处理错误
<Input className={errors.field ? 'border-destructive' : ''} />
{errors.field && (
  <p className="text-sm text-destructive mt-1">{errors.field.message}</p>
)}
```

### 3. 空状态处理
```typescript
// ✅ 区分搜索为空和真正的数据为空
emptyTitle={hasFilters ? "搜索结果为空" : "暂无数据"}
emptyDescription={
  hasFilters 
    ? "没有找到符合条件的数据"
    : "还没有创建任何数据"
}
```

## 🕳️ 常见踩坑

### 1. 函数重复定义
- **问题**：编辑时容易产生重复的函数定义
- **解决**：仔细检查，使用搜索功能避免重复

### 2. 导入路径错误
- **问题**：组件路径变更导致导入失败
- **解决**：使用codebase-retrieval工具查找正确路径

### 3. API接口不一致
- **问题**：前端类型定义与后端接口不匹配
- **解决**：及时同步接口变更，保持类型定义最新

### 4. 状态管理复杂化
- **问题**：过多的状态变量导致组件复杂
- **解决**：合理拆分组件，使用自定义hooks抽离逻辑

## 🎯 最佳实践

### 1. 渐进式开发
- 先实现基础CRUD，再添加高级功能
- 每个功能点完成后立即测试
- 及时提交，避免大批量修改

### 2. 用户体验优先
- 弹框方案适合简单数据编辑
- 智能的空状态和错误提示
- 批量操作提升管理效率

### 3. 代码质量保证
- 严格的TypeScript类型检查
- 统一的错误处理规范
- 可复用的组件设计

### 4. 性能优化
- 合理的缓存策略（staleTime设置）
- 避免不必要的重渲染
- 懒加载和代码分割

## 📊 开发效率提升

1. **模板化开发**：使用本文档作为标准模板
2. **工具辅助**：充分利用codebase-retrieval等工具
3. **增量测试**：每个功能点完成后立即验证
4. **文档同步**：及时更新相关文档和类型定义

## 🚀 质量检查清单

### 开发完成前检查
- [ ] 所有TypeScript类型定义正确
- [ ] API接口调用正常
- [ ] 表单验证和错误处理完整
- [ ] 空状态和加载状态处理
- [ ] 响应式设计适配
- [ ] 错误边界保护
- [ ] 路由和导航配置
- [ ] 权限控制（如需要）

### 代码质量检查
- [ ] ESLint检查通过
- [ ] TypeScript编译无错误
- [ ] 组件可复用性良好
- [ ] 代码注释充分
- [ ] 命名规范一致
- [ ] 文件组织合理

## 📁 文件结构模板

```
src/
├── types/api/
│   └── {module}.ts              # 类型定义
├── constants/
│   ├── api.ts                   # API端点（更新）
│   └── {module}.ts              # 业务常量
├── lib/api/
│   ├── index.ts                 # API导出（更新）
│   └── {module}.ts              # API函数
├── hooks/
│   └── use-{module}.ts          # 自定义hooks
├── components/
│   ├── forms/
│   │   └── {module}-form-dialog.tsx  # 表单组件
│   └── tables/
│       ├── index.ts             # 表格导出（更新）
│       └── columns/
│           └── {module}-columns.tsx   # 表格列定义
├── app/
│   └── {module}/
│       └── page.tsx             # 页面组件
└── config/
    └── navigation.ts            # 导航配置（更新）
```

## 🔧 开发工具和命令

### 常用开发命令
```bash
# 开发环境启动
npm run dev

# 类型检查
npm run type-check

# 代码检查
npm run lint

# 构建测试
npm run build
```

### 调试技巧
```typescript
// 使用codebase-retrieval查找组件
// 使用diagnostics检查类型错误
// 使用view工具查看文件内容
```

## 📖 相关文档

- [快速开发指南](./快速开发指南.md)
- [UI设计规范](../UI设计相关/)
- [项目结构规范](../../.augment/rules/imported/project-structure.md)
- [技术栈规范](../../.augment/rules/imported/tech-stack.md)

---

**使用本模板可以快速、高质量地开发出功能完整的管理模块！** 🚀
