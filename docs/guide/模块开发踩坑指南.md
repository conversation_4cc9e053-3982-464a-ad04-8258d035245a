# 模块开发踩坑指南

基于标签管理模块开发过程中遇到的实际问题，整理的踩坑经验和解决方案。

## 🚨 高频踩坑问题

### 1. TypeScript 类型问题

#### 问题：使用 `any` 类型导致类型检查失效
```typescript
// ❌ 错误做法
const variant = TagStatusColors[statusKey] as any;
```

**解决方案**：
```typescript
// ✅ 正确做法
const variant = TagStatusColors[statusKey] as 'success' | 'secondary';
```

**预防措施**：
- 严格定义联合类型
- 使用 TypeScript 的严格模式
- 定期运行类型检查

#### 问题：空接口定义导致 ESLint 警告
```typescript
// ❌ 会触发 ESLint 警告
export interface TagCategoryCreate extends TagCategoryBase {}
```

**解决方案**：
```typescript
// ✅ 使用类型别名
export type TagCategoryCreate = TagCategoryBase;
```

### 2. 组件导入路径问题

#### 问题：错误边界组件路径错误
```typescript
// ❌ 错误路径
import { PageErrorBoundary } from '@/components/error-boundary';
```

**解决方案**：
```typescript
// ✅ 正确路径
import { PageErrorBoundary } from '@/components/providers/error-boundary';
```

**预防措施**：
- 使用 codebase-retrieval 工具查找正确路径
- 建立统一的导入路径规范
- 使用 IDE 的自动导入功能

#### 问题：组件导出不一致
```typescript
// ❌ DataStateWrapper 从错误位置导入
import { CompactDataTable, DataStateWrapper } from '@/components/tables';
```

**解决方案**：
```typescript
// ✅ 从正确位置导入
import { CompactDataTable } from '@/components/tables';
import { DataStateWrapper } from '@/components/ui/loading-states';
```

### 3. 表单处理问题

#### 问题：Input 组件不支持 error 属性
```typescript
// ❌ shadcn/ui Input 组件不支持 error 属性
<Input error={errors.name?.message} />
```

**解决方案**：
```typescript
// ✅ 按项目规范处理错误状态
<Input className={errors.name ? 'border-destructive' : ''} />
{errors.name && (
  <p className="text-sm text-destructive mt-1">
    {errors.name.message}
  </p>
)}
```

**最佳实践**：
- 统一错误处理样式
- 创建可复用的表单字段组件
- 遵循项目的 UI 规范

### 4. 函数重复定义

#### 问题：编辑过程中产生重复函数
```typescript
// ❌ 重复定义导致编译错误
const handleToggleTagFilters = () => { /* ... */ };
const handleToggleTagFilters = () => { /* ... */ };
```

**解决方案**：
- 使用搜索功能检查重复
- 及时删除多余的函数定义
- 使用 Git diff 检查变更

**预防措施**：
- 小步提交，避免大批量修改
- 使用 IDE 的重构功能
- 定期检查代码重复

### 5. API 接口类型不匹配

#### 问题：前端类型与后端接口不同步
```typescript
// ❌ 前端期望的类型
interface TagWithCategory {
  category: { id: number; name: string; };
}

// 实际后端返回的类型
interface TagWithTeacherCount {
  teacher_count: number;
}
```

**解决方案**：
1. 及时同步接口变更
2. 更新相关类型定义
3. 修改所有使用该类型的地方

**预防措施**：
- 建立接口文档同步机制
- 使用 API 类型生成工具
- 定期验证接口一致性

## 🛠️ 调试技巧

### 1. 编译错误调试

#### 常见编译错误类型
```bash
# 类型错误
Type error: Property 'xxx' does not exist on type 'yyy'

# 导入错误  
Module not found: Can't resolve '@/components/xxx'

# 重复定义错误
the name `xxx` is defined multiple times
```

#### 调试步骤
1. **查看错误信息**：仔细阅读错误提示
2. **检查类型定义**：确认类型是否正确
3. **验证导入路径**：使用工具查找正确路径
4. **搜索重复定义**：使用搜索功能查找重复

### 2. 运行时错误调试

#### 常见运行时问题
- 组件渲染错误
- API 调用失败
- 状态更新异常

#### 调试方法
```typescript
// 使用 console.log 调试
console.log('当前状态:', state);

// 使用 React DevTools
// 检查组件状态和 props

// 使用 Network 面板
// 检查 API 请求和响应
```

### 3. 性能问题调试

#### 常见性能问题
- 不必要的重渲染
- 内存泄漏
- 大量数据渲染卡顿

#### 优化方案
```typescript
// 使用 React.memo 避免重渲染
const MemoizedComponent = React.memo(Component);

// 使用 useMemo 缓存计算结果
const expensiveValue = useMemo(() => {
  return computeExpensiveValue(data);
}, [data]);

// 使用 useCallback 缓存函数
const handleClick = useCallback(() => {
  // 处理点击
}, [dependency]);
```

## 📋 质量检查清单

### 开发过程中检查
- [ ] TypeScript 类型定义正确
- [ ] 导入路径无误
- [ ] 无重复函数定义
- [ ] 表单错误处理符合规范
- [ ] API 接口类型同步

### 提交前检查
- [ ] 编译无错误
- [ ] ESLint 检查通过
- [ ] 功能测试正常
- [ ] 无 console.log 残留
- [ ] 代码格式化正确

### 部署前检查
- [ ] 生产构建成功
- [ ] 所有功能正常
- [ ] 性能表现良好
- [ ] 错误处理完善
- [ ] 用户体验流畅

## 🎯 最佳实践总结

### 1. 开发习惯
- **小步提交**：每完成一个功能点就提交
- **及时测试**：写完代码立即测试功能
- **代码审查**：提交前自己先审查一遍
- **文档同步**：及时更新相关文档

### 2. 错误处理
- **统一规范**：遵循项目的错误处理规范
- **用户友好**：提供清晰的错误提示
- **边界保护**：使用错误边界防止崩溃
- **日志记录**：记录关键操作和错误

### 3. 性能优化
- **合理缓存**：设置适当的缓存策略
- **懒加载**：大组件使用懒加载
- **避免重渲染**：使用 memo 和 callback
- **数据分页**：大量数据使用分页

### 4. 代码质量
- **类型安全**：严格的 TypeScript 类型检查
- **组件复用**：提取可复用的组件
- **逻辑分离**：使用自定义 hooks 分离逻辑
- **命名规范**：使用描述性的命名

## 🚀 效率提升工具

### 1. 开发工具
- **codebase-retrieval**：查找代码和组件
- **diagnostics**：检查类型错误
- **view**：查看文件内容
- **str-replace-editor**：批量修改代码

### 2. 调试工具
- **React DevTools**：组件状态调试
- **Chrome DevTools**：网络和性能调试
- **TypeScript Compiler**：类型检查
- **ESLint**：代码质量检查

### 3. 自动化工具
- **Prettier**：代码格式化
- **Husky**：Git hooks
- **lint-staged**：提交前检查
- **GitHub Actions**：CI/CD 流程

---

**遵循本指南可以避免大部分常见问题，提升开发效率和代码质量！** 🎯
