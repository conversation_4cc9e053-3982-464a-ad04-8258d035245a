# 深色模式颜色规范

## 核心原则

1. **使用 Tailwind CSS 变量和类**：优先使用 Tailwind CSS 提供的语义化类名，如 `bg-card`、`text-foreground` 等，而非直接使用颜色值
2. **避免硬编码颜色**：不要使用硬编码的颜色值，如 `bg-white` 或 `text-black`
3. **总是提供深色模式变体**：对于自定义颜色，始终提供深色模式变体，如 `bg-gray-100 dark:bg-gray-800`
4. **保持对比度**：确保文本和背景之间有足够的对比度，特别是在深色模式下

## 正确的颜色使用方式

### 1. 使用语义化颜色变量

```jsx
// ✅ 正确：使用语义化颜色变量
<div className="bg-card text-card-foreground">
  <p className="text-foreground">内容</p>
  <p className="text-muted-foreground">次要内容</p>
</div>

// ❌ 错误：硬编码颜色
<div className="bg-white text-black">
  <p className="text-black">内容</p>
  <p className="text-gray-500">次要内容</p>
</div>
```

### 2. 常用语义化颜色变量

| 用途       | 类名                      | 说明                     |
| ---------- | ------------------------- | ------------------------ |
| 页面背景   | `bg-background`           | 整个页面的背景色         |
| 页面文本   | `text-foreground`         | 主要文本颜色             |
| 卡片背景   | `bg-card`                 | 卡片、面板等组件的背景色 |
| 卡片文本   | `text-card-foreground`    | 卡片内的文本颜色         |
| 次要文本   | `text-muted-foreground`   | 次要文本、说明文字等     |
| 边框       | `border-border`           | 边框颜色                 |
| 输入框     | `bg-input`                | 输入框背景色             |
| 主题色     | `bg-primary`              | 主题色背景               |
| 主题色文本 | `text-primary-foreground` | 主题色上的文本           |

### 3. 自定义颜色时提供深色模式变体

```jsx
// ✅ 正确：提供深色模式变体
<div className="bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100">
  自定义颜色内容
</div>

// ❌ 错误：没有提供深色模式变体
<div className="bg-gray-100 text-gray-900">
  自定义颜色内容
</div>
```

### 4. 状态颜色

对于表示状态的颜色（如成功、警告、错误等），使用以下方式：

```jsx
// ✅ 正确：使用语义化的状态颜色
<div className="bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300">
  成功状态
</div>

<div className="bg-yellow-50 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300">
  警告状态
</div>

<div className="bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300">
  错误状态
</div>
```

## 常见问题及解决方案

### 1. 表格和列表

对于表格和列表，使用 `bg-card` 作为背景色，使用 `bg-muted` 作为交替行或悬停状态的背景色：

```jsx
<table className="bg-card border-border">
  <thead className="bg-muted/50">
    <tr>...</tr>
  </thead>
  <tbody>
    <tr className="hover:bg-muted/30">...</tr>
  </tbody>
</table>
```

### 2. 表单元素

表单元素应使用 `bg-input` 作为背景色：

```jsx
<input className="bg-input border-input" />
```

### 3. 状态指示器

状态指示器（如徽章、标签等）应根据状态提供适当的深色模式变体：

```jsx
// 活跃状态
<span className="bg-green-500 dark:bg-green-600 text-white">活跃</span>

// 非活跃状态
<span className="bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-200">非活跃</span>
```

## 颜色检查清单

在提交代码前，请检查以下几点：

1. ✅ 是否使用了语义化的颜色变量而非硬编码颜色？
2. ✅ 自定义颜色是否提供了深色模式变体？
3. ✅ 文本和背景之间是否有足够的对比度？
4. ✅ 状态颜色是否在深色模式下仍然清晰可辨？
5. ✅ 是否避免了使用固定的颜色值（如 `#ffffff`）？

遵循这些规范可以确保我们的应用在深色模式下提供一致、舒适的用户体验。
