# 中后台管理系统快速开发指南

本指南旨在帮助开发人员快速上手项目，了解核心功能和开发流程。

## 0. 环境准备

### 0.1 安装依赖

```bash
npm install
```

### 0.2 环境变量配置

创建 `.env.local` 文件：

```
NEXT_PUBLIC_API_BASE_URL=http://127.0.0.1:3000
NEXT_PUBLIC_APP_NAME=管理系统
```

### 0.3 启动开发服务器

```bash
npm run dev
```

服务器将在 http://localhost:3005 启动。

## 1. 核心目录结构

```
src/
├── app/                    # 页面路由
│   ├── (auth)/            # 认证相关页面
│   ├── dashboard/         # 仪表盘页面
│   ├── users/             # 用户管理
│   └── members/           # 会员管理
├── components/            # UI组件
│   ├── ui/               # 基础UI组件
│   ├── layout/           # 布局组件
│   ├── forms/            # 表单组件
│   └── tables/           # 表格组件
├── lib/                  # 核心库
│   ├── api/              # API交互
│   ├── auth/             # 认证逻辑
│   └── stores/           # 状态管理
├── hooks/                # 自定义hooks
├── types/                # 类型定义
└── constants/            # 常量定义
```

## 2. 样式规范

### Tailwind CSS 使用

- 完全使用原子类
- 禁止自定义 CSS 文件
- 响应式设计优先

### 组件设计原则

- 基于 shadcn/ui 组件
- 保持设计一致性
- 支持主题切换

## 3. 快速开发流程

### 3.1 认证系统

项目已实现完整的JWT认证系统，包括登录、登出和路由保护。

**使用方式**：

```tsx
import { useAuth } from '@/hooks/use-auth';

export function MyComponent() {
  const { user, isAuthenticated, login, logout } = useAuth();

  // 使用认证状态和方法
}
```

### 3.2 API请求

项目使用TanStack Query和自定义hooks简化API请求：

```tsx
// 查询数据
const { data, isLoading } = useUserList();

// 变更数据
const createUserMutation = useCreateUser();
await createUserMutation.mutateAsync(userData);
```

### 3.3 页面开发

1. **创建页面文件**

在 `src/app` 下创建对应路径的页面文件：

```tsx
// src/app/my-feature/page.tsx
'use client';

import { MainLayout } from '@/components/layout/main-layout';

export default function MyFeaturePage() {
  return (
    <MainLayout>
      {/* 页面内容 */}
      <h1>我的新功能</h1>
    </MainLayout>
  );
}
```

2. **添加导航项**

在 `src/config/navigation.ts` 中添加导航项：

```tsx
{
  title: '我的功能',
  href: '/my-feature',
  icon: SomeIcon,
}
```

### 3.4 表格开发

项目使用 `DataTable` 组件实现数据表格：

````tsx
// 1. 创建列定义
// src/components/tables/columns/my-feature-columns.tsx
export function createMyFeatureColumns({ onEdit, onDelete }) {
  return [
    {
      accessorKey: 'name',
      header: '名称',
      // ...
    },
    // 其他列...
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }) => {
        // 操作按钮...
      },
    },
  ];
}

### 3.5 表单开发

项目使用 React Hook Form 和 Zod 处理表单：

```tsx
// 1. 创建表单组件
// src/components/forms/my-feature-form.tsx
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

// 表单验证schema
const formSchema = z.object({
  name: z.string().min(2, '名称至少2个字符'),
  // 其他字段...
});

export function MyFeatureForm({ onSubmit }) {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(formSchema),
  });

  return <form onSubmit={handleSubmit(onSubmit)}>{/* 表单字段 */}</form>;
}

// 2. 使用表单
import { MyFeatureForm } from '@/components/forms/my-feature-form';

const handleSubmit = async data => {
  await createMyFeatureMutation.mutateAsync(data);
};

<MyFeatureForm onSubmit={handleSubmit} />;
````

### 3.6 API集成

1. **添加API端点**

在 `src/constants/api.ts` 中添加端点：

```tsx
MY_FEATURE: {
  LIST: '/api/v1/my-feature/',
  CREATE: '/api/v1/my-feature/',
  GET: (id: string) => `/api/v1/my-feature/${id}`,
  UPDATE: (id: string) => `/api/v1/my-feature/${id}`,
  DELETE: (id: string) => `/api/v1/my-feature/${id}/delete`,
},
```

2. **创建API函数**

在 `src/lib/api` 下创建对应模块：

```tsx
// src/lib/api/my-feature.ts
import apiClient from './client';
import { API_ENDPOINTS } from '@/constants/api';

export async function getMyFeatureList() {
  const response = await apiClient.get(API_ENDPOINTS.MY_FEATURE.LIST);
  return response.data;
}

export async function createMyFeature(data) {
  const response = await apiClient.post(API_ENDPOINTS.MY_FEATURE.CREATE, data);
  return response.data.data;
}

// 其他API函数...
```

3. **创建自定义hooks**

在 `src/hooks` 下创建对应hooks：

```tsx
// src/hooks/use-my-feature.ts
import { useApiQuery, useApiMutation } from './use-api';
import { getMyFeatureList, createMyFeature } from '@/lib/api/my-feature';

export function useMyFeatureList() {
  return useApiQuery(['my-feature', 'list'], getMyFeatureList);
}

export function useCreateMyFeature() {
  return useApiMutation(data => createMyFeature(data), {
    successMessage: '创建成功',
    errorMessage: '创建失败',
    invalidateQueries: [['my-feature', 'list']],
  });
}

// 其他hooks...
```

## 4. 常用功能示例

### 4.1 用户认证

```tsx
// 登录
const { login } = useAuth();
await login({ username, password });

// 登出
const { logout } = useAuth();
await logout();

// 获取用户信息
const { user, isAuthenticated } = useAuth();
```

### 4.2 数据获取和变更

```tsx
// 获取列表数据
const { data, isLoading, error } = useUserList();

// 获取详情数据
const { data: user } = useUserDetail(id);

// 创建数据
const createMutation = useCreateUser();
await createMutation.mutateAsync(userData);

// 更新数据
const updateMutation = useUpdateUser();
await updateMutation.mutateAsync({ id, data: userData });

// 删除数据
const deleteMutation = useDeleteUser();
await deleteMutation.mutateAsync(id);
```

### 4.3 通知提示

```tsx
import { toast } from 'sonner';

// 成功通知
toast.success('操作成功');

// 错误通知
toast.error('操作失败');

// 警告通知
toast.warning('警告信息');
```

### 4.4 状态管理

```tsx
// 定义store
// src/lib/stores/my-feature-store.ts
import { create } from 'zustand';

interface MyFeatureStore {
  someState: string;
  setSomeState: (value: string) => void;
}

export const useMyFeatureStore = create<MyFeatureStore>(set => ({
  someState: '',
  setSomeState: value => set({ someState: value }),
}));

// 使用store
import { useMyFeatureStore } from '@/lib/stores/my-feature-store';

const { someState, setSomeState } = useMyFeatureStore();
```

## 5. 开发最佳实践

### 5.1 代码风格

- 使用函数式组件和hooks
- 组件文件使用PascalCase命名
- 工具函数和hooks使用camelCase命名
- 使用TypeScript类型定义

### 5.2 组件开发

- 优先使用和扩展现有组件
- 保持组件的单一职责
- 将复杂逻辑抽离到hooks中
- 使用TypeScript Props接口定义组件属性

### 5.3 API请求

- 使用useApiQuery和useApiMutation封装请求
- 合理设置缓存策略
- 处理加载和错误状态

### 5.4 错误处理

**分层错误处理策略**：

- **API Client层**：只处理系统级错误（网络、认证401、权限403、服务器500错误）
- **Hook层**：处理业务错误，显示用户友好的错误消息（推荐）
- **组件层**：处理UI状态和业务逻辑（跳转、表单重置等）

**最佳实践**：

```tsx
// ❌ 错误：重复的错误处理
const handleSubmit = async data => {
  try {
    await mutation.mutateAsync(data);
    toast.success('操作成功'); // Hook已处理，重复了
    router.push('/list');
  } catch (error) {
    toast.error('操作失败'); // Hook已处理，重复了
  }
};

// ✅ 正确：简洁的错误处理
const handleSubmit = async data => {
  await mutation.mutateAsync(data);
  router.push('/list');
  // 成功和错误消息由Hook统一处理
};

// ✅ 特殊情况：需要自定义处理
const handleSubmit = async data => {
  try {
    await mutation.mutateAsync(data);
    router.push('/list');
  } catch (error) {
    // 只处理特殊业务逻辑，不显示toast
    if (error.message.includes('特殊情况')) {
      // 特殊处理
    }
  }
};
```

**避免重复提示**：

- 确保同一个错误只显示一次toast
- API Client拦截器不处理业务错误的toast显示
- 页面组件不重复显示Hook已处理的消息

**生产环境优化**：

```tsx
// ✅ 网络异常检测
if (!navigator.onLine) {
  // 离线状态特殊处理
  toast.error('网络连接已断开，请检查网络设置');
}

// ✅ 防止401重复跳转
let isRedirectingToLogin = false;
if (status === 401 && !isRedirectingToLogin) {
  isRedirectingToLogin = true;
  handleUnauthorized();
  setTimeout(() => {
    isRedirectingToLogin = false;
  }, 5000);
}

// ✅ 保留错误堆栈
const apiError = new Error(error.message);
apiError.stack = error.stack;
apiError.response = error.response; // 保留调试信息
```

**全局错误边界**：

```tsx
// ✅ 全局错误边界 - 防止应用崩溃
<GlobalErrorBoundary>
  <App />
</GlobalErrorBoundary>

// ✅ 页面级错误边界 - 局部错误处理
<PageErrorBoundary
  fallback={CustomErrorFallback}
  onError={(error) => console.log('页面错误:', error)}
>
  <UserManagement />
</PageErrorBoundary>

// ✅ 简单错误边界 - 小组件使用
<ErrorBoundary FallbackComponent={SimpleErrorFallback}>
  <Chart data={chartData} />
</ErrorBoundary>
```

**错误边界使用场景**：

- 全局错误边界：捕获整个应用的渲染错误，防止白屏
- 页面级错误边界：捕获页面组件错误，其他功能继续工作
- 组件级错误边界：捕获第三方组件或复杂组件的错误

### 5.5 性能优化

- 避免不必要的渲染
- 合理使用React.memo和useMemo
- 使用虚拟化处理大量数据

## 6. 常见问题解决

### 6.1 认证问题

- **令牌过期**：系统已配置自动刷新
- **登录失败**：检查API端点和凭据

### 6.2 API请求问题

- **请求失败**：检查网络和API端点
- **数据格式错误**：确保API响应格式符合规范

### 6.3 布局问题

- **响应式适配**：检查媒体查询和条件渲染
- **组件重叠**：检查z-index和定位

### 6.4 表单问题

- **验证错误**：检查schema定义
- **提交失败**：检查API请求和错误处理
