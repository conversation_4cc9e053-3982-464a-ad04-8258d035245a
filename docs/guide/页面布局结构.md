# 页面布局结构详解

## 整体布局架构

本项目采用现代化的布局架构，具有良好的响应式设计和用户体验。整体布局如下：

```
┌─────────────────────────────────────────────────────────────┐
│                    侧边栏 (Sidebar)                          │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              Header (粘性定位)                           │ │
│  │  搜索框 | 主题切换 | 用户菜单                              │ │
│  ├─────────────────────────────────────────────────────────┤ │
│  │           面包屑导航 (粘性定位)                           │ │
│  │  首页 > 用户管理                                         │ │
│  ├─────────────────────────────────────────────────────────┤ │
│  │                                                         │ │
│  │                页面内容区域                              │ │
│  │                                                         │ │
│  │                                                         │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 布局组件详解

### 1. 主布局容器 (MainLayout)

`MainLayout` 是整个应用的主布局容器，位于 `src/components/layout/main-layout.tsx`。它负责组织页面的整体结构，包括侧边栏、顶部栏、面包屑导航和内容区域。

```tsx
// 主布局组件结构
<div className="min-h-screen bg-background">
  {/* 侧边栏 */}
  <Sidebar />

  {/* 主内容区域 */}
  <div
    className={cn(
      'flex flex-col transition-all duration-300',
      'md:ml-0',
      sidebarCollapsed ? 'md:ml-16' : 'md:ml-64'
    )}
  >
    {/* 顶部栏 */}
    <Header />

    {/* 内容区域 */}
    <main className="flex-1">
      {/* 面包屑导航 */}
      <div className="sticky top-16 z-20 border-b bg-background">
        <div className="px-4 py-3">
          <Breadcrumb />
        </div>
      </div>

      {/* 页面内容 */}
      <div className="p-4">{children}</div>
    </main>
  </div>
</div>
```

### 2. 侧边栏 (Sidebar)

侧边栏组件位于 `src/components/layout/sidebar.tsx`，负责显示应用的导航菜单。

#### 关键特性：

- **固定定位**：使用 `fixed left-0 top-0` 固定在页面左侧
- **响应式宽度**：
  - 展开状态：`w-64` (256px)
  - 折叠状态：`w-16` (64px)
- **移动端适配**：在移动设备上转为抽屉式侧边栏
- **嵌套菜单**：支持多级菜单结构
- **状态管理**：折叠状态通过 `ui-store` 进行管理

#### 代码实现：

```tsx
// 桌面端侧边栏
<div className={cn(
  'hidden md:flex h-full flex-col fixed left-0 top-0 z-40 border-r bg-background transition-all duration-300',
  sidebarCollapsed ? 'w-16' : 'w-64',
)}>
  <SidebarContent
    pathname={pathname}
    collapsed={sidebarCollapsed}
    onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
  />
</div>

// 移动端侧边栏
<Sheet>
  <SheetTrigger asChild>
    <Button variant="outline" size="icon" className="md:hidden fixed left-4 top-4 z-50">
      <Menu className="h-4 w-4" />
    </Button>
  </SheetTrigger>
  <SheetContent side="left" className="w-64 p-0">
    <SidebarContent pathname={pathname} collapsed={false} />
  </SheetContent>
</Sheet>
```

### 3. 顶部栏 (Header)

顶部栏组件位于 `src/components/layout/header.tsx`，包含搜索框、用户菜单和主题切换按钮。

#### 关键特性：

- **粘性定位**：使用 `sticky top-0 z-30` 确保在滚动时始终可见
- **固定高度**：`h-16` (64px)
- **边框样式**：底部有边框 `border-b`
- **响应式设计**：在不同屏幕尺寸下调整内容布局

#### 代码实现：

```tsx
<header className="sticky top-0 z-30 flex h-16 items-center justify-between border-b bg-background px-4 transition-all duration-300">
  {/* 左侧：为移动端菜单按钮预留空间 */}
  <div className="flex items-center">
    <div className="md:hidden w-10" />
  </div>

  {/* 右侧：搜索、用户菜单、主题切换 */}
  <div className="flex items-center space-x-4">
    {/* 搜索框 */}
    <div className="relative hidden sm:block">
      <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
      <Input placeholder="搜索..." className="w-64 pl-10" />
    </div>

    {/* 主题切换 */}
    <ThemeToggle />

    {/* 用户菜单 */}
    <DropdownMenu>{/* 用户菜单内容 */}</DropdownMenu>
  </div>
</header>
```

### 4. 面包屑导航 (Breadcrumb)

面包屑导航组件位于 `src/components/layout/breadcrumb.tsx`，显示当前页面的层级路径。

#### 关键特性：

- **粘性定位**：使用 `sticky top-16 z-20` 确保在顶部栏下方固定
- **边框样式**：底部有边框 `border-b`
- **适当内边距**：`px-4 py-3` 提供良好的间距
- **动态生成**：根据当前路由路径自动生成面包屑

#### 代码实现：

```tsx
<div className="sticky top-16 z-20 border-b bg-background transition-all duration-300">
  <div className="px-4 py-3">
    <Breadcrumb />
  </div>
</div>
```

### 5. 页面内容区域

页面内容区域是实际内容的容器，包含在 `MainLayout` 组件内。

#### 关键特性：

- **弹性布局**：使用 `flex-1` 占据剩余空间
- **内边距**：`p-4` 提供适当的内容间距
- **自适应宽度**：根据侧边栏状态自动调整左边距

#### 代码实现：

```tsx
<div className="p-4">{children}</div>
```

## 响应式设计详解

### 桌面端 (md及以上)

- **侧边栏**：
  - 始终可见，可折叠/展开
  - 展开时宽度为 256px (`w-64`)
  - 折叠时宽度为 64px (`w-16`)
- **主内容区域**：
  - 左边距随侧边栏状态变化
  - 展开时：`md:ml-64`
  - 折叠时：`md:ml-16`
- **顶部栏和面包屑**：
  - 完全延伸至左侧边缘
  - 搜索框完整显示

### 移动端 (md以下)

- **侧边栏**：
  - 默认隐藏，通过按钮触发显示
  - 以抽屉形式从左侧滑入，宽度为 256px (`w-64`)
- **主内容区域**：
  - 无左边距 (`md:ml-0`)
  - 占据整个屏幕宽度
- **顶部栏**：
  - 左侧预留菜单按钮空间
  - 搜索框隐藏，替换为搜索图标按钮

## 粘性定位与层级管理

为确保良好的用户体验，布局组件采用了精心设计的粘性定位和层级管理：

1. **顶部栏**：`z-30, top-0`
   - 最高优先级，始终位于顶部
   - 滚动时保持固定在视口顶部

2. **面包屑导航**：`z-20, top-16`
   - 次高优先级，位于顶部栏下方
   - 滚动时固定在顶部栏下方

3. **侧边栏**：`z-40`
   - 在移动端视图中需要覆盖其他元素
   - 固定定位确保始终可见

4. **内容层**：无特殊 z-index
   - 正常文档流，位于所有固定元素下方
   - 可自由滚动

## 主题和过渡效果

### 主题支持

布局系统支持亮色/暗色主题切换：

- 使用 `next-themes` 管理主题
- 通过 `bg-background` 和 `text-foreground` 应用主题颜色
- `ThemeToggle` 组件提供主题切换功能

### 过渡动画

所有布局变化都应用了平滑过渡效果：

```css
transition-all duration-300
```

这确保了侧边栏折叠/展开、主内容区域调整等操作的视觉连贯性。

## 布局组件的使用方法

要在页面中使用主布局，只需将页面内容包裹在 `MainLayout` 组件中：

```tsx
'use client';

import { MainLayout } from '@/components/layout/main-layout';

export default function MyPage() {
  return (
    <MainLayout>
      {/* 页面内容 */}
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">我的页面</h1>
        <p>页面内容...</p>
      </div>
    </MainLayout>
  );
}
```

`MainLayout` 会自动处理认证检查、侧边栏状态和所有布局组件的渲染。

## 布局优势总结

1. **用户体验**：顶部栏和面包屑导航始终可见，方便导航
2. **视觉一致性**：边框和间距统一，提供清晰的视觉层次
3. **响应式设计**：自动适应不同屏幕尺寸，提供最佳体验
4. **性能优化**：使用 CSS 粘性定位，性能优于 JavaScript 实现
5. **可维护性**：组件化设计，易于扩展和修改
