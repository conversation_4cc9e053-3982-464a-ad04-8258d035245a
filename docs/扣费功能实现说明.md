# 扣费功能实现说明

## 🎯 功能概述

基于充值功能的实现，完整实现了会员卡扣费功能，包括：
- 扣除余额（金额或次数）
- 扣除有效期天数
- 快捷备注功能
- 实时计算显示

## 🔧 技术实现

### 1. API层实现

**端点**：`/api/v1/admin/member-cards/cards/{card_id}/deduct`

**类型定义**：
```typescript
// 扣费请求
export interface DeductRequest {
  member_card_id: number;
  amount: number;
  deduct_validity_days?: number | null;
  notes?: string | null;
}

// 扣费响应
export interface DeductResponse {
  operation_id: number;
  member_card_id: number;
  amount: number;
  balance_before: number;
  balance_after: number;
  created_at: string;
}
```

**API函数**：
```typescript
export async function deductMemberCard(
  id: string,
  data: DeductRequest
): Promise<DeductResponse> {
  const response = await apiClient.post<DeductResponseType>(
    API_ENDPOINTS.MEMBER_CARDS.DEDUCT(id),
    data
  );
  return response.data.data!;
}
```

### 2. Hook层实现

```typescript
export function useDeductMemberCard() {
  return useApiMutation(
    ({ id, data }: { id: string; data: DeductRequest }) =>
      deductMemberCard(id, data),
    {
      successMessage: '扣费成功',
      errorMessage: '扣费失败',
      invalidateQueries: [
        ['member-cards', 'list'],
        ['member-cards', 'by-member'],
        ['member-cards', 'detail'],
        ['member-cards', 'recharge-history'],
        ['member-cards', 'consumption-history'],
      ],
    }
  );
}
```

### 3. UI组件实现

**扣费按钮**：
- 位置：会员卡操作区域，充值按钮旁边
- 样式：outline变体，与充值按钮保持一致
- 状态：只有活跃状态的卡片才能扣费

**扣费对话框**：
- 扣除余额：必填字段，支持金额卡和次数卡
- 扣除有效期：可选字段，实时计算显示缩短后的日期
- 备注：支持手动输入和快捷按钮
- 确认按钮：使用destructive变体，突出危险操作

## 🎨 UI设计特色

### 1. 扣除有效期实时计算
```typescript
// 实时计算扣除后的日期
if (deductValidityDays && Number(deductValidityDays) > 0) {
  if (card.expires_at) {
    const currentExpiry = new Date(card.expires_at);
    const deductedDate = new Date(currentExpiry);
    deductedDate.setDate(deductedDate.getDate() - Number(deductValidityDays));
    
    return (
      <span>
        将缩短至：
        <span className="text-destructive font-medium">
          {format(deductedDate, 'yyyy-MM-dd')}
        </span>
      </span>
    );
  }
}
```

### 2. 快捷备注按钮
提供三个常用的扣费原因：
- **退款**：处理退款相关的扣费
- **固定课额外扣费**：固定课程的额外费用
- **额外补扣**：其他补充扣费

```typescript
<div className="flex gap-2 mt-2">
  <Button variant="outline" size="xs" onClick={() => setNotes('退款')}>
    退款
  </Button>
  <Button variant="outline" size="xs" onClick={() => setNotes('固定课额外扣费')}>
    固定课额外扣费
  </Button>
  <Button variant="outline" size="xs" onClick={() => setNotes('额外补扣')}>
    额外补扣
  </Button>
</div>
```

### 3. 视觉安全设计
- 确认按钮使用 `variant="destructive"` 红色警告样式
- 扣除后的日期使用 `text-destructive` 红色高亮
- 对话框标题明确标注"扣费操作，请谨慎操作"

## 🔄 数据流程

1. **用户点击扣费按钮** → 打开扣费对话框
2. **输入扣费信息** → 实时计算并显示结果
3. **点击快捷备注** → 自动填入常用备注
4. **确认扣费** → 调用API执行扣费
5. **操作完成** → 刷新相关数据，关闭对话框

## 🛡️ 安全考虑

1. **权限控制**：只有活跃状态的卡片才能扣费
2. **数据验证**：扣费金额必须为正数
3. **视觉警告**：使用红色样式突出危险操作
4. **操作记录**：所有扣费操作都会记录到操作历史

## 📊 功能对比

| 功能 | 充值 | 扣费 |
|------|------|------|
| 主要操作 | 增加余额 | 减少余额 |
| 有效期 | 延长天数 | 缩短天数 |
| 按钮样式 | outline | outline |
| 确认按钮 | default | destructive |
| 快捷操作 | 支付方式选择 | 备注快捷按钮 |
| 安全级别 | 普通 | 高危险 |

## ✅ 测试要点

1. **基础扣费**：验证金额卡和次数卡的扣费功能
2. **有效期扣除**：测试有效期缩短的计算和显示
3. **快捷备注**：验证三个快捷按钮的功能
4. **边界情况**：测试永久有效卡的有效期扣除提示
5. **表单重置**：确认操作完成后表单正确重置
6. **权限控制**：验证非活跃卡片无法扣费
