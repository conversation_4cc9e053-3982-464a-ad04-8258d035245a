# UI 设计元素规范

本文档整理了项目中使用的UI设计元素规范，包括色值、字体大小、按钮大小等核心设计元素，以确保整个项目的设计一致性。

## 色值系统

### 主题色

我们使用CSS变量和Tailwind类名来定义和使用主题色：

| 用途       | CSS变量                  | Tailwind类名                              | 说明                     |
| ---------- | ------------------------ | ----------------------------------------- | ------------------------ |
| 主题色     | `--theme-primary`        | `text-primary` `bg-primary`               | 当前选中的主题色         |
| 主题色悬停 | `--theme-primary-hover`  | `text-primary-hover` `bg-primary-hover`   | 主题色的悬停状态         |
| 主题色激活 | `--theme-primary-active` | `text-primary-active` `bg-primary-active` | 主题色的激活状态         |
| 主题前景色 | `--primary-foreground`   | `text-primary-foreground`                 | 在主题色背景上的文字颜色 |

### 预设主题

可以通过添加以下CSS类来切换主题：

| 主题     | CSS类          | 示例             |
| -------- | -------------- | ---------------- |
| 蓝色主题 | `theme-blue`   | 适用于默认主题   |
| 紫色主题 | `theme-purple` | 适用于创意类应用 |
| 青绿主题 | `theme-teal`   | 适用于财务类应用 |
| 灰色主题 | `theme-gray`   | 适用于企业级应用 |

### 语义色

| 用途       | CSS变量                    | Tailwind类名                  | 说明               |
| ---------- | -------------------------- | ----------------------------- | ------------------ |
| 背景色     | `--background`             | `bg-background`               | 页面背景           |
| 前景色     | `--foreground`             | `text-foreground`             | 主要文本颜色       |
| 卡片背景   | `--card`                   | `bg-card`                     | 卡片背景色         |
| 卡片文字   | `--card-foreground`        | `text-card-foreground`        | 卡片文字颜色       |
| 次要色     | `--secondary`              | `bg-secondary`                | 次要按钮、标签等   |
| 次要前景色 | `--secondary-foreground`   | `text-secondary-foreground`   | 次要元素的文字     |
| 弱化背景   | `--muted`                  | `bg-muted`                    | 弱化的背景色       |
| 弱化文字   | `--muted-foreground`       | `text-muted-foreground`       | 弱化的文字颜色     |
| 强调背景   | `--accent`                 | `bg-accent`                   | 强调元素背景       |
| 强调文字   | `--accent-foreground`      | `text-accent-foreground`      | 强调元素文字       |
| 危险色     | `--destructive`            | `bg-destructive`              | 危险操作、错误提示 |
| 危险前景色 | `--destructive-foreground` | `text-destructive-foreground` | 危险元素的文字     |
| 边框色     | `--border`                 | `border-border`               | 边框颜色           |
| 输入框边框 | `--input`                  | `border-input`                | 输入框边框颜色     |
| 环形色     | `--ring`                   | `ring-ring`                   | 焦点环颜色         |

### 图表色

图表专用颜色，用于数据可视化：

| 用途    | CSS变量     | Tailwind类名 |
| ------- | ----------- | ------------ |
| 图表色1 | `--chart-1` | `bg-chart-1` |
| 图表色2 | `--chart-2` | `bg-chart-2` |
| 图表色3 | `--chart-3` | `bg-chart-3` |
| 图表色4 | `--chart-4` | `bg-chart-4` |
| 图表色5 | `--chart-5` | `bg-chart-5` |

## 字体大小

我们使用Tailwind的字体大小类，已在配置中自定义：

| 类名        | 字体大小        | 行高 | 用途                                 |
| ----------- | --------------- | ---- | ------------------------------------ |
| `text-xs`   | 0.75rem (12px)  | 1.4  | 辅助文本、标签、小提示               |
| `text-sm`   | 0.875rem (14px) | 1.4  | **默认文本大小**，表格内容，表单标签 |
| `text-base` | 0.875rem (14px) | 1.4  | 与`text-sm`相同，用于兼容性          |
| `text-lg`   | 1rem (16px)     | 1.4  | 强调文本，小标题                     |
| `text-xl`   | 1.125rem (18px) | 1.4  | 卡片标题，区块标题                   |
| `text-2xl`  | 1.5rem (24px)   | 1.4  | 页面主标题                           |
| `text-3xl`  | 1.875rem (30px) | 1.3  | 大型标题，欢迎页面                   |

## 间距系统

使用Tailwind的间距类，已在配置中自定义：

| 类名            | 大小            | 用途               |
| --------------- | --------------- | ------------------ |
| `p-0.5` `m-0.5` | 0.125rem (2px)  | 极小间距           |
| `p-1` `m-1`     | 0.25rem (4px)   | 非常紧凑的元素间距 |
| `p-1.5` `m-1.5` | 0.375rem (6px)  | 紧凑元素内边距     |
| `p-2` `m-2`     | 0.5rem (8px)    | 常用的紧凑间距     |
| `p-2.5` `m-2.5` | 0.625rem (10px) | 中等紧凑间距       |
| `p-3` `m-3`     | 0.75rem (12px)  | 标准内边距         |
| `p-3.5` `m-3.5` | 0.875rem (14px) | 中等宽松间距       |
| `p-4` `m-4`     | 1rem (16px)     | 宽松间距           |

## 按钮大小

我们定义了多种按钮尺寸以适应不同场景：

| 尺寸 | 类名             | 高度           | 内边距      | 字体大小 | 用途             |
| ---- | ---------------- | -------------- | ----------- | -------- | ---------------- |
| 超小 | `size="xs"`      | 24px (h-6)     | px-2 py-0.5 | text-xs  | 表格内操作按钮   |
| 小型 | `size="sm"`      | 28px (h-7)     | px-2.5 py-1 | text-xs  | 紧凑布局中的按钮 |
| 默认 | `size="default"` | 32px (h-8)     | px-3 py-1.5 | text-sm  | 标准按钮         |
| 大型 | `size="lg"`      | 40px (h-10)    | px-6 py-2   | text-sm  | 强调操作按钮     |
| 图标 | `size="icon"`    | 32px (h-8 w-8) | -           | -        | 仅包含图标的按钮 |

## 圆角

使用CSS变量和Tailwind类定义圆角：

| 类型   | CSS变量                | Tailwind类     | 大小 | 用途         |
| ------ | ---------------------- | -------------- | ---- | ------------ |
| 小圆角 | `--radius` 的 2px 减值 | `rounded-sm`   | 2px  | 小型元素     |
| 中圆角 | `--radius` 的 1px 减值 | `rounded-md`   | 3px  | 按钮、输入框 |
| 大圆角 | `--radius`             | `rounded-lg`   | 4px  | 卡片、模态框 |
| 全圆角 | -                      | `rounded-full` | 50%  | 头像、徽章   |

## 阴影

使用CSS变量和Tailwind类定义阴影：

| 类型     | CSS变量       | Tailwind类  | 用途           |
| -------- | ------------- | ----------- | -------------- |
| 小阴影   | `--shadow-sm` | `shadow-sm` | 轻微提升       |
| 默认阴影 | `--shadow`    | `shadow`    | 卡片、按钮     |
| 中等阴影 | `--shadow-md` | `shadow-md` | 悬浮卡片       |
| 大阴影   | `--shadow-lg` | `shadow-lg` | 模态框、侧边栏 |

## 最佳实践

1. **使用语义化类名**：优先使用 `text-primary` 而非直接使用颜色值如 `text-blue-500`
2. **遵循间距规范**：使用定义好的间距类，避免使用自定义数值
3. **响应式设计**：使用Tailwind的响应式前缀 `sm:`, `md:`, `lg:` 等
4. **暗色模式兼容**：确保组件在暗色模式下正常显示，使用语义化颜色变量
5. **字体大小一致性**：正文使用 `text-sm`，标题使用规范中定义的尺寸
