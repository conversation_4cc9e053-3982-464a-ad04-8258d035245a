# 页面布局结构文档

## 概述

本文档描述了中后台管理系统的页面布局结构，包括各个组件的定位、样式和交互行为。

## 整体布局架构

```
┌─────────────────────────────────────────────────────────────┐
│                    侧边栏 (Sidebar)                          │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              Header (粘性定位)                           │ │
│  │  搜索框 | 主题切换 | 用户菜单                              │ │
│  ├─────────────────────────────────────────────────────────┤ │
│  │           面包屑导航 (粘性定位)                           │ │
│  │  首页 > 用户管理                                         │ │
│  ├─────────────────────────────────────────────────────────┤ │
│  │                                                         │ │
│  │                页面内容区域                              │ │
│  │                                                         │ │
│  │                                                         │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 组件详细说明

### 1. 侧边栏 (Sidebar)

**位置**: 固定在页面左侧
**宽度**:

- 展开状态: `w-64` (256px)
- 收起状态: `w-16` (64px)

**特性**:

- 固定定位 (`fixed`)
- 响应式设计，移动端可隐藏
- 支持展开/收起切换
- 包含导航菜单和系统logo

### 2. 主内容区域

**位置**: 侧边栏右侧
**左边距**:

- 侧边栏展开时: `md:ml-64`
- 侧边栏收起时: `md:ml-16`

**结构**:

```tsx
<div className={cn(
  'flex flex-col transition-all duration-300',
  'md:ml-0',
  sidebarCollapsed ? 'md:ml-16' : 'md:ml-64'
)}>
```

### 3. Header 组件

**位置**: 主内容区域顶部
**定位**: `sticky top-0 z-30`
**高度**: `h-16` (64px)

**样式类**:

```tsx
className =
  'sticky top-0 z-30 flex h-16 items-center justify-between border-b bg-background px-4 transition-all duration-300';
```

**功能区域**:

- **左侧**: 移动端菜单按钮预留空间
- **右侧**: 搜索框、主题切换、用户下拉菜单

**关键修复**:

- ✅ 移除了左边距，让下划线完全延伸到左侧
- ✅ 保持粘性定位，提供良好的用户体验

### 4. 面包屑导航

**位置**: Header 下方
**定位**: `sticky top-16 z-20`
**样式**: 带下划线分隔

**结构**:

```tsx
<div className="sticky top-16 z-20 border-b bg-background transition-all duration-300">
  <div className="px-4 py-3">
    <Breadcrumb />
  </div>
</div>
```

**特性**:

- 粘性定位在 Header 下方 (top-16)
- 下划线延伸到页面边缘
- 内容区域有适当的内边距

### 5. 页面内容区域

**位置**: 面包屑导航下方
**内边距**: `p-4`
**布局**: `flex-1` 占据剩余空间

## 响应式设计

### 桌面端 (md及以上)

- 侧边栏始终可见
- 主内容区域有左边距适配侧边栏宽度
- Header 和面包屑导航完全延伸

### 移动端 (md以下)

- 侧边栏可隐藏/显示
- 主内容区域无左边距 (`md:ml-0`)
- Header 左侧预留菜单按钮空间

## 粘性定位层级

1. **Header**: `z-30, top-0` - 最高优先级
2. **面包屑导航**: `z-20, top-16` - 在 Header 下方
3. **其他浮动元素**: `z-10` 及以下

## 关键样式类

### 过渡动画

所有布局组件都使用统一的过渡动画：

```css
transition-all duration-300
```

### 边框和背景

- 下划线: `border-b`
- 背景色: `bg-background`
- 前景色: `text-foreground`

## 布局优势

1. **用户体验**: Header 和面包屑导航始终可见，方便导航
2. **视觉一致性**: 下划线完全延伸，视觉效果统一
3. **响应式**: 适配不同屏幕尺寸
4. **性能**: 使用 CSS 粘性定位，性能优于 JavaScript 实现
5. **可维护性**: 清晰的组件分离和样式管理

## 注意事项

- Header 不应添加左边距，以确保下划线延伸到左侧
- 面包屑导航的 `top-16` 值应与 Header 高度 `h-16` 保持一致
- z-index 层级需要合理规划，避免遮挡问题
- 所有粘性定位元素都应设置背景色，避免内容透过
