# 中后台管理系统开发文档

## 🏗️ 核心项目目录结构

```
aug-admin/
├── src/
│   ├── app/                    # Next.js App Router页面
│   │   ├── (auth)/login/      # 登录页面
│   │   ├── dashboard/         # 仪表盘
│   │   ├── users/             # 用户管理
│   │   ├── members/           # 会员管理
│   │   └── layout.tsx         # 根布局
│   ├── components/            # UI组件
│   │   ├── ui/               # shadcn/ui基础组件
│   │   ├── layout/           # 布局组件(sidebar/header/breadcrumb)
│   │   ├── forms/            # 表单组件
│   │   ├── tables/           # 表格组件
│   │   └── providers/        # 上下文提供者
│   ├── lib/                  # 核心库
│   │   ├── api/              # API交互层
│   │   ├── auth/             # 认证模块
│   │   ├── stores/           # 状态管理(Zustand)
│   │   └── utils/            # 工具函数
│   ├── hooks/                # 自定义hooks
│   ├── types/                # TypeScript类型定义
│   ├── constants/            # 常量定义(API端点/路由/消息)
│   └── config/               # 配置文件(导航/站点/环境变量)
├── middleware.ts             # 路由保护中间件
├── tailwind.config.ts        # Tailwind CSS配置
├── tsconfig.json            # TypeScript配置
└── package.json             # 项目依赖
```

## 🛠️ 技术栈

- **框架**: Next.js 14 App Router + TypeScript
- **UI**: Tailwind CSS + shadcn/ui + lucide-react
- **状态管理**: TanStack Query + Zustand
- **表单**: React Hook Form + Zod
- **表格**: TanStack Table
- **HTTP**: Axios
- **主题**: next-themes

## 🎨 UI开发规范

### 1. 设计系统
- 基于 shadcn/ui 组件库，统一设计语言
- 使用 Tailwind CSS 原子化类，禁止自定义 CSS
- 支持亮色/暗色/系统主题切换
- 响应式设计优先(mobile-first)

具体：
- 样式  100 % Tailwind 原子类   
目的:与 shadcn/ui 同构，AI、开发同事都能一眼看懂
- 主题	强制使用 next-themes <ThemeProvider>	
目的:保证所有自定义组件自动继承暗黑/亮色

### 2. 布局规范
```tsx
// 页面布局结构
<MainLayout>
  {/* 页面内容 */}
</MainLayout>

// 布局层级
- 侧边栏: fixed, w-64/w-16, z-40
- 顶部栏: sticky top-0, h-16, z-30  
- 面包屑: sticky top-16, z-20
- 内容区: p-4, 自适应宽度
```

### 3. 组件开发规范
- 组件文件使用 PascalCase 命名
- 优先使用函数式组件和 hooks
- 组件 props 使用 TypeScript 接口定义
- 保持组件单一职责原则

### 4. 表格开发规范
```tsx
// 使用通用DataTable组件
<DataTable
  columns={columns}
  data={data}
  title="数据列表"
  searchKey="name"
  onAdd={handleAdd}
  isLoading={isLoading}
/>

// 列定义函数
export function createXxxColumns({ onEdit, onDelete }) {
  return [
    { accessorKey: 'name', header: '名称' },
    { id: 'actions', header: '操作', cell: ActionCell }
  ];
}
```

### 5. 表单开发规范
```tsx
// 使用React Hook Form + Zod
const schema = z.object({
  name: z.string().min(2, '名称至少2个字符'),
});

const { register, handleSubmit, formState: { errors } } = useForm({
  resolver: zodResolver(schema),
});

// 表单容器使用Card
<Card>
  <CardHeader>
    <CardTitle>表单标题</CardTitle>
  </CardHeader>
  <CardContent>
    <form onSubmit={handleSubmit(onSubmit)}>
      {/* 表单字段 */}
    </form>
  </CardContent>
</Card>
```

## 🔄 数据流架构

```
页面组件 → 自定义hooks → API层 → 后端
    ↓
状态管理: 认证状态(Zustand) + 服务端状态(TanStack Query)
    ↓
类型安全: 全链路TypeScript类型定义
```

### API交互规范
```tsx
// 自定义hooks封装API调用
export function useUserList() {
  return useApiQuery(['users', 'list'], getUserList);
}

export function useCreateUser() {
  return useApiMutation(createUser, {
    successMessage: '创建成功',
    invalidateQueries: [['users', 'list']],
  });
}

// 页面中使用
const { data, isLoading } = useUserList();
const createMutation = useCreateUser();
```

## 🚀 快速开发流程

### 1. 环境准备
```bash
npm install
npm run dev  # 启动开发服务器(端口3005)
```

### 2. 新增功能模块
1. 在 `src/app` 下创建路由页面
2. 在 `src/components` 下添加相关组件  
3. 在 `src/lib/api` 下添加API函数
4. 在 `src/hooks` 下创建自定义hooks
5. 在 `src/types` 下定义类型
6. 在 `src/config/navigation.ts` 中添加导航项

### 3. 开发最佳实践
- 使用 TypeScript 严格模式
- 遵循 ESLint + Prettier 代码规范
- 合理使用 React.memo 和 useMemo 优化性能
- 统一的错误处理和用户提示
- 适当的加载状态和骨架屏

## 🔐 认证系统

- JWT Token 自动管理和刷新
- 路由保护中间件
- Zustand 状态持久化
- 安全的登录/登出流程

## 📝 开发示例

### 新增页面
```tsx
'use client';

import { MainLayout } from '@/components/layout/main-layout';

export default function MyPage() {
  return (
    <MainLayout>
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">我的页面</h1>
        {/* 页面内容 */}
      </div>
    </MainLayout>
  );
}
```

### API集成
```tsx
// 1. 添加API端点 (src/constants/api.ts)
MY_FEATURE: {
  LIST: '/api/v1/my-feature/',
  CREATE: '/api/v1/my-feature/',
}

// 2. 创建API函数 (src/lib/api/my-feature.ts)
export async function getMyFeatureList() {
  const response = await apiClient.get(API_ENDPOINTS.MY_FEATURE.LIST);
  return response.data;
}

// 3. 创建hooks (src/hooks/use-my-feature.ts)
export function useMyFeatureList() {
  return useApiQuery(['my-feature', 'list'], getMyFeatureList);
}
```

## 🎯 项目特色

- **现代化技术栈**: 基于最新的 React 生态系统
- **类型安全**: 全链路 TypeScript 类型定义
- **开发体验**: 完善的开发工具链和热重载
- **生产就绪**: 完整的认证、错误处理、性能优化
- **可扩展性**: 模块化设计，易于扩展新功能
- **响应式设计**: 完美适配桌面端和移动端

---

这个项目是一个高质量的中后台管理系统脚手架，代码结构清晰，功能完整，可以作为企业级项目的基础框架使用。
