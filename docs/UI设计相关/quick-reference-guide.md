# UI设计系统快速参考指南

## 🎨 颜色系统速查

### 语义化颜色类名
```tsx
// 状态色 - 优先使用
text-success      // 成功状态 (绿色)
text-warning      // 警告状态 (橙色) 
text-info         // 信息状态 (蓝色)
text-destructive  // 错误状态 (红色)

// 基础色
text-primary      // 主题色
text-secondary    // 次要色
text-muted-foreground  // 次要文字
text-foreground   // 主要文字
```

### 背景色使用
```tsx
// 状态背景
bg-success/10 border-success/30    // 成功提示背景
bg-warning/10 border-warning/30    // 警告提示背景
bg-info/10 border-info/30          // 信息提示背景
bg-destructive/10 border-destructive/30  // 错误提示背景
```

## 🔘 组件快速使用

### Button 组件
```tsx
// 基础变体
<Button variant="default">默认按钮</Button>
<Button variant="secondary">次要按钮</Button>
<Button variant="outline">边框按钮</Button>
<Button variant="ghost">幽灵按钮</Button>

// 状态变体 (新增)
<Button variant="success">成功按钮</Button>
<Button variant="warning">警告按钮</Button>
<Button variant="info">信息按钮</Button>
<Button variant="destructive">危险按钮</Button>

// 尺寸
<Button size="xs">超小</Button>
<Button size="sm">小</Button>
<Button size="default">默认</Button>
<Button size="lg">大</Button>
```

### Badge 组件
```tsx
// 状态徽章
<Badge variant="success">在线</Badge>
<Badge variant="warning">待处理</Badge>
<Badge variant="info">维护中</Badge>
<Badge variant="destructive">错误</Badge>
<Badge variant="secondary">离线</Badge>
```

### Alert 组件
```tsx
import { CheckCircle, AlertTriangle, Info, XCircle } from 'lucide-react';

// 状态提示
<Alert variant="success">
  <CheckCircle className="h-4 w-4" />
  <AlertTitle>成功</AlertTitle>
  <AlertDescription>操作已完成</AlertDescription>
</Alert>

<Alert variant="warning">
  <AlertTriangle className="h-4 w-4" />
  <AlertTitle>警告</AlertTitle>
  <AlertDescription>请注意检查</AlertDescription>
</Alert>
```

## 📊 数据状态组件

### DataStateWrapper (推荐)
```tsx
import { DataStateWrapper } from '@/components/ui/loading-states';

<DataStateWrapper
  isLoading={isLoading}
  isEmpty={data.length === 0}
  isError={hasError}
  emptyTitle="还没有数据"
  emptyDescription="点击按钮添加第一条记录"
  emptyActionLabel="添加数据"
  onEmptyAction={handleAdd}
  onRetry={refetch}
>
  <YourDataComponent data={data} />
</DataStateWrapper>
```

### 独立状态组件
```tsx
import { 
  LoadingSpinner, 
  EmptyState, 
  ErrorState 
} from '@/components/ui/loading-states';

// 加载状态
<LoadingSpinner size="md" text="加载中..." />

// 空状态
<EmptyState 
  title="暂无数据"
  description="还没有任何记录"
  action={{
    label: "添加数据",
    onClick: handleAdd
  }}
/>

// 错误状态
<ErrorState 
  title="加载失败"
  description="网络连接异常"
  onRetry={handleRetry}
/>
```

## 📝 表单验证规范

### 输入框错误状态
```tsx
// React Hook Form 示例
<Input
  {...register('field')}
  className={errors.field ? 'border-destructive' : ''}
/>
{errors.field && (
  <p className="text-destructive text-sm">{errors.field.message}</p>
)}
```

### 表单状态提示
```tsx
// 成功提示
<div className="p-3 bg-success/10 border border-success/30 rounded-md">
  <p className="text-success font-medium">✓ 保存成功</p>
</div>

// 错误提示
<div className="p-3 bg-destructive/10 border border-destructive/30 rounded-md">
  <p className="text-destructive font-medium">✗ 保存失败</p>
</div>
```

## 🗂️ 常用模式

### 用户状态显示
```tsx
// 用户在线状态
<Badge variant={user.isActive ? 'success' : 'secondary'}>
  {user.isActive ? '在线' : '离线'}
</Badge>

// 用户角色显示
const roleVariants = {
  super_admin: 'destructive',
  admin: 'info', 
  agent: 'warning',
  sale: 'secondary'
};

<Badge variant={roleVariants[user.role]}>
  {roleLabels[user.role]}
</Badge>
```

### 操作按钮组合
```tsx
// 表格操作按钮
<div className="flex items-center space-x-2">
  <Button variant="ghost" size="xs">
    <Edit className="h-3 w-3" />
  </Button>
  <Button 
    variant="ghost" 
    size="xs"
    className="text-destructive hover:text-destructive"
  >
    <Trash2 className="h-3 w-3" />
  </Button>
</div>
```

### 确认对话框
```tsx
<AlertDialogAction
  variant="destructive"  // 危险操作使用 destructive
  onClick={handleDelete}
>
  确认删除
</AlertDialogAction>
```

## ⚠️ 注意事项

### ❌ 避免使用
```tsx
// 硬编码颜色
className="text-red-500 bg-green-100"
className="border-blue-600"

// 内联样式颜色
style={{ color: '#ff0000' }}
```

### ✅ 推荐使用
```tsx
// 语义化类名
className="text-destructive bg-success/10"
className="border-primary"

// CSS变量
style={{ color: 'hsl(var(--destructive))' }}
```

## 🔍 调试技巧

### 检查颜色变量
```tsx
// 在浏览器控制台查看CSS变量
getComputedStyle(document.documentElement).getPropertyValue('--success')
```

### 主题切换测试
```tsx
// 确保组件在深色模式下正常显示
document.documentElement.classList.toggle('dark')
```

## 📚 相关资源

- **完整规范**：`/examples/ui-design-system` 页面
- **组件文档**：`docs/ui-design-specification.md`
- **实施计划**：`docs/ui-implementation-plan.md`
- **项目总结**：`docs/ui-system-implementation-summary.md`

---

💡 **提示**：遇到样式问题时，优先检查是否使用了语义化颜色类名，避免硬编码颜色值。
