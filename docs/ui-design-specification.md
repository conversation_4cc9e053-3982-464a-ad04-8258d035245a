# UI 设计规范文档

## 📋 概述

本文档定义了项目的完整UI设计规范，确保整个应用的视觉一致性和用户体验。项目基于 **Next.js + Tailwind CSS + shadcn/ui**，针对中国用户习惯进行了紧凑型定制。

## 🎨 色彩系统

### 主题色系统

项目支持四套主题色，通过CSS变量实现动态切换：

| 主题 | 主色调 | 使用场景 |
|------|--------|----------|
| 蓝色 (Blue) | `hsl(210, 100%, 62%)` | 默认主题，适合商务场景 |
| 紫色 (Purple) | `hsl(250, 100%, 60%)` | 创意、设计类应用 |
| 青绿色 (Teal) | `hsl(175, 80%, 40%)` | 健康、环保类应用 |
| 灰色 (Gray) | `hsl(220, 10%, 40%)` | 简约、专业场景 |

### 语义化颜色

| 用途 | CSS变量 | Tailwind类名 | 说明 |
|------|---------|--------------|------|
| 页面背景 | `--background` | `bg-background` | 整个页面的背景色 |
| 主要文本 | `--foreground` | `text-foreground` | 主要文本颜色 |
| 卡片背景 | `--card` | `bg-card` | 卡片、面板等组件背景 |
| 次要文本 | `--muted-foreground` | `text-muted-foreground` | 次要文本、说明文字 |
| 边框 | `--border` | `border-border` | 边框颜色 |
| 输入框 | `--input` | `bg-input` | 输入框背景色 |

### 状态色系统

| 状态 | CSS变量 | Tailwind类名 | 使用场景 |
|------|---------|--------------|----------|
| 成功 | `--success` | `bg-success` `text-success` | 成功提示、完成状态 |
| 警告 | `--warning` | `bg-warning` `text-warning` | 警告信息、注意事项 |
| 信息 | `--info` | `bg-info` `text-info` | 一般信息提示 |
| 危险 | `--destructive` | `bg-destructive` `text-destructive` | 错误、删除等危险操作 |

### 图表色系统

专为数据可视化设计的色彩方案：

| 图表色 | 浅色模式 | 暗色模式 | Tailwind类名 |
|--------|----------|----------|--------------|
| 图表色1 | `hsl(210, 100%, 56%)` | `hsl(210, 100%, 70%)` | `bg-chart-1` |
| 图表色2 | `hsl(142, 76%, 36%)` | `hsl(142, 70%, 50%)` | `bg-chart-2` |
| 图表色3 | `hsl(38, 92%, 50%)` | `hsl(38, 95%, 65%)` | `bg-chart-3` |
| 图表色4 | `hsl(250, 100%, 60%)` | `hsl(250, 100%, 75%)` | `bg-chart-4` |
| 图表色5 | `hsl(175, 80%, 40%)` | `hsl(175, 80%, 55%)` | `bg-chart-5` |

## 📝 字体系统

### 字体大小层级

针对中文阅读习惯优化的字体大小系统：

| 级别 | 大小 | 行高 | Tailwind类名 | 使用场景 |
|------|------|------|--------------|----------|
| XS | 12px | 1.4 | `text-xs` | 辅助信息、标签 |
| SM | 14px | 1.4 | `text-sm` | 正文内容（默认） |
| Base | 14px | 1.4 | `text-base` | 正文内容 |
| LG | 16px | 1.4 | `text-lg` | 小标题 |
| XL | 18px | 1.4 | `text-xl` | 中标题 |
| 2XL | 24px | 1.4 | `text-2xl` | 大标题 |
| 3XL | 30px | 1.3 | `text-3xl` | 主标题 |

### 字重规范

| 字重 | Tailwind类名 | 使用场景 |
|------|--------------|----------|
| 正常 | `font-normal` | 正文内容 |
| 中等 | `font-medium` | 小标题、重要信息 |
| 半粗 | `font-semibold` | 标题、按钮文字 |
| 粗体 | `font-bold` | 强调文字 |

## 🔘 按钮系统

### 按钮尺寸

| 尺寸 | 高度 | 内边距 | 字体 | Tailwind类名 | 使用场景 |
|------|------|--------|------|--------------|----------|
| XS | 24px | `px-2 py-0.5` | `text-xs` | `size="xs"` | 表格内操作 |
| SM | 28px | `px-2.5 py-1` | `text-xs` | `size="sm"` | 次要操作 |
| Default | 32px | `px-3 py-1.5` | `text-sm` | `size="default"` | 主要操作 |
| LG | 40px | `px-6 py-2` | `text-sm` | `size="lg"` | 重要操作 |
| Icon | 32x32px | - | - | `size="icon"` | 图标按钮 |

### 按钮变体

| 变体 | 外观 | 使用场景 |
|------|------|----------|
| Default | 主题色背景 | 主要操作按钮 |
| Secondary | 次要色背景 | 次要操作按钮 |
| Outline | 边框样式 | 取消、返回等操作 |
| Ghost | 透明背景 | 文字链接式操作 |
| Link | 下划线样式 | 链接跳转 |
| Destructive | 危险色背景 | 删除、危险操作 |

## 📏 间距系统

### 基础间距

| 名称 | 大小 | Tailwind类名 | 使用场景 |
|------|------|--------------|----------|
| 0.5 | 2px | `p-0.5` `m-0.5` | 极小间距 |
| 1 | 4px | `p-1` `m-1` | 很小间距 |
| 1.5 | 6px | `p-1.5` `m-1.5` | 小间距 |
| 2 | 8px | `p-2` `m-2` | 常用小间距 |
| 2.5 | 10px | `p-2.5` `m-2.5` | 中小间距 |
| 3 | 12px | `p-3` `m-3` | 常用间距 |
| 3.5 | 14px | `p-3.5` `m-3.5` | 中等间距 |
| 4 | 16px | `p-4` `m-4` | 大间距 |

### 组件内边距规范

| 组件 | 内边距 | CSS变量 |
|------|--------|---------|
| 卡片 | 12px | `var(--card-padding)` |
| 输入框 | `6px 8px` | `var(--input-padding-y)` `var(--input-padding-x)` |
| 按钮 | `6px 12px` | `var(--button-padding-y)` `var(--button-padding-x)` |

## 🔲 圆角和阴影

### 圆角系统

| 级别 | 大小 | Tailwind类名 | 使用场景 |
|------|------|--------------|----------|
| SM | 2px | `rounded-sm` | 小元素 |
| MD | 3px | `rounded-md` | 常用组件 |
| LG | 4px | `rounded-lg` | 卡片、面板 |
| Full | 50% | `rounded-full` | 圆形元素 |

### 阴影系统

| 级别 | 效果 | Tailwind类名 | 使用场景 |
|------|------|--------------|----------|
| SM | 轻微阴影 | `shadow-sm` | 输入框、按钮 |
| Default | 标准阴影 | `shadow` | 卡片 |
| MD | 中等阴影 | `shadow-md` | 弹窗、下拉菜单 |
| LG | 较强阴影 | `shadow-lg` | 模态框、侧边栏 |

## 📱 响应式断点

| 断点 | 屏幕宽度 | Tailwind前缀 | 使用场景 |
|------|----------|--------------|----------|
| SM | ≥640px | `sm:` | 平板竖屏 |
| MD | ≥768px | `md:` | 平板横屏 |
| LG | ≥1024px | `lg:` | 桌面端 |
| XL | ≥1280px | `xl:` | 大屏桌面 |
| 2XL | ≥1536px | `2xl:` | 超大屏 |

## 🌙 深色模式

### 核心原则

1. **使用语义化变量**：优先使用 `bg-card`、`text-foreground` 等语义化类名
2. **避免硬编码颜色**：不使用 `bg-white`、`text-black` 等固定颜色
3. **保持对比度**：确保文本和背景有足够的对比度
4. **状态色适配**：状态色在深色模式下自动调整亮度

### 最佳实践

```jsx
// ✅ 正确：使用语义化颜色
<div className="bg-card text-card-foreground">
  <p className="text-foreground">主要内容</p>
  <p className="text-muted-foreground">次要内容</p>
</div>

// ❌ 错误：硬编码颜色
<div className="bg-white text-black">
  <p className="text-black">主要内容</p>
  <p className="text-gray-500">次要内容</p>
</div>
```

## 🎯 使用指南

### 开发规范

1. **优先使用语义化类名**：`text-primary` 而非 `text-blue-500`
2. **状态色使用**：用 `text-success`、`text-warning` 等表示状态
3. **遵循间距规范**：使用定义好的间距类
4. **响应式设计**：使用 `sm:`、`md:`、`lg:` 等前缀
5. **暗色模式兼容**：确保所有组件在暗色模式下正常显示

### 状态色使用示例

```jsx
// 成功状态
<div className="bg-success/10 border border-success/30 text-success">
  操作成功
</div>

// 警告状态  
<div className="bg-warning/10 border border-warning/30 text-warning">
  注意事项
</div>

// 错误状态
<div className="bg-destructive/10 border border-destructive/30 text-destructive">
  操作失败
</div>
```

## 🔧 技术实现

### CSS变量配置

所有颜色通过CSS变量定义在 `src/app/globals.css` 中，支持主题切换和深色模式。

### Tailwind配置

在 `tailwind.config.ts` 中扩展了字体、颜色、间距等配置，确保与设计系统一致。

### 组件库

基于 shadcn/ui 进行定制，保持组件的可访问性和一致性。

---

**更新日期**：2025-07-24  
**版本**：v1.0  
**维护者**：开发团队
