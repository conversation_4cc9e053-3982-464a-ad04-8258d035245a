{"openapi": "3.1.0", "info": {"title": "课程预订系统API", "description": "\n## 🎯 系统概述\n\n这是一个现代化的课程预订系统后端API，基于FastAPI + SQLModel + PostgreSQL构建。\n系统采用多租户架构，支持不同机构独立管理用户和会员数据。\n\n## 🔐 认证机制\n\n系统使用JWT（JSON Web Token）进行身份认证：\n\n1. **获取Token**: 通过 `/auth/login` 接口登录获取access_token\n2. **使用Token**: 在请求头中添加 `Authorization: Bearer <access_token>`\n3. **Token刷新**: 使用refresh_token通过 `/auth/refresh` 接口刷新access_token\n\n## 📊 响应格式\n\n所有API响应都遵循统一的格式：\n\n\n### 成功响应\n\n```json\n{\n  \"success\": true,\n  \"message\": \"操作成功\",\n  \"http_code\": 200,\n  \"business_code\": \"SUCCESS\",\n  \"data\": { ... },\n  \"timestamp\": \"2024-01-01T00:00:00Z\"\n}\n```\n\n### 不分页列表响应\n\n```json\n{\n  \"success\": true,\n  \"message\": \"获取列表成功\",\n  \"http_code\": 200,\n  \"business_code\": \"SUCCESS\",\n  \"data\": [ ... ],\n  \"total\": 10,\n  \"timestamp\": \"2024-01-01T00:00:00Z\"\n}\n```\n\n### 分页列表响应\n```json\n{\n  \"success\": true,\n  \"message\": \"获取数据成功\",\n  \"http_code\": 200,\n  \"business_code\": \"SUCCESS\",\n  \"data\": [...],\n  \"total\": 100,\n  \"page\": 1,\n  \"size\": 20,\n  \"pages\": 5,\n  \"timestamp\": \"2024-01-01T00:00:00Z\"\n}\n```\n\n### 错误响应\n\n```json\n{\n  \"success\": false,\n  \"message\": \"错误描述\",\n  \"http_code\": 400,\n  \"business_code\": \"SPECIFIC_ERROR_CODE\",\n  \"level\": \"error\",\n  \"details\": { ... },\n  \"timestamp\": \"2024-01-01T00:00:00Z\"\n}\n```\n\n## 🏷️ 错误码说明\n\n| 错误码 | 说明 | HTTP状态码 |\n|--------|------|------------|\n| BUSINESS_ERROR | 通用业务错误 | 400 |\n| VALIDATION_ERROR | 数据验证失败 | 422 |\n| AUTHENTICATION_FAILED | 认证失败 | 401 |\n| PERMISSION_DENIED | 权限不足 | 403 |\n\n## 📝 开发指南\n\n1. **分页查询**: 使用 `skip` 和 `limit` 参数进行分页\n2. **数据验证**: 所有输入数据都会进行严格的格式验证\n3. **多租户**: 大部分接口都会自动根据用户的租户ID进行数据隔离\n4. **幂等性**: 支持幂等操作的接口会在文档中特别说明\n\n## 🔧 环境配置\n\n使用PostgreSQL数据库，需要配置相应的环境变量\n\n## 📞 技术支持\n\n如有问题，请联系开发团队或查看项目文档。\n", "version": "1.0.0"}, "paths": {"/": {"get": {"summary": "Root", "description": "根路径", "operationId": "root__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/health": {"get": {"summary": "Health Check", "description": "健康检查端点", "operationId": "health_check_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/admin/users/": {"post": {"tags": ["管理端-用户管理"], "summary": "Create User", "description": "创建用户", "operationId": "create_user_api_v1_admin_users__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_UserRead_"}}}}}}, "get": {"tags": ["管理端-用户管理"], "summary": "Get Users", "description": "获取用户列表", "operationId": "get_users_api_v1_admin_users__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "跳过记录数", "default": 0, "title": "<PERSON><PERSON>"}, "description": "跳过记录数"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "description": "返回记录数", "default": 100, "title": "Limit"}, "description": "返回记录数"}, {"name": "role", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/UserRole"}, {"type": "null"}], "description": "按角色筛选", "title": "Role"}, "description": "按角色筛选"}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/UserStatus"}, {"type": "null"}], "description": "按状态筛选", "title": "Status"}, "description": "按状态筛选"}, {"name": "search", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "搜索关键词", "title": "Search"}, "description": "搜索关键词"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_UserRead_"}}}}}}}, "/api/v1/admin/users/{user_id}": {"get": {"tags": ["管理端-用户管理"], "summary": "Get User", "description": "获取用户详情", "operationId": "get_user_api_v1_admin_users__user_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_UserRead_"}}}}}}}, "/api/v1/admin/users/{user_id}/update": {"post": {"tags": ["管理端-用户管理"], "summary": "Update User", "description": "更新用户信息", "operationId": "update_user_api_v1_admin_users__user_id__update_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_UserRead_"}}}}}}}, "/api/v1/admin/users/{user_id}/change-password": {"post": {"tags": ["管理端-用户管理"], "summary": "Change Password", "description": "修改密码", "operationId": "change_password_api_v1_admin_users__user_id__change_password_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PasswordChange"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}}}}, "/api/v1/admin/users/{user_id}/reset-password": {"post": {"tags": ["管理端-用户管理"], "summary": "Reset Password", "description": "重置密码（管理员操作）", "operationId": "reset_password_api_v1_admin_users__user_id__reset_password_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}, {"name": "new_password", "in": "query", "required": true, "schema": {"type": "string", "title": "New Password"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}}}}, "/api/v1/admin/users/{user_id}/activate": {"post": {"tags": ["管理端-用户管理"], "summary": "Activate User", "description": "激活用户", "operationId": "activate_user_api_v1_admin_users__user_id__activate_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_UserRead_"}}}}}}}, "/api/v1/admin/users/{user_id}/deactivate": {"post": {"tags": ["管理端-用户管理"], "summary": "Deactivate User", "description": "停用用户", "operationId": "deactivate_user_api_v1_admin_users__user_id__deactivate_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_UserRead_"}}}}}}}, "/api/v1/admin/users/{user_id}/lock": {"post": {"tags": ["管理端-用户管理"], "summary": "Lock User", "description": "锁定用户", "operationId": "lock_user_api_v1_admin_users__user_id__lock_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_UserRead_"}}}}}}}, "/api/v1/admin/users/{user_id}/delete": {"post": {"tags": ["管理端-用户管理"], "summary": "Delete User", "description": "删除用户", "operationId": "delete_user_api_v1_admin_users__user_id__delete_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}}}}, "/api/v1/admin/users/salesman/list": {"get": {"tags": ["管理端-用户管理"], "summary": "Get Salesmen", "description": "获取销售人员列表（角色为AGENT，状态为ACTIVE的用户）", "operationId": "get_salesmen_api_v1_admin_users_salesman_list_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_UserRead_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/members/": {"post": {"tags": ["管理端-会员管理"], "summary": "创建会员", "description": "创建新会员账户\n    \n    **可能的错误码：**\n    - `MEMBER_PHONE_EXISTS`: 手机号已存在\n    - `MEMBER_EMAIL_EXISTS`: 邮箱已存在\n    - `VALIDATION_ERROR`: 数据验证失败", "operationId": "create_member_api_v1_admin_members__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberRead_"}}}}}}, "get": {"tags": ["管理端-会员管理"], "summary": "获取会员列表", "description": "分页获取会员列表\n    \n    **分页参数：**\n    - `page`: 页码，从1开始（默认1）\n    - `size`: 每页大小，默认20，最大100\n    \n    **筛选参数：**\n    - `member_type`: 会员类型\n    - `member_status`: 会员状态\n    - `agent_id`: 代理ID\n    - `search_keyword`: 搜索关键词", "operationId": "get_members_api_v1_admin_members__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/MemberType"}, {"type": "null"}], "title": "Member Type"}}, {"name": "member_status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/MemberStatus"}, {"type": "null"}], "title": "Member Status"}}, {"name": "agent_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Agent Id"}}, {"name": "search_keyword", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search Keyword"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "default": 1, "title": "Page"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "default": 20, "title": "Size"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageResponse_MemberRead_"}}}}}}}, "/api/v1/admin/members/count": {"get": {"tags": ["管理端-会员管理"], "summary": "Count Members", "description": "统计会员数量", "operationId": "count_members_api_v1_admin_members_count_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/MemberType"}, {"type": "null"}], "title": "Member Type"}}, {"name": "member_status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/MemberStatus"}, {"type": "null"}], "title": "Member Status"}}, {"name": "agent_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Agent Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_int_"}}}}}}}, "/api/v1/admin/members/{member_id}": {"get": {"tags": ["管理端-会员管理"], "summary": "Get Member", "description": "获取会员详情", "operationId": "get_member_api_v1_admin_members__member_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Member Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberRead_"}}}}}}}, "/api/v1/admin/members/{member_id}/update": {"post": {"tags": ["管理端-会员管理"], "summary": "Update Member", "description": "更新会员信息", "operationId": "update_member_api_v1_admin_members__member_id__update_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Member Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberRead_"}}}}}}}, "/api/v1/admin/members/{member_id}/update-status": {"post": {"tags": ["管理端-会员管理"], "summary": "Update Member Status", "description": "更新会员状态", "operationId": "update_member_status_api_v1_admin_members__member_id__update_status_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Member Id"}}, {"name": "status", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/MemberStatus"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberRead_"}}}}}}}, "/api/v1/admin/members/{member_id}/update-stats": {"post": {"tags": ["管理端-会员管理"], "summary": "Update Member Stats", "description": "更新会员统计信息", "operationId": "update_member_stats_api_v1_admin_members__member_id__update_stats_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Member Id"}}, {"name": "class_completed", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Class Completed"}}, {"name": "class_cancelled", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Class Cancelled"}}, {"name": "class_no_show", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Class No Show"}}, {"name": "amount_spent", "in": "query", "required": false, "schema": {"type": "number", "default": 0.0, "title": "Amount Spent"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberRead_"}}}}}}}, "/api/v1/admin/members/{member_id}/delete": {"post": {"tags": ["管理端-会员管理"], "summary": "Delete Member", "description": "删除会员", "operationId": "delete_member_api_v1_admin_members__member_id__delete_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Member Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}}}}, "/api/v1/admin/members/{member_id}/deactivate": {"post": {"tags": ["管理端-会员管理"], "summary": "Deactivate Member", "description": "停用会员", "operationId": "deactivate_member_api_v1_admin_members__member_id__deactivate_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Member Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}}}}, "/api/v1/admin/members/{member_id}/activate": {"post": {"tags": ["管理端-会员管理"], "summary": "Activate Member", "description": "激活会员", "operationId": "activate_member_api_v1_admin_members__member_id__activate_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Member Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}}}}, "/api/v1/admin/teachers/statistics": {"get": {"tags": ["管理端-教师管理"], "summary": "获取教师统计信息", "description": "获取教师数量统计和分布信息", "operationId": "get_teacher_statistics_api_v1_admin_teachers_statistics_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/teachers/priority": {"get": {"tags": ["管理端-教师管理"], "summary": "按优先级获取教师", "description": "按优先级排序获取激活状态的教师列表", "operationId": "get_teachers_by_priority_api_v1_admin_teachers_priority_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 50, "minimum": 1, "description": "返回数量限制", "default": 10, "title": "Limit"}, "description": "返回数量限制"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_TeacherList_"}}}}}}}, "/api/v1/admin/teachers/available": {"get": {"tags": ["管理端-教师管理"], "summary": "获取对会员可见的教师", "description": "获取激活且对会员端展示的教师列表", "operationId": "get_available_teachers_for_members_api_v1_admin_teachers_available_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "region", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/TeacherRegion"}, {"type": "null"}], "description": "教师区域筛选", "title": "Region"}, "description": "教师区域筛选"}, {"name": "category", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/TeacherCategory"}, {"type": "null"}], "description": "教师分类筛选", "title": "Category"}, "description": "教师分类筛选"}, {"name": "max_price", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer", "minimum": 0}, {"type": "null"}], "description": "最高价格筛选", "title": "Max Price"}, "description": "最高价格筛选"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_TeacherList_"}}}}}}}, "/api/v1/admin/teachers/search": {"get": {"tags": ["管理端-教师管理"], "summary": "搜索教师", "description": "根据关键词搜索教师", "operationId": "search_teachers_api_v1_admin_teachers_search_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "keyword", "in": "query", "required": true, "schema": {"type": "string", "description": "搜索关键词", "title": "Keyword"}, "description": "搜索关键词"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "返回数量限制", "default": 20, "title": "Limit"}, "description": "返回数量限制"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_TeacherList_"}}}}}}}, "/api/v1/admin/teachers/tags/batch": {"post": {"tags": ["管理端-教师管理"], "summary": "批量管理教师标签", "description": "批量为多个教师分配或移除标签", "operationId": "batch_manage_teacher_tags_api_v1_admin_teachers_tags_batch_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TeacherTagBatch"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/teachers/": {"post": {"tags": ["管理端-教师管理"], "summary": "创建教师", "description": "创建新教师\n    \n    **可能的错误码：**\n    - `TEACHER_EMAIL_EXISTS`: 邮箱已存在\n    - `TEACHER_PHONE_EXISTS`: 手机号已存在\n    - `TEACHER_WECHAT_BOUND`: 微信已绑定其他教师\n    - `VALIDATION_ERROR`: 数据验证失败", "operationId": "create_teacher_api_v1_admin_teachers__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TeacherCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TeacherRead_"}}}}}}, "get": {"tags": ["管理端-教师管理"], "summary": "获取教师列表", "description": "获取教师列表，支持分页、筛选和排序", "operationId": "get_teachers_api_v1_admin_teachers__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "title": "Page"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 20, "title": "Size"}}, {"name": "name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}}, {"name": "teacher_category", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/TeacherCategory"}, {"type": "null"}], "title": "Teacher Category"}}, {"name": "region", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/TeacherRegion"}, {"type": "null"}], "title": "Region"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/TeacherStatus"}, {"type": "null"}], "title": "Status"}}, {"name": "show_to_members", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Show To Members"}}, {"name": "min_price", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer", "minimum": 0}, {"type": "null"}], "title": "<PERSON>"}}, {"name": "max_price", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer", "minimum": 0}, {"type": "null"}], "title": "Max Price"}}, {"name": "tag_ids", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tag Ids"}}, {"name": "sort_by", "in": "query", "required": false, "schema": {"type": "string", "default": "created_at", "title": "Sort By"}}, {"name": "sort_order", "in": "query", "required": false, "schema": {"type": "string", "default": "desc", "title": "Sort Order"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageResponse_TeacherList_"}}}}}}}, "/api/v1/admin/teachers/{teacher_id}": {"get": {"tags": ["管理端-教师管理"], "summary": "获取教师详情", "description": "根据ID获取教师详情，包含关联的标签信息", "operationId": "get_teacher_api_v1_admin_teachers__teacher_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Teacher Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TeacherDetail_"}}}}}}}, "/api/v1/admin/teachers/{teacher_id}/update": {"post": {"tags": ["管理端-教师管理"], "summary": "更新教师信息", "description": "更新教师基本信息", "operationId": "update_teacher_api_v1_admin_teachers__teacher_id__update_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Teacher Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TeacherUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TeacherRead_"}}}}}}}, "/api/v1/admin/teachers/{teacher_id}/delete": {"post": {"tags": ["管理端-教师管理"], "summary": "删除教师", "description": "删除指定教师", "operationId": "delete_teacher_api_v1_admin_teachers__teacher_id__delete_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Teacher Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}}}}, "/api/v1/admin/teachers/{teacher_id}/status": {"post": {"tags": ["管理端-教师管理"], "summary": "更新教师状态", "description": "更新教师状态（激活/停用/暂停）", "operationId": "update_teacher_status_api_v1_admin_teachers__teacher_id__status_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Teacher Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TeacherStatusUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TeacherRead_"}}}}}}}, "/api/v1/admin/teachers/{teacher_id}/update-stats": {"post": {"tags": ["管理端-教师管理"], "summary": "Update Teacher Stats", "description": "更新教师统计信息", "operationId": "update_teacher_stats_api_v1_admin_teachers__teacher_id__update_stats_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Teacher Id"}}, {"name": "class_completed", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Class Completed"}}, {"name": "class_cancelled", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Class Cancelled"}}, {"name": "class_no_show", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Class No Show"}}, {"name": "earnings_added", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "Earnings Added"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TeacherRead_"}}}}}}}, "/api/v1/admin/teachers/{teacher_id}/activate": {"post": {"tags": ["管理端-教师管理"], "summary": "激活教师", "description": "激活指定教师", "operationId": "activate_teacher_api_v1_admin_teachers__teacher_id__activate_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Teacher Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TeacherRead_"}}}}}}}, "/api/v1/admin/teachers/{teacher_id}/deactivate": {"post": {"tags": ["管理端-教师管理"], "summary": "停用教师", "description": "停用指定教师", "operationId": "deactivate_teacher_api_v1_admin_teachers__teacher_id__deactivate_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Teacher Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TeacherRead_"}}}}}}}, "/api/v1/admin/teachers/{teacher_id}/tags": {"post": {"tags": ["管理端-教师管理"], "summary": "为教师分配标签", "description": "为指定教师分配一个或多个标签", "operationId": "assign_tags_to_teacher_api_v1_admin_teachers__teacher_id__tags_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Teacher Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TeacherTagAssign"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}}}, "get": {"tags": ["管理端-教师管理"], "summary": "获取教师标签列表", "description": "获取指定教师的所有标签", "operationId": "get_teacher_tags_api_v1_admin_teachers__teacher_id__tags_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Teacher Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_dict_"}}}}}}}, "/api/v1/admin/teachers/{teacher_id}/tags/remove": {"post": {"tags": ["管理端-教师管理"], "summary": "移除教师标签", "description": "移除教师的指定标签", "operationId": "remove_tags_from_teacher_api_v1_admin_teachers__teacher_id__tags_remove_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Teacher Id"}}, {"name": "tag_ids", "in": "query", "required": true, "schema": {"type": "string", "description": "要移除的标签ID列表，逗号分隔", "title": "Tag Ids"}, "description": "要移除的标签ID列表，逗号分隔"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}}}}, "/api/v1/admin/teachers/{teacher_id}/avatar": {"post": {"tags": ["管理端-教师管理"], "summary": "上传教师头像", "description": "上传教师头像图片（基础版本，返回文件路径）", "operationId": "upload_teacher_avatar_api_v1_admin_teachers__teacher_id__avatar_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Teacher Id"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_upload_teacher_avatar_api_v1_admin_teachers__teacher_id__avatar_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/admin/teachers/{teacher_id}/avatar/delete": {"post": {"tags": ["管理端-教师管理"], "summary": "删除教师头像", "description": "删除教师头像", "operationId": "delete_teacher_avatar_api_v1_admin_teachers__teacher_id__avatar_delete_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Teacher Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}}}}, "/api/v1/admin/tags/categories": {"post": {"tags": ["管理端-标签管理"], "summary": "创建标签分类", "description": "创建新的标签分类\n    \n    **可能的错误码：**\n    - `TAG_CATEGORY_NAME_EXISTS`: 分类名称已存在\n    - `VALIDATION_ERROR`: 数据验证失败", "operationId": "create_tag_category_api_v1_admin_tags_categories_post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TagCategoryCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TagCategoryRead_"}}}}}}, "get": {"tags": ["管理端-标签管理"], "summary": "获取标签分类列表", "description": "获取标签分类列表，支持搜索", "operationId": "get_tag_categories_api_v1_admin_tags_categories_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "分类名称模糊查询", "title": "Name"}, "description": "分类名称模糊查询"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_TagCategoryList_"}}}}}}}, "/api/v1/admin/tags/categories/{category_id}": {"get": {"tags": ["管理端-标签管理"], "summary": "获取标签分类详情", "description": "根据ID获取标签分类详情", "operationId": "get_tag_category_api_v1_admin_tags_categories__category_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "category_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Category Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TagCategoryRead_"}}}}}}, "post": {"tags": ["管理端-标签管理"], "summary": "更新标签分类", "description": "更新标签分类信息\n    \n    **可能的错误码：**\n    - `TAG_CATEGORY_NAME_EXISTS`: 分类名称已存在\n    - `VALIDATION_ERROR`: 数据验证失败", "operationId": "update_tag_category_api_v1_admin_tags_categories__category_id__post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "category_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Category Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TagCategoryUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TagCategoryRead_"}}}}}}, "delete": {"tags": ["管理端-标签管理"], "summary": "删除标签分类", "description": "删除标签分类\n    \n    **可能的错误码：**\n    - `TAG_CATEGORY_HAS_TAGS`: 分类下还有标签，无法删除", "operationId": "delete_tag_category_api_v1_admin_tags_categories__category_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "category_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Category Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}}}}, "/api/v1/admin/tags/": {"post": {"tags": ["管理端-标签管理"], "summary": "创建标签", "description": "创建新标签\n    \n    **可能的错误码：**\n    - `TAG_TAG_NAME_EXISTS`: 标签名称在该分类下已存在\n    - `VALIDATION_ERROR`: 数据验证失败", "operationId": "create_tag_api_v1_admin_tags__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TagCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TagRead_"}}}}}}, "get": {"tags": ["管理端-标签管理"], "summary": "获取标签列表", "description": "获取标签列表，支持搜索和筛选", "operationId": "get_tags_api_v1_admin_tags__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "标签名称模糊查询", "title": "Name"}, "description": "标签名称模糊查询"}, {"name": "category_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "标签分类ID", "title": "Category Id"}, "description": "标签分类ID"}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/TagStatus"}, {"type": "null"}], "description": "标签状态", "title": "Status"}, "description": "标签状态"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_TagRead_"}}}}}}}, "/api/v1/admin/tags/with-teacher-count": {"get": {"tags": ["管理端-标签管理"], "summary": "获取带教师数量的标签列表", "description": "获取标签列表，包含每个标签被多少教师使用的统计信息", "operationId": "get_tags_with_teacher_count_api_v1_admin_tags_with_teacher_count_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "标签名称模糊查询", "title": "Name"}, "description": "标签名称模糊查询"}, {"name": "category_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "标签分类ID", "title": "Category Id"}, "description": "标签分类ID"}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/TagStatus"}, {"type": "null"}], "description": "标签状态", "title": "Status"}, "description": "标签状态"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_TagWithTeacherCount_"}}}}}}}, "/api/v1/admin/tags/batch": {"post": {"tags": ["管理端-标签管理"], "summary": "批量创建标签", "description": "批量创建标签\n\n    **注意：**\n    - 如果某个标签名称已存在，会跳过该标签继续处理其他标签\n    - 返回成功创建的标签列表\n\n    **可能的错误码：**\n    - `VALIDATION_ERROR`: 数据验证失败", "operationId": "batch_create_tags_api_v1_admin_tags_batch_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TagBatchCreate"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_List_TagRead__"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/tags/batch/update": {"post": {"tags": ["管理端-标签管理"], "summary": "批量更新标签", "description": "批量更新标签\n\n    **可能的错误码：**\n    - `VALIDATION_ERROR`: 数据验证失败", "operationId": "batch_update_tags_api_v1_admin_tags_batch_update_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TagBatchUpdate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_List_TagRead__"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/tags/active": {"get": {"tags": ["管理端-标签管理"], "summary": "获取所有激活状态的标签", "description": "获取所有激活状态的标签，按分类排序", "operationId": "get_active_tags_api_v1_admin_tags_active_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_TagList_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/tags/{tag_id}": {"get": {"tags": ["管理端-标签管理"], "summary": "获取标签详情", "description": "根据ID获取标签详情", "operationId": "get_tag_api_v1_admin_tags__tag_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tag_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Tag Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TagRead_"}}}}}}, "post": {"tags": ["管理端-标签管理"], "summary": "更新标签", "description": "更新标签信息\n\n    **可能的错误码：**\n    - `TAG_TAG_NAME_EXISTS`: 标签名称在该分类下已存在\n    - `VALIDATION_ERROR`: 数据验证失败", "operationId": "update_tag_api_v1_admin_tags__tag_id__post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tag_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Tag Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TagUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TagRead_"}}}}}}, "delete": {"tags": ["管理端-标签管理"], "summary": "删除标签", "description": "删除标签\n\n    **可能的错误码：**\n    - `TAG_TAG_IN_USE`: 标签正在使用中，无法删除", "operationId": "delete_tag_api_v1_admin_tags__tag_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tag_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Tag Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}}}}, "/api/v1/admin/tags/categories/{category_id}/tags": {"get": {"tags": ["管理端-标签管理"], "summary": "获取指定分类下的所有标签", "description": "获取指定分类下的所有标签，不分页", "operationId": "get_tags_by_category_api_v1_admin_tags_categories__category_id__tags_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "category_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Category Id"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/TagStatus"}, {"type": "null"}], "description": "标签状态筛选", "title": "Status"}, "description": "标签状态筛选"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_TagList_"}}}}}}}, "/api/v1/admin/member-cards/templates": {"get": {"tags": ["管理端-会员卡管理"], "summary": "Get Card Templates", "description": "获取会员卡模板列表", "operationId": "get_card_templates_api_v1_admin_member_cards_templates_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_List_MemberCardTemplateList__"}}}}}, "security": [{"HTTPBearer": []}]}, "post": {"tags": ["管理端-会员卡管理"], "summary": "Create Card Template", "description": "创建会员卡模板", "operationId": "create_card_template_api_v1_admin_member_cards_templates_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberCardTemplateCreate"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberCardTemplateRead_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/member-cards/templates/{template_id}": {"get": {"tags": ["管理端-会员卡管理"], "summary": "Get Card Template", "description": "获取会员卡模板详情", "operationId": "get_card_template_api_v1_admin_member_cards_templates__template_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "template_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Template Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberCardTemplateRead_"}}}}}}, "put": {"tags": ["管理端-会员卡管理"], "summary": "Update Card Template", "description": "更新会员卡模板", "operationId": "update_card_template_api_v1_admin_member_cards_templates__template_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "template_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Template Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberCardTemplateUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberCardTemplateRead_"}}}}}}, "delete": {"tags": ["管理端-会员卡管理"], "summary": "Delete Card Template", "description": "删除会员卡模板", "operationId": "delete_card_template_api_v1_admin_member_cards_templates__template_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "template_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Template Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/admin/member-cards/templates/{template_id}/toggle-status": {"patch": {"tags": ["管理端-会员卡管理"], "summary": "Toggle Template Status", "description": "切换会员卡模板激活状态", "operationId": "toggle_template_status_api_v1_admin_member_cards_templates__template_id__toggle_status_patch", "security": [{"HTTPBearer": []}], "parameters": [{"name": "template_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Template Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberCardTemplateRead_"}}}}}}}, "/api/v1/admin/member-cards/cards": {"get": {"tags": ["管理端-会员卡管理"], "summary": "Get Member Cards", "description": "获取会员卡列表", "operationId": "get_member_cards_api_v1_admin_member_cards_cards_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0}, {"type": "null"}], "title": "Member Id"}}, {"name": "card_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/CardType"}, {"type": "null"}], "title": "Card Type"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/CardStatus"}, {"type": "null"}], "title": "Status"}}, {"name": "template_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0}, {"type": "null"}], "title": "Template Id"}}, {"name": "search_keyword", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Search Keyword"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "title": "Page"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 20, "title": "Size"}}, {"name": "sort_by", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "created_at", "title": "Sort By"}}, {"name": "sort_order", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "desc", "title": "Sort Order"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageResponse_MemberCardList_"}}}}}}, "post": {"tags": ["管理端-会员卡管理"], "summary": "Create Member Card", "description": "创建会员卡", "operationId": "create_member_card_api_v1_admin_member_cards_cards_post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberCardCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberCardRead_"}}}}}}}, "/api/v1/admin/member-cards/cards/{card_id}": {"get": {"tags": ["管理端-会员卡管理"], "summary": "Get Member Card", "description": "获取会员卡详情", "operationId": "get_member_card_api_v1_admin_member_cards_cards__card_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "card_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Card Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberCardRead_"}}}}}}, "put": {"tags": ["管理端-会员卡管理"], "summary": "Update Member Card", "description": "更新会员卡", "operationId": "update_member_card_api_v1_admin_member_cards_cards__card_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "card_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Card Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberCardUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberCardRead_"}}}}}}}, "/api/v1/admin/member-cards/cards/{card_id}/freeze": {"patch": {"tags": ["管理端-会员卡管理"], "summary": "Freeze Member Card", "description": "冻结会员卡", "operationId": "freeze_member_card_api_v1_admin_member_cards_cards__card_id__freeze_patch", "security": [{"HTTPBearer": []}], "parameters": [{"name": "card_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Card Id"}}, {"name": "reason", "in": "query", "required": false, "schema": {"type": "string", "default": "管理员冻结", "title": "Reason"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberCardRead_"}}}}}}}, "/api/v1/admin/member-cards/cards/{card_id}/unfreeze": {"patch": {"tags": ["管理端-会员卡管理"], "summary": "Unfreeze Member Card", "description": "解冻会员卡", "operationId": "unfreeze_member_card_api_v1_admin_member_cards_cards__card_id__unfreeze_patch", "security": [{"HTTPBearer": []}], "parameters": [{"name": "card_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Card Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberCardRead_"}}}}}}}, "/api/v1/admin/member-cards/cards/{card_id}/cancel": {"patch": {"tags": ["管理端-会员卡管理"], "summary": "Cancel Member Card", "description": "注销会员卡", "operationId": "cancel_member_card_api_v1_admin_member_cards_cards__card_id__cancel_patch", "security": [{"HTTPBearer": []}], "parameters": [{"name": "card_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Card Id"}}, {"name": "reason", "in": "query", "required": false, "schema": {"type": "string", "default": "管理员注销", "title": "Reason"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberCardRead_"}}}}}}}, "/api/v1/admin/member-cards/members/{member_id}/cards": {"get": {"tags": ["管理端-会员卡管理"], "summary": "Get Member Cards By Member", "description": "获取指定会员的所有卡片", "operationId": "get_member_cards_by_member_api_v1_admin_member_cards_members__member_id__cards_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Member Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_List_MemberCardRead__"}}}}}}}, "/api/v1/admin/member-cards/cards/{card_id}/recharge": {"post": {"tags": ["管理端-会员卡管理"], "summary": "Recharge Member Card", "description": "会员卡充值", "operationId": "recharge_member_card_api_v1_admin_member_cards_cards__card_id__recharge_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "card_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Card Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RechargeRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_RechargeResponse_"}}}}}}}, "/api/v1/admin/member-cards/recharge/batch": {"post": {"tags": ["管理端-会员卡管理"], "summary": "Batch Recharge Member Cards", "description": "批量充值会员卡", "operationId": "batch_recharge_member_cards_api_v1_admin_member_cards_recharge_batch_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchRechargeRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_BatchRechargeResponse_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/member-cards/cards/{card_id}/recharge-history": {"get": {"tags": ["管理端-会员卡管理"], "summary": "Get Card Recharge History", "description": "获取会员卡充值历史", "operationId": "get_card_recharge_history_api_v1_admin_member_cards_cards__card_id__recharge_history_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "card_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Card Id"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 50, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_List_MemberCardOperationRead__"}}}}}}}, "/api/v1/admin/member-cards/members/{member_id}/recharge-statistics": {"get": {"tags": ["管理端-会员卡管理"], "summary": "Get Member Recharge Statistics", "description": "获取会员充值统计", "operationId": "get_member_recharge_statistics_api_v1_admin_member_cards_members__member_id__recharge_statistics_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Member Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/admin/member-cards/recharge/daily-statistics": {"get": {"tags": ["管理端-会员卡管理"], "summary": "Get Daily Recharge Statistics", "description": "获取每日充值统计", "operationId": "get_daily_recharge_statistics_api_v1_admin_member_cards_recharge_daily_statistics_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "start_date", "in": "query", "required": true, "schema": {"type": "string", "title": "Start Date"}}, {"name": "end_date", "in": "query", "required": true, "schema": {"type": "string", "title": "End Date"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/admin/member-cards/cards/{card_id}/deduct": {"post": {"tags": ["管理端-会员卡管理"], "summary": "Deduct Member Card", "description": "会员卡扣费", "operationId": "deduct_member_card_api_v1_admin_member_cards_cards__card_id__deduct_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "card_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Card Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeductionRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_DeductionResponse_"}}}}}}}, "/api/v1/admin/member-cards/operations": {"get": {"tags": ["管理端-会员卡管理"], "summary": "获取会员卡操作记录", "description": "分页获取会员卡操作记录列表，支持按会员ID、卡ID和操作类型筛选\n    \n    **必填参数：**\n    - `member_id`: 会员ID\n    \n    **可选参数：**\n    - `member_card_id`: 会员卡ID\n    - `operation_types`: 操作类型列表，支持多选\n    \n    **分页参数：**\n    - `page`: 页码，从1开始（默认1）\n    - `size`: 每页大小，默认20，最大100", "operationId": "get_member_card_operations_api_v1_admin_member_cards_operations_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Member Id"}}, {"name": "member_card_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Member Card Id"}}, {"name": "operation_types", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "title": "Operation Types"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "default": 1, "title": "Page"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "default": 20, "title": "Size"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageResponse_MemberCardOperationRead_"}}}}}}}, "/api/v1/admin/member-cards/operations/{operation_id}": {"get": {"tags": ["管理端-会员卡管理"], "summary": "Get Card Operation Detail", "description": "获取操作记录详情", "operationId": "get_card_operation_detail_api_v1_admin_member_cards_operations__operation_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "operation_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Operation Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberCardOperationRead_"}}}}}}}, "/api/v1/admin/member-cards/cards/{card_id}/consumption-history": {"get": {"tags": ["管理端-会员卡管理"], "summary": "Get Card Consumption History", "description": "获取会员卡消费历史", "operationId": "get_card_consumption_history_api_v1_admin_member_cards_cards__card_id__consumption_history_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "card_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Card Id"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 50, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_List_MemberCardOperationRead__"}}}}}}}, "/api/v1/admin/member-cards/members/{member_id}/consumption-statistics": {"get": {"tags": ["管理端-会员卡管理"], "summary": "Get Member Consumption Statistics", "description": "获取会员消费统计", "operationId": "get_member_consumption_statistics_api_v1_admin_member_cards_members__member_id__consumption_statistics_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Member Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/admin/member-cards/consumption/daily-statistics": {"get": {"tags": ["管理端-会员卡管理"], "summary": "Get Daily Consumption Statistics", "description": "获取每日消费统计", "operationId": "get_daily_consumption_statistics_api_v1_admin_member_cards_consumption_daily_statistics_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "start_date", "in": "query", "required": true, "schema": {"type": "string", "title": "Start Date"}}, {"name": "end_date", "in": "query", "required": true, "schema": {"type": "string", "title": "End Date"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/admin/member-cards/cards/{card_id}/operation-summary": {"get": {"tags": ["管理端-会员卡管理"], "summary": "Get Card Operation Summary", "description": "获取会员卡操作汇总统计", "operationId": "get_card_operation_summary_api_v1_admin_member_cards_cards__card_id__operation_summary_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "card_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Card Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/auth/admin/login": {"post": {"tags": ["认证"], "summary": "<PERSON><PERSON>", "description": "CMS管理员登录", "operationId": "admin_login_api_v1_auth_admin_login_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminLoginRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_AdminLoginResponse_"}}}}}}}, "/api/v1/auth/member/login": {"post": {"tags": ["认证"], "summary": "Member <PERSON><PERSON>", "description": "会员登录（手机号+验证码）", "operationId": "member_login_api_v1_auth_member_login_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberLogin"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberLoginResponse_"}}}}}}}, "/api/v1/auth/member/send-code": {"post": {"tags": ["认证"], "summary": "Send Verification Code", "description": "发送验证码", "operationId": "send_verification_code_api_v1_auth_member_send_code_post", "parameters": [{"name": "phone", "in": "query", "required": true, "schema": {"type": "string", "title": "Phone"}}, {"name": "tenant_code", "in": "query", "required": true, "schema": {"type": "string", "title": "Tenant Code"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/info/health": {"get": {"tags": ["公开信息"], "summary": "Health Check", "description": "健康检查", "operationId": "health_check_api_v1_info_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/info/version": {"get": {"tags": ["公开信息"], "summary": "Get Version", "description": "获取API版本信息", "operationId": "get_version_api_v1_info_version_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"AdminLoginRequest": {"properties": {"username": {"type": "string", "title": "Username", "example": "demo_admin"}, "password": {"type": "string", "title": "Password", "example": "demo123456"}}, "type": "object", "required": ["username", "password"], "title": "AdminLoginRequest", "description": "管理员登录请求"}, "AdminLoginResponse": {"properties": {"access_token": {"type": "string", "title": "Access Token"}, "expires": {"type": "string", "format": "date-time", "title": "Expires"}, "token_type": {"type": "string", "title": "Token Type"}, "user": {"$ref": "#/components/schemas/UserRead"}, "tenant": {"anyOf": [{"$ref": "#/components/schemas/TenantRead"}, {"type": "null"}]}}, "type": "object", "required": ["access_token", "expires", "token_type", "user"], "title": "AdminLoginResponse", "description": "管理员登录响应"}, "BatchRechargeRequest": {"properties": {"recharge_items": {"items": {"$ref": "#/components/schemas/RechargeRequest"}, "type": "array", "title": "Recharge Items", "description": "充值项目列表"}}, "type": "object", "required": ["recharge_items"], "title": "BatchRechargeRequest", "description": "批量充值请求模型"}, "BatchRechargeResponse": {"properties": {"success_count": {"type": "integer", "title": "Success Count", "description": "成功数量"}, "failed_count": {"type": "integer", "title": "Failed Count", "description": "失败数量"}, "success_items": {"items": {"$ref": "#/components/schemas/RechargeResponse"}, "type": "array", "title": "Success Items", "description": "成功项目列表"}, "failed_items": {"items": {"type": "object"}, "type": "array", "title": "Failed Items", "description": "失败项目列表（包含错误信息）"}}, "type": "object", "required": ["success_count", "failed_count", "success_items", "failed_items"], "title": "BatchRechargeResponse", "description": "批量充值响应模型"}, "BillingCycle": {"type": "string", "enum": ["monthly", "quarterly", "yearly"], "title": "BillingCycle", "description": "计费周期枚举"}, "Body_upload_teacher_avatar_api_v1_admin_teachers__teacher_id__avatar_post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File", "description": "头像图片文件"}}, "type": "object", "required": ["file"], "title": "Body_upload_teacher_avatar_api_v1_admin_teachers__teacher_id__avatar_post"}, "CardStatus": {"type": "string", "enum": ["active", "frozen", "expired", "cancelled"], "title": "CardStatus", "description": "卡片状态枚举"}, "CardType": {"type": "string", "enum": ["times_limited", "times_unlimited", "value_limited", "value_unlimited"], "title": "CardType", "description": "卡片类型枚举"}, "DataResponse_AdminLoginResponse_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/AdminLoginResponse"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[AdminLoginResponse]"}, "DataResponse_BatchRechargeResponse_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/BatchRechargeResponse"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[BatchRechargeResponse]"}, "DataResponse_DeductionResponse_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/DeductionResponse"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[DeductionResponse]"}, "DataResponse_List_MemberCardOperationRead__": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"items": {"$ref": "#/components/schemas/MemberCardOperationRead"}, "type": "array"}, {"type": "null"}], "title": "Data", "description": "响应数据"}}, "type": "object", "title": "DataResponse[List[MemberCardOperationRead]]"}, "DataResponse_List_MemberCardRead__": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"items": {"$ref": "#/components/schemas/MemberCardRead"}, "type": "array"}, {"type": "null"}], "title": "Data", "description": "响应数据"}}, "type": "object", "title": "DataResponse[List[MemberCardRead]]"}, "DataResponse_List_MemberCardTemplateList__": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"items": {"$ref": "#/components/schemas/MemberCardTemplateList"}, "type": "array"}, {"type": "null"}], "title": "Data", "description": "响应数据"}}, "type": "object", "title": "DataResponse[List[MemberCardTemplateList]]"}, "DataResponse_List_TagRead__": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"items": {"$ref": "#/components/schemas/TagRead"}, "type": "array"}, {"type": "null"}], "title": "Data", "description": "响应数据"}}, "type": "object", "title": "DataResponse[List[TagRead]]"}, "DataResponse_MemberCardOperationRead_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/MemberCardOperationRead"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[MemberCardOperationRead]"}, "DataResponse_MemberCardRead_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/MemberCardRead"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[MemberCardRead]"}, "DataResponse_MemberCardTemplateRead_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/MemberCardTemplateRead"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[MemberCardTemplateRead]"}, "DataResponse_MemberLoginResponse_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/MemberLoginResponse"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[MemberLoginResponse]"}, "DataResponse_MemberRead_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/MemberRead"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[MemberRead]"}, "DataResponse_RechargeResponse_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/RechargeResponse"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[RechargeResponse]"}, "DataResponse_TagCategoryRead_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/TagCategoryRead"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[TagCategoryRead]"}, "DataResponse_TagRead_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/TagRead"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[TagRead]"}, "DataResponse_TeacherDetail_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/TeacherDetail"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[TeacherDetail]"}, "DataResponse_TeacherRead_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/TeacherRead"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[TeacherRead]"}, "DataResponse_UserRead_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/UserRead"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[UserRead]"}, "DataResponse_dict_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Data", "description": "响应数据"}}, "type": "object", "title": "DataResponse[dict]"}, "DataResponse_int_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Data", "description": "响应数据"}}, "type": "object", "title": "DataResponse[int]"}, "DeductionRequest": {"properties": {"member_card_id": {"type": "integer", "exclusiveMinimum": 0.0, "title": "Member Card Id", "description": "会员卡ID"}, "amount": {"type": "integer", "exclusiveMinimum": 0.0, "title": "Amount", "description": "扣费金额（元）"}, "reduce_validity_days": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Reduce Validity Days", "description": "减少有效期（天）", "default": 0}, "notes": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Notes", "description": "备注信息"}}, "type": "object", "required": ["member_card_id", "amount"], "title": "DeductionRequest", "description": "扣费请求模型"}, "DeductionResponse": {"properties": {"operation_id": {"type": "integer", "title": "Operation Id", "description": "操作记录ID"}, "member_card_id": {"type": "integer", "title": "Member Card Id", "description": "会员卡ID"}, "amount": {"type": "integer", "title": "Amount", "description": "扣费金额（元）"}, "balance_before": {"type": "integer", "title": "Balance Before", "description": "扣费前余额（元）"}, "balance_after": {"type": "integer", "title": "Balance After", "description": "扣费后余额（元）"}, "reduce_validity_days": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Reduce Validity Days", "description": "减少的有效期天数"}, "expires_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expires At", "description": "新的过期时间"}, "transaction_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Transaction Id", "description": "交易ID"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "扣费时间"}}, "type": "object", "required": ["operation_id", "member_card_id", "amount", "balance_before", "balance_after", "transaction_id", "created_at"], "title": "DeductionResponse", "description": "扣费响应模型"}, "ErrorResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": false}, "message": {"type": "string", "title": "Message", "default": "操作失败"}, "http_code": {"type": "integer", "title": "Http Code", "default": 400}, "business_code": {"type": "string", "title": "Business Code", "default": "BUSINESS_ERROR"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "level": {"type": "string", "title": "Level", "default": "error"}, "details": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Details"}}, "type": "object", "title": "ErrorResponse", "description": "错误响应模型"}, "Gender": {"type": "string", "enum": ["male", "female", "other"], "title": "Gender", "description": "性别枚举"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "ListResponse_TagCategoryList_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/TagCategoryList"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}}, "type": "object", "title": "ListResponse[TagCategoryList]"}, "ListResponse_TagList_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/TagList"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}}, "type": "object", "title": "ListResponse[TagList]"}, "ListResponse_TagRead_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/TagRead"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}}, "type": "object", "title": "ListResponse[TagRead]"}, "ListResponse_TagWithTeacherCount_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/TagWithTeacherCount"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}}, "type": "object", "title": "ListResponse[TagWithTeacherCount]"}, "ListResponse_TeacherList_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/TeacherList"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}}, "type": "object", "title": "ListResponse[TeacherList]"}, "ListResponse_UserRead_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/UserRead"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}}, "type": "object", "title": "ListResponse[UserRead]"}, "ListResponse_dict_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"type": "object"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}}, "type": "object", "title": "ListResponse[dict]"}, "MemberBasicRead": {"properties": {"tenant_id": {"type": "integer", "title": "Tenant Id", "description": "租户ID"}, "name": {"type": "string", "maxLength": 50, "title": "Name", "description": "姓名"}, "phone": {"type": "string", "maxLength": 20, "title": "Phone", "description": "手机号"}, "email": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Email", "description": "邮箱"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/Gender"}, {"type": "null"}], "description": "性别"}, "birthday": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Birthday", "description": "生日"}, "avatar_url": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Avatar Url", "description": "头像URL"}, "wechat_openid": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Wechat Openid", "description": "微信OpenID"}, "wechat_unionid": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Wechat <PERSON>id", "description": "微信UnionID"}, "wechat_nickname": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "微信昵称"}, "wechat_avatar": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Wechat Avatar", "description": "微信头像"}, "member_type": {"$ref": "#/components/schemas/MemberType", "description": "会员类型", "default": "trial"}, "member_status": {"$ref": "#/components/schemas/MemberStatus", "description": "会员状态", "default": "active"}, "source_channel": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Source Channel", "description": "来源渠道"}, "agent_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Agent Id", "description": "代理人员ID"}, "address": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Address", "description": "地址"}, "city": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "City", "description": "城市"}, "province": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Province", "description": "省份"}, "country": {"type": "string", "maxLength": 50, "title": "Country", "description": "国家", "default": "China"}, "postal_code": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Postal Code", "description": "邮编"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "备注"}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "标签"}, "primary_member_card_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Primary Member Card Id", "description": "主要会员卡ID"}, "primary_member_card_name": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Primary Member Card Name", "description": "主要会员卡名称"}, "id": {"type": "integer", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "registered_at": {"type": "string", "format": "date-time", "title": "Registered At"}}, "type": "object", "required": ["tenant_id", "name", "phone", "id", "created_at", "registered_at"], "title": "MemberBasicRead", "description": "会员基础响应模型（不包含统计信息）"}, "MemberCardCreate": {"properties": {"member_id": {"type": "integer", "exclusiveMinimum": 0.0, "title": "Member Id", "description": "会员ID"}, "template_id": {"type": "integer", "exclusiveMinimum": 0.0, "title": "Template Id", "description": "模板ID"}}, "type": "object", "required": ["member_id", "template_id"], "title": "MemberCardCreate", "description": "创建会员卡请求模型"}, "MemberCardList": {"properties": {"id": {"type": "integer", "title": "Id"}, "member_id": {"type": "integer", "title": "Member Id"}, "name": {"type": "string", "title": "Name"}, "card_type": {"$ref": "#/components/schemas/CardType"}, "balance": {"type": "integer", "title": "Balance"}, "status": {"$ref": "#/components/schemas/CardStatus"}, "card_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Card Number"}, "expires_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expires At"}, "last_used_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Used At"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}}, "type": "object", "required": ["id", "member_id", "name", "card_type", "balance", "status", "created_at"], "title": "MemberCardList", "description": "会员卡列表项模型"}, "MemberCardOperationRead": {"properties": {"tenant_id": {"type": "integer", "title": "Tenant Id", "description": "租户ID"}, "member_id": {"type": "integer", "title": "Member Id", "description": "会员ID"}, "member_card_id": {"type": "integer", "title": "Member Card Id", "description": "会员卡ID"}, "operation_type": {"$ref": "#/components/schemas/MemberCardOperationType", "description": "操作类型"}, "operation_description": {"type": "string", "maxLength": 200, "title": "Operation Description", "description": "操作描述"}, "amount_change": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Amount Change", "description": "余额变化金额（元，正数表示增加，负数表示减少）"}, "balance_before": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Balance Before", "description": "操作前余额（元）"}, "balance_after": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Balance After", "description": "操作后余额（元）"}, "status_before": {"anyOf": [{"$ref": "#/components/schemas/CardStatus"}, {"type": "null"}], "description": "操作前状态"}, "status_after": {"anyOf": [{"$ref": "#/components/schemas/CardStatus"}, {"type": "null"}], "description": "操作后状态"}, "member_card_name": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Member Card Name", "description": "会员卡名称"}, "bonus_amount": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Bonus Amount", "description": "赠送金额（元）"}, "actual_amount": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Actual Amount", "description": "实收金额（元）"}, "payment_method": {"anyOf": [{"$ref": "#/components/schemas/PaymentMethod"}, {"type": "null"}], "description": "支付方式"}, "payment_status": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Payment Status", "description": "支付状态"}, "transaction_id": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Transaction Id", "description": "第三方交易ID"}, "scheduled_class_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Scheduled Class Id", "description": "关联课程ID"}, "operator_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Operator Id", "description": "操作人ID（为空表示会员自己操作）"}, "operator_name": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Operator Name", "description": "操作人姓名"}, "operator_type": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Operator Type", "description": "操作人类型（member/admin/system）"}, "reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reason", "description": "操作原因"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "备注信息"}, "client_ip": {"anyOf": [{"type": "string", "maxLength": 45}, {"type": "null"}], "title": "Client Ip", "description": "客户端IP"}, "user_agent": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "User Agent", "description": "用户代理"}, "status": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Status", "description": "操作状态", "default": "completed"}, "id": {"type": "integer", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}}, "type": "object", "required": ["tenant_id", "member_id", "member_card_id", "operation_type", "operation_description", "id", "created_at"], "title": "MemberCardOperationRead", "description": "会员卡操作记录响应模型"}, "MemberCardOperationType": {"type": "string", "enum": ["create_card", "update_card_info", "freeze_card", "unfreeze_card", "cancel_card", "recharge", "initial_binding", "direct_booking", "fixed_schedule_booking", "admin_booking", "manual_deduction", "member_cancel_booking", "admin_cancel_booking", "refund", "other"], "title": "MemberCardOperationType", "description": "会员卡操作类型枚举 - 统一记录所有会员卡相关操作"}, "MemberCardRead": {"properties": {"tenant_id": {"type": "integer", "title": "Tenant Id", "description": "租户ID"}, "member_id": {"type": "integer", "title": "Member Id", "description": "会员ID"}, "template_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Template Id", "description": "模板ID"}, "name": {"type": "string", "maxLength": 100, "title": "Name", "description": "卡片名称"}, "card_type": {"$ref": "#/components/schemas/CardType", "description": "卡片类型"}, "balance": {"type": "integer", "minimum": 0.0, "title": "Balance", "description": "当前余额（元或次数）", "default": 0}, "status": {"$ref": "#/components/schemas/CardStatus", "description": "卡片状态", "default": "active"}, "card_number": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Card Number", "description": "卡号"}, "total_recharged": {"type": "integer", "minimum": 0.0, "title": "Total Recharged", "description": "总充值金额（元）", "default": 0}, "total_consumed": {"type": "integer", "minimum": 0.0, "title": "Total Consumed", "description": "总消费金额（元）", "default": 0}, "expires_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expires At", "description": "过期时间"}, "last_used_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Used At", "description": "最后使用时间"}, "freeze_reason": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Freeze Reason", "description": "冻结原因"}, "cancel_reason": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Cancel Reason", "description": "注销原因"}, "id": {"type": "integer", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "created_by": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Created By"}}, "type": "object", "required": ["tenant_id", "member_id", "name", "card_type", "id", "created_at"], "title": "MemberCardRead", "description": "会员卡响应模型"}, "MemberCardSummary": {"properties": {"id": {"type": "integer", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "card_type": {"$ref": "#/components/schemas/CardType"}, "balance": {"type": "integer", "title": "Balance"}, "status": {"$ref": "#/components/schemas/CardStatus"}, "expires_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expires At"}, "last_used_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Used At"}}, "type": "object", "required": ["id", "name", "card_type", "balance", "status"], "title": "MemberCardSummary", "description": "会员卡摘要信息模型（用于会员详情页面）"}, "MemberCardTemplateCreate": {"properties": {"name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Name", "description": "卡片模板名称"}, "card_type": {"$ref": "#/components/schemas/CardType", "description": "卡片类型"}, "sale_price": {"type": "integer", "minimum": 0.0, "title": "Sale Price", "description": "售卖价格（元）"}, "available_balance": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Available Balance", "description": "可用余额（元或次数）"}, "validity_days": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0.0}, {"type": "null"}], "title": "Validity Days", "description": "有效期(天)"}, "is_agent_exclusive": {"type": "boolean", "title": "Is Agent Exclusive", "description": "是否代理专售", "default": false}, "allow_repeat_purchase": {"type": "boolean", "title": "Allow Repeat Purchase", "description": "是否允许重复购买", "default": true}, "allow_renewal": {"type": "boolean", "title": "Allow <PERSON>", "description": "是否支持线上续费", "default": true}, "description": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Description", "description": "模板描述"}}, "type": "object", "required": ["name", "card_type", "sale_price"], "title": "MemberCardTemplateCreate", "description": "创建会员卡模板请求模型"}, "MemberCardTemplateList": {"properties": {"id": {"type": "integer", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "card_type": {"$ref": "#/components/schemas/CardType"}, "sale_price": {"type": "integer", "title": "Sale Price"}, "available_balance": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Available Balance"}, "validity_days": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Validity Days"}, "is_agent_exclusive": {"type": "boolean", "title": "Is Agent Exclusive"}, "is_active": {"type": "boolean", "title": "Is Active"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}}, "type": "object", "required": ["id", "name", "card_type", "sale_price", "is_agent_exclusive", "is_active", "created_at"], "title": "MemberCardTemplateList", "description": "会员卡模板列表项模型"}, "MemberCardTemplateRead": {"properties": {"tenant_id": {"type": "integer", "title": "Tenant Id", "description": "租户ID"}, "name": {"type": "string", "maxLength": 100, "title": "Name", "description": "卡片模板名称"}, "card_type": {"$ref": "#/components/schemas/CardType", "description": "卡片类型"}, "sale_price": {"type": "integer", "minimum": 0.0, "title": "Sale Price", "description": "售卖价格（元）"}, "available_balance": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Available Balance", "description": "可用余额（元或次数）"}, "validity_days": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Validity Days", "description": "有效期(天)"}, "is_agent_exclusive": {"type": "boolean", "title": "Is Agent Exclusive", "description": "是否代理专售", "default": false}, "allow_repeat_purchase": {"type": "boolean", "title": "Allow Repeat Purchase", "description": "是否允许重复购买", "default": true}, "allow_renewal": {"type": "boolean", "title": "Allow <PERSON>", "description": "是否支持续费", "default": true}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "模板描述"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "是否启用", "default": true}, "id": {"type": "integer", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "created_by": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Created By"}}, "type": "object", "required": ["tenant_id", "name", "card_type", "sale_price", "id", "created_at"], "title": "MemberCardTemplateRead", "description": "会员卡模板响应模型"}, "MemberCardTemplateUpdate": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 100, "minLength": 1}, {"type": "null"}], "title": "Name", "description": "卡片模板名称"}, "sale_price": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Sale Price", "description": "售卖价格（元）"}, "available_balance": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Available Balance", "description": "可用余额（元或次数）"}, "validity_days": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0.0}, {"type": "null"}], "title": "Validity Days", "description": "有效期(天)"}, "is_agent_exclusive": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Agent Exclusive", "description": "是否代理专售"}, "allow_repeat_purchase": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Allow Repeat Purchase", "description": "是否允许重复购买"}, "allow_renewal": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Allow <PERSON>", "description": "是否支持线上续费"}, "description": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Description", "description": "模板描述"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active", "description": "是否启用"}}, "type": "object", "title": "MemberCardTemplateUpdate", "description": "更新会员卡模板请求模型"}, "MemberCardUpdate": {"properties": {"status": {"anyOf": [{"$ref": "#/components/schemas/CardStatus"}, {"type": "null"}], "description": "卡片状态"}, "balance": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Balance", "description": "余额（元或次数）"}, "expires_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expires At", "description": "过期时间"}, "freeze_reason": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Freeze Reason", "description": "冻结原因"}, "cancel_reason": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Cancel Reason", "description": "注销原因"}}, "type": "object", "title": "MemberCardUpdate", "description": "更新会员卡请求模型"}, "MemberCreate": {"properties": {"name": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Name"}, "phone": {"type": "string", "maxLength": 20, "minLength": 11, "title": "Phone"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/Gender"}, {"type": "null"}]}, "birthday": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Birthday"}, "member_type": {"$ref": "#/components/schemas/MemberType", "default": "trial"}, "source_channel": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Source Channel"}, "agent_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Agent Id"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}}, "type": "object", "required": ["name", "phone"], "title": "MemberCreate", "description": "创建会员请求模型"}, "MemberLogin": {"properties": {"phone": {"type": "string", "maxLength": 20, "minLength": 11, "title": "Phone"}, "verification_code": {"type": "string", "maxLength": 6, "minLength": 4, "title": "Verification Code"}, "tenant_code": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Tenant Code"}}, "type": "object", "required": ["phone", "verification_code", "tenant_code"], "title": "Member<PERSON><PERSON><PERSON>", "description": "会员登录请求模型"}, "MemberLoginResponse": {"properties": {"access_token": {"type": "string", "title": "Access Token"}, "token_type": {"type": "string", "title": "Token Type"}, "member": {"$ref": "#/components/schemas/MemberBasicRead"}, "tenant": {"$ref": "#/components/schemas/TenantRead"}}, "type": "object", "required": ["access_token", "token_type", "member", "tenant"], "title": "MemberLoginResponse", "description": "会员登录响应"}, "MemberRead": {"properties": {"total_classes": {"type": "integer", "title": "Total Classes", "description": "总上课数", "default": 0}, "completed_classes": {"type": "integer", "title": "Completed Classes", "description": "完成上课数", "default": 0}, "cancelled_classes": {"type": "integer", "title": "Cancelled Classes", "description": "取消上课数", "default": 0}, "no_show_classes": {"type": "integer", "title": "No Show Classes", "description": "缺席上课数", "default": 0}, "total_spent": {"type": "integer", "title": "Total Spent", "description": "总消费金额（元）", "default": 0}, "current_month_spent": {"type": "integer", "title": "Current Month Spent", "description": "当月消费金额（元）", "default": 0}, "total_recharged": {"type": "integer", "title": "Total Recharged", "description": "总充值金额（元）", "default": 0}, "avg_rating": {"type": "string", "title": "Avg <PERSON>ing", "description": "平均评分", "default": "0.0"}, "rating_count": {"type": "integer", "title": "Rating Count", "description": "评价次数", "default": 0}, "last_class_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Class At", "description": "最后上课时间"}, "last_login_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Login At", "description": "最后登录时间"}, "tenant_id": {"type": "integer", "title": "Tenant Id", "description": "租户ID"}, "name": {"type": "string", "maxLength": 50, "title": "Name", "description": "姓名"}, "phone": {"type": "string", "maxLength": 20, "title": "Phone", "description": "手机号"}, "email": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Email", "description": "邮箱"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/Gender"}, {"type": "null"}], "description": "性别"}, "birthday": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Birthday", "description": "生日"}, "avatar_url": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Avatar Url", "description": "头像URL"}, "wechat_openid": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Wechat Openid", "description": "微信OpenID"}, "wechat_unionid": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Wechat <PERSON>id", "description": "微信UnionID"}, "wechat_nickname": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "微信昵称"}, "wechat_avatar": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Wechat Avatar", "description": "微信头像"}, "member_type": {"$ref": "#/components/schemas/MemberType", "description": "会员类型", "default": "trial"}, "member_status": {"$ref": "#/components/schemas/MemberStatus", "description": "会员状态", "default": "active"}, "source_channel": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Source Channel", "description": "来源渠道"}, "agent_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Agent Id", "description": "代理人员ID"}, "address": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Address", "description": "地址"}, "city": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "City", "description": "城市"}, "province": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Province", "description": "省份"}, "country": {"type": "string", "maxLength": 50, "title": "Country", "description": "国家", "default": "China"}, "postal_code": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Postal Code", "description": "邮编"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "备注"}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "标签"}, "primary_member_card_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Primary Member Card Id", "description": "主要会员卡ID"}, "primary_member_card_name": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Primary Member Card Name", "description": "主要会员卡名称"}, "id": {"type": "integer", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "registered_at": {"type": "string", "format": "date-time", "title": "Registered At"}, "cards": {"anyOf": [{"items": {"$ref": "#/components/schemas/MemberCardSummary"}, "type": "array"}, {"type": "null"}], "title": "Cards", "description": "会员卡列表"}}, "type": "object", "required": ["tenant_id", "name", "phone", "id", "created_at", "registered_at"], "title": "MemberRead", "description": "会员响应模型（包含统计信息）"}, "MemberStatus": {"type": "string", "enum": ["active", "silent", "frozen", "cancelled"], "title": "MemberStatus", "description": "会员状态枚举"}, "MemberType": {"type": "string", "enum": ["trial", "formal", "vip"], "title": "MemberType", "description": "会员类型枚举"}, "MemberUpdate": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/Gender"}, {"type": "null"}]}, "birthday": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Birthday"}, "avatar_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Avatar Url"}, "wechat_openid": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Wechat Openid"}, "wechat_unionid": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Wechat <PERSON>id"}, "wechat_nickname": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "wechat_avatar": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Wechat Avatar"}, "member_type": {"anyOf": [{"$ref": "#/components/schemas/MemberType"}, {"type": "null"}]}, "member_status": {"anyOf": [{"$ref": "#/components/schemas/MemberStatus"}, {"type": "null"}]}, "source_channel": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Source Channel"}, "agent_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Agent Id"}, "address": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Address"}, "city": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "City"}, "province": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Province"}, "country": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Country"}, "postal_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postal Code"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Tags"}}, "type": "object", "title": "MemberUpdate", "description": "更新会员请求模型"}, "MessageResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}}, "type": "object", "title": "MessageResponse", "description": "消息响应模型（无数据）"}, "PageResponse_MemberCardList_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/MemberCardList"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}, "page": {"type": "integer", "title": "Page", "description": "当前页码", "default": 1}, "size": {"type": "integer", "title": "Size", "description": "每页记录数", "default": 20}, "pages": {"type": "integer", "title": "Pages", "description": "总页数", "default": 0}}, "type": "object", "title": "PageResponse[MemberCardList]"}, "PageResponse_MemberCardOperationRead_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/MemberCardOperationRead"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}, "page": {"type": "integer", "title": "Page", "description": "当前页码", "default": 1}, "size": {"type": "integer", "title": "Size", "description": "每页记录数", "default": 20}, "pages": {"type": "integer", "title": "Pages", "description": "总页数", "default": 0}}, "type": "object", "title": "PageResponse[MemberCardOperationRead]"}, "PageResponse_MemberRead_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/MemberRead"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}, "page": {"type": "integer", "title": "Page", "description": "当前页码", "default": 1}, "size": {"type": "integer", "title": "Size", "description": "每页记录数", "default": 20}, "pages": {"type": "integer", "title": "Pages", "description": "总页数", "default": 0}}, "type": "object", "title": "PageResponse[MemberRead]"}, "PageResponse_TeacherList_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/TeacherList"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}, "page": {"type": "integer", "title": "Page", "description": "当前页码", "default": 1}, "size": {"type": "integer", "title": "Size", "description": "每页记录数", "default": 20}, "pages": {"type": "integer", "title": "Pages", "description": "总页数", "default": 0}}, "type": "object", "title": "PageResponse[TeacherList]"}, "PasswordChange": {"properties": {"old_password": {"type": "string", "title": "Old Password"}, "new_password": {"type": "string", "minLength": 6, "title": "New Password"}}, "type": "object", "required": ["old_password", "new_password"], "title": "PasswordChange", "description": "修改密码请求模型"}, "PaymentMethod": {"type": "string", "enum": ["wechat", "alipay", "manual", "bank_transfer"], "title": "PaymentMethod", "description": "支付方式枚举"}, "PlanType": {"type": "string", "enum": ["trial", "basic", "standard", "premium", "enterprise"], "title": "PlanType", "description": "套餐类型枚举"}, "RechargeRequest": {"properties": {"member_card_id": {"type": "integer", "exclusiveMinimum": 0.0, "title": "Member Card Id", "description": "会员卡ID"}, "amount": {"type": "integer", "exclusiveMinimum": 0.0, "title": "Amount", "description": "充值金额（元）"}, "bonus_amount": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Bonus Amount", "description": "赠送金额（元）", "default": 0}, "actual_amount": {"type": "integer", "minimum": 0.0, "title": "Actual Amount", "description": "实收金额（元）", "default": 0}, "payment_method": {"$ref": "#/components/schemas/PaymentMethod", "description": "支付方式"}, "extend_validity_days": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Extend Validity Days", "description": "延长有效期（天）", "default": 0}, "notes": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Notes", "description": "备注信息"}}, "type": "object", "required": ["member_card_id", "amount", "payment_method"], "title": "RechargeRequest", "description": "充值请求模型"}, "RechargeResponse": {"properties": {"operation_id": {"type": "integer", "title": "Operation Id", "description": "操作记录ID"}, "member_card_id": {"type": "integer", "title": "Member Card Id", "description": "会员卡ID"}, "amount": {"type": "integer", "title": "Amount", "description": "充值金额（元）"}, "bonus_amount": {"type": "integer", "title": "Bonus Amount", "description": "赠送金额（元）"}, "total_amount": {"type": "integer", "title": "Total Amount", "description": "总到账金额（元）"}, "balance_before": {"type": "integer", "title": "Balance Before", "description": "充值前余额（元）"}, "balance_after": {"type": "integer", "title": "Balance After", "description": "充值后余额（元）"}, "payment_method": {"$ref": "#/components/schemas/PaymentMethod", "description": "支付方式"}, "transaction_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Transaction Id", "description": "交易ID"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "充值时间"}, "extend_validity_days": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Extend Validity Days", "description": "延长的有效期天数"}, "expires_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expires At", "description": "新的过期时间"}}, "type": "object", "required": ["operation_id", "member_card_id", "amount", "bonus_amount", "total_amount", "balance_before", "balance_after", "payment_method", "transaction_id", "created_at"], "title": "RechargeResponse", "description": "充值响应模型"}, "TagBatchCreate": {"properties": {"category_id": {"type": "integer", "exclusiveMinimum": 0.0, "title": "Category Id", "description": "标签分类ID"}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "标签名称列表"}}, "type": "object", "required": ["category_id", "tags"], "title": "TagBatchCreate", "description": "批量创建标签请求模型"}, "TagBatchUpdate": {"properties": {"tag_ids": {"items": {"type": "integer"}, "type": "array", "title": "Tag Ids", "description": "标签ID列表"}, "status": {"anyOf": [{"$ref": "#/components/schemas/TagStatus"}, {"type": "null"}], "description": "标签状态"}, "category_id": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0.0}, {"type": "null"}], "title": "Category Id", "description": "标签分类ID"}}, "type": "object", "required": ["tag_ids"], "title": "TagBatchUpdate", "description": "批量更新标签请求模型"}, "TagCategoryCreate": {"properties": {"name": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Name", "description": "分类名称"}, "description": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Description", "description": "分类描述"}, "sort_order": {"type": "integer", "title": "Sort Order", "description": "排序顺序", "default": 0}}, "type": "object", "required": ["name"], "title": "TagCategoryCreate", "description": "创建标签分类请求模型"}, "TagCategoryList": {"properties": {"id": {"type": "integer", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "sort_order": {"type": "integer", "title": "Sort Order"}, "tag_count": {"type": "integer", "title": "Tag Count", "description": "该分类下的标签数量", "default": 0}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}}, "type": "object", "required": ["id", "name", "sort_order", "created_at"], "title": "TagCategoryList", "description": "标签分类列表响应模型"}, "TagCategoryRead": {"properties": {"tenant_id": {"type": "integer", "title": "Tenant Id", "description": "租户ID"}, "name": {"type": "string", "maxLength": 50, "title": "Name", "description": "分类名称"}, "description": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Description", "description": "分类描述"}, "sort_order": {"type": "integer", "title": "Sort Order", "description": "排序顺序", "default": 0}, "id": {"type": "integer", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "created_by": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Created By"}}, "type": "object", "required": ["tenant_id", "name", "id", "created_at"], "title": "TagCategoryRead", "description": "标签分类响应模型"}, "TagCategoryUpdate": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 50, "minLength": 1}, {"type": "null"}], "title": "Name", "description": "分类名称"}, "description": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Description", "description": "分类描述"}, "sort_order": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Sort Order", "description": "排序顺序"}}, "type": "object", "title": "TagCategoryUpdate", "description": "更新标签分类请求模型"}, "TagCreate": {"properties": {"name": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Name", "description": "标签名称"}, "category_id": {"type": "integer", "exclusiveMinimum": 0.0, "title": "Category Id", "description": "标签分类ID"}, "description": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Description", "description": "标签描述"}, "status": {"$ref": "#/components/schemas/TagStatus", "description": "标签状态", "default": "active"}}, "type": "object", "required": ["name", "category_id"], "title": "TagCreate", "description": "创建标签请求模型"}, "TagList": {"properties": {"id": {"type": "integer", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "status": {"$ref": "#/components/schemas/TagStatus"}, "category_id": {"type": "integer", "title": "Category Id"}, "category_name": {"type": "string", "title": "Category Name", "description": "分类名称"}}, "type": "object", "required": ["id", "name", "status", "category_id", "category_name"], "title": "TagList", "description": "标签列表响应模型"}, "TagRead": {"properties": {"tenant_id": {"type": "integer", "title": "Tenant Id", "description": "租户ID"}, "category_id": {"type": "integer", "title": "Category Id", "description": "标签分类ID"}, "category_name": {"type": "string", "maxLength": 50, "title": "Category Name", "description": "标签分类名称（冗余字段，用于优化查询）"}, "name": {"type": "string", "maxLength": 50, "title": "Name", "description": "标签名称"}, "description": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Description", "description": "标签描述"}, "status": {"$ref": "#/components/schemas/TagStatus", "description": "标签状态", "default": "active"}, "id": {"type": "integer", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "created_by": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Created By"}}, "type": "object", "required": ["tenant_id", "category_id", "category_name", "name", "id", "created_at"], "title": "TagRead", "description": "标签响应模型"}, "TagStatus": {"type": "string", "enum": ["active", "inactive"], "title": "TagStatus", "description": "标签状态枚举"}, "TagUpdate": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 50, "minLength": 1}, {"type": "null"}], "title": "Name", "description": "标签名称"}, "category_id": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0.0}, {"type": "null"}], "title": "Category Id", "description": "标签分类ID"}, "description": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Description", "description": "标签描述"}, "status": {"anyOf": [{"$ref": "#/components/schemas/TagStatus"}, {"type": "null"}], "description": "标签状态"}}, "type": "object", "title": "TagUpdate", "description": "更新标签请求模型"}, "TagWithTeacherCount": {"properties": {"tenant_id": {"type": "integer", "title": "Tenant Id", "description": "租户ID"}, "category_id": {"type": "integer", "title": "Category Id", "description": "标签分类ID"}, "category_name": {"type": "string", "maxLength": 50, "title": "Category Name", "description": "标签分类名称（冗余字段，用于优化查询）"}, "name": {"type": "string", "maxLength": 50, "title": "Name", "description": "标签名称"}, "description": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Description", "description": "标签描述"}, "status": {"$ref": "#/components/schemas/TagStatus", "description": "标签状态", "default": "active"}, "id": {"type": "integer", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "created_by": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Created By"}, "teacher_count": {"type": "integer", "title": "Teacher Count", "description": "使用该标签的教师数量", "default": 0}}, "type": "object", "required": ["tenant_id", "category_id", "category_name", "name", "id", "created_at"], "title": "TagWithTeacherCount", "description": "带教师数量的标签响应模型"}, "TeacherCategory": {"type": "string", "enum": ["european", "south_african", "filipino", "chinese", "other"], "title": "TeacherCategory", "description": "教师分类枚举"}, "TeacherCreate": {"properties": {"name": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Name", "description": "教师姓名"}, "display_code": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Display Code", "description": "教师显示编号（给会员看的标识）"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/Gender"}, {"type": "null"}], "description": "性别"}, "avatar": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Avatar", "description": "头像URL"}, "phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Phone", "description": "手机号"}, "email": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Email", "description": "邮箱"}, "price_per_class": {"type": "integer", "title": "Price Per Class", "description": "单节课价格（整数，单位：元）", "default": 0}, "teacher_category": {"$ref": "#/components/schemas/TeacherCategory", "description": "教师分类"}, "region": {"$ref": "#/components/schemas/TeacherRegion", "description": "教师区域"}, "wechat_bound": {"type": "boolean", "title": "Wechat Bound", "description": "是否绑定微信", "default": false}, "wechat_openid": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Wechat Openid", "description": "微信OpenID"}, "wechat_unionid": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Wechat <PERSON>id", "description": "微信UnionID"}, "show_to_members": {"type": "boolean", "title": "Show To Members", "description": "是否对会员端展示", "default": true}, "introduction": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Introduction", "description": "教师介绍"}, "teaching_experience": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Teaching Experience", "description": "教学经验(年)"}, "specialties": {"items": {"type": "string"}, "type": "array", "title": "Specialties", "description": "专业特长"}, "certifications": {"items": {"type": "string"}, "type": "array", "title": "Certifications", "description": "资质证书"}, "priority_level": {"type": "integer", "title": "Priority Level", "description": "排课优先级", "default": 0}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "备注"}, "tag_ids": {"items": {"type": "integer"}, "type": "array", "title": "Tag Ids", "description": "标签ID列表"}}, "type": "object", "required": ["name", "teacher_category", "region"], "title": "TeacherCreate", "description": "创建教师请求模型"}, "TeacherDetail": {"properties": {"total_classes": {"type": "integer", "title": "Total Classes", "description": "总上课数", "default": 0}, "completed_classes": {"type": "integer", "title": "Completed Classes", "description": "完成上课数", "default": 0}, "cancelled_classes": {"type": "integer", "title": "Cancelled Classes", "description": "取消上课数", "default": 0}, "no_show_classes": {"type": "integer", "title": "No Show Classes", "description": "缺席上课数", "default": 0}, "total_earnings": {"type": "integer", "title": "Total Earnings", "description": "总收入（元）", "default": 0}, "current_month_earnings": {"type": "integer", "title": "Current Month Earnings", "description": "当月收入（元）", "default": 0}, "avg_rating": {"type": "string", "title": "Avg <PERSON>ing", "description": "平均评分", "default": "0.0"}, "rating_count": {"type": "integer", "title": "Rating Count", "description": "评价次数", "default": 0}, "last_class_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Class At", "description": "最后上课时间"}, "tenant_id": {"type": "integer", "title": "Tenant Id", "description": "租户ID"}, "name": {"type": "string", "maxLength": 50, "title": "Name", "description": "教师姓名"}, "display_code": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Display Code", "description": "教师显示编号（给会员看的标识）"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/Gender"}, {"type": "null"}], "description": "性别"}, "avatar": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Avatar", "description": "头像URL"}, "phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Phone", "description": "手机号"}, "email": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Email", "description": "邮箱"}, "price_per_class": {"type": "integer", "title": "Price Per Class", "description": "单节课价格（整数，单位：元）", "default": 0}, "teacher_category": {"$ref": "#/components/schemas/TeacherCategory", "description": "教师分类"}, "region": {"$ref": "#/components/schemas/TeacherRegion", "description": "教师区域"}, "wechat_bound": {"type": "boolean", "title": "Wechat Bound", "description": "是否绑定微信", "default": false}, "wechat_openid": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Wechat Openid", "description": "微信OpenID"}, "wechat_unionid": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Wechat <PERSON>id", "description": "微信UnionID"}, "show_to_members": {"type": "boolean", "title": "Show To Members", "description": "是否对会员端展示", "default": true}, "status": {"$ref": "#/components/schemas/TeacherStatus", "description": "教师状态", "default": "pending"}, "introduction": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Introduction", "description": "教师介绍"}, "teaching_experience": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Teaching Experience", "description": "教学经验(年)"}, "specialties": {"items": {"type": "string"}, "type": "array", "title": "Specialties", "description": "专业特长"}, "certifications": {"items": {"type": "string"}, "type": "array", "title": "Certifications", "description": "资质证书"}, "priority_level": {"type": "integer", "title": "Priority Level", "description": "排课优先级(数字越大优先级越高)", "default": 0}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "备注"}, "id": {"type": "integer", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "created_by": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Created By"}, "tags": {"items": {"type": "object"}, "type": "array", "title": "Tags", "description": "关联的标签列表"}}, "type": "object", "required": ["tenant_id", "name", "teacher_category", "region", "id", "created_at"], "title": "TeacherDetail", "description": "教师详情响应模型"}, "TeacherList": {"properties": {"id": {"type": "integer", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "display_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Display Code"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/Gender"}, {"type": "null"}]}, "avatar": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Avatar"}, "teacher_category": {"$ref": "#/components/schemas/TeacherCategory"}, "region": {"$ref": "#/components/schemas/TeacherRegion"}, "price_per_class": {"type": "integer", "title": "Price Per Class"}, "status": {"$ref": "#/components/schemas/TeacherStatus"}, "show_to_members": {"type": "boolean", "title": "Show To Members"}, "priority_level": {"type": "integer", "title": "Priority Level"}, "teaching_experience": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Teaching Experience"}, "tag_count": {"type": "integer", "title": "Tag Count", "description": "标签数量", "default": 0}, "tags": {"anyOf": [{"items": {"$ref": "#/components/schemas/TagList"}, "type": "array"}, {"type": "null"}], "title": "Tags", "description": "教师标签列表"}}, "type": "object", "required": ["id", "name", "teacher_category", "region", "price_per_class", "status", "show_to_members", "priority_level"], "title": "TeacherList", "description": "教师列表响应模型"}, "TeacherRead": {"properties": {"total_classes": {"type": "integer", "title": "Total Classes", "description": "总上课数", "default": 0}, "completed_classes": {"type": "integer", "title": "Completed Classes", "description": "完成上课数", "default": 0}, "cancelled_classes": {"type": "integer", "title": "Cancelled Classes", "description": "取消上课数", "default": 0}, "no_show_classes": {"type": "integer", "title": "No Show Classes", "description": "缺席上课数", "default": 0}, "total_earnings": {"type": "integer", "title": "Total Earnings", "description": "总收入（元）", "default": 0}, "current_month_earnings": {"type": "integer", "title": "Current Month Earnings", "description": "当月收入（元）", "default": 0}, "avg_rating": {"type": "string", "title": "Avg <PERSON>ing", "description": "平均评分", "default": "0.0"}, "rating_count": {"type": "integer", "title": "Rating Count", "description": "评价次数", "default": 0}, "last_class_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Class At", "description": "最后上课时间"}, "tenant_id": {"type": "integer", "title": "Tenant Id", "description": "租户ID"}, "name": {"type": "string", "maxLength": 50, "title": "Name", "description": "教师姓名"}, "display_code": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Display Code", "description": "教师显示编号（给会员看的标识）"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/Gender"}, {"type": "null"}], "description": "性别"}, "avatar": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Avatar", "description": "头像URL"}, "phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Phone", "description": "手机号"}, "email": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Email", "description": "邮箱"}, "price_per_class": {"type": "integer", "title": "Price Per Class", "description": "单节课价格（整数，单位：元）", "default": 0}, "teacher_category": {"$ref": "#/components/schemas/TeacherCategory", "description": "教师分类"}, "region": {"$ref": "#/components/schemas/TeacherRegion", "description": "教师区域"}, "wechat_bound": {"type": "boolean", "title": "Wechat Bound", "description": "是否绑定微信", "default": false}, "wechat_openid": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Wechat Openid", "description": "微信OpenID"}, "wechat_unionid": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Wechat <PERSON>id", "description": "微信UnionID"}, "show_to_members": {"type": "boolean", "title": "Show To Members", "description": "是否对会员端展示", "default": true}, "status": {"$ref": "#/components/schemas/TeacherStatus", "description": "教师状态", "default": "pending"}, "introduction": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Introduction", "description": "教师介绍"}, "teaching_experience": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Teaching Experience", "description": "教学经验(年)"}, "specialties": {"items": {"type": "string"}, "type": "array", "title": "Specialties", "description": "专业特长"}, "certifications": {"items": {"type": "string"}, "type": "array", "title": "Certifications", "description": "资质证书"}, "priority_level": {"type": "integer", "title": "Priority Level", "description": "排课优先级(数字越大优先级越高)", "default": 0}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "备注"}, "id": {"type": "integer", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "created_by": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Created By"}}, "type": "object", "required": ["tenant_id", "name", "teacher_category", "region", "id", "created_at"], "title": "TeacherRead", "description": "教师响应模型（包含统计信息）"}, "TeacherRegion": {"type": "string", "enum": ["europe", "north_america", "south_africa", "philippines", "china", "other"], "title": "TeacherRegion", "description": "教师区域枚举"}, "TeacherStatus": {"type": "string", "enum": ["active", "inactive", "pending", "suspended"], "title": "TeacherStatus", "description": "教师状态枚举"}, "TeacherStatusUpdate": {"properties": {"status": {"$ref": "#/components/schemas/TeacherStatus", "description": "新状态"}, "reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reason", "description": "状态变更原因"}}, "type": "object", "required": ["status"], "title": "TeacherStatusUpdate", "description": "教师状态更新模型"}, "TeacherTagAssign": {"properties": {"tag_ids": {"items": {"type": "integer"}, "type": "array", "title": "Tag Ids", "description": "标签ID列表"}}, "type": "object", "required": ["tag_ids"], "title": "TeacherTagAssign", "description": "教师标签分配模型"}, "TeacherTagBatch": {"properties": {"teacher_ids": {"items": {"type": "integer"}, "type": "array", "title": "Teacher Ids", "description": "教师ID列表"}, "tag_ids": {"items": {"type": "integer"}, "type": "array", "title": "Tag Ids", "description": "标签ID列表"}, "operation": {"type": "string", "title": "Operation", "description": "操作类型：add-添加，remove-移除"}}, "type": "object", "required": ["teacher_ids", "tag_ids", "operation"], "title": "TeacherTagBatch", "description": "教师标签批量操作模型"}, "TeacherUpdate": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 50, "minLength": 1}, {"type": "null"}], "title": "Name", "description": "教师姓名"}, "display_code": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Display Code", "description": "教师显示编号（给会员看的标识）"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/Gender"}, {"type": "null"}], "description": "性别"}, "avatar": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Avatar", "description": "头像URL"}, "phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Phone", "description": "手机号"}, "email": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Email", "description": "邮箱"}, "price_per_class": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Price Per Class", "description": "单节课价格"}, "teacher_category": {"anyOf": [{"$ref": "#/components/schemas/TeacherCategory"}, {"type": "null"}], "description": "教师分类"}, "region": {"anyOf": [{"$ref": "#/components/schemas/TeacherRegion"}, {"type": "null"}], "description": "教师区域"}, "wechat_bound": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Wechat Bound", "description": "是否绑定微信"}, "wechat_openid": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Wechat Openid", "description": "微信OpenID"}, "wechat_unionid": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Wechat <PERSON>id", "description": "微信UnionID"}, "show_to_members": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Show To Members", "description": "是否对会员端展示"}, "introduction": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Introduction", "description": "教师介绍"}, "teaching_experience": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Teaching Experience", "description": "教学经验(年)"}, "specialties": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Specialties", "description": "专业特长"}, "certifications": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Certifications", "description": "资质证书"}, "priority_level": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Priority Level", "description": "排课优先级"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "备注"}}, "type": "object", "title": "TeacherUpdate", "description": "更新教师请求模型"}, "TenantRead": {"properties": {"name": {"type": "string", "maxLength": 100, "title": "Name", "description": "机构名称"}, "code": {"type": "string", "maxLength": 50, "title": "Code", "description": "机构代码"}, "display_name": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Display Name", "description": "显示名称"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "机构描述"}, "domain": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Domain", "description": "自定义域名"}, "subdomain": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Subdomain", "description": "子域名"}, "logo_url": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Logo Url", "description": "机构logo"}, "favicon_url": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Favicon <PERSON>", "description": "网站图标"}, "contact_name": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Contact Name", "description": "联系人姓名"}, "contact_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Contact Phone", "description": "联系电话"}, "contact_email": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Contact Email", "description": "联系邮箱"}, "address": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Address", "description": "机构地址"}, "plan_type": {"$ref": "#/components/schemas/PlanType", "description": "套餐类型", "default": "trial"}, "max_teachers": {"type": "integer", "title": "Max Teachers", "description": "最大教师数量", "default": 5}, "max_members": {"type": "integer", "title": "Max Members", "description": "最大会员数量", "default": 50}, "max_storage_gb": {"type": "integer", "title": "Max Storage Gb", "description": "最大存储空间(GB)", "default": 1}, "billing_cycle": {"$ref": "#/components/schemas/BillingCycle", "description": "计费周期", "default": "monthly"}, "price_per_class": {"type": "integer", "title": "Price Per Class", "description": "按课时计费单价", "default": 0}, "monthly_fee": {"type": "integer", "title": "Monthly Fee", "description": "月费", "default": 0}, "setup_fee": {"type": "integer", "title": "<PERSON>up <PERSON>e", "description": "安装费", "default": 0}, "status": {"$ref": "#/components/schemas/TenantStatus", "description": "租户状态", "default": "trial"}, "subscription_expires_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Subscription Expires At", "description": "订阅到期时间"}, "id": {"type": "integer", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "database_schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Database Schema"}, "api_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Key"}, "trial_expires_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Trial Expires At"}}, "type": "object", "required": ["name", "code", "id", "created_at"], "title": "TenantRead", "description": "租户响应模型"}, "TenantStatus": {"type": "string", "enum": ["trial", "active", "suspended", "terminated", "expired"], "title": "TenantStatus", "description": "租户状态枚举"}, "UserCreate": {"properties": {"username": {"type": "string", "maxLength": 50, "minLength": 3, "title": "Username"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}, "password": {"type": "string", "minLength": 6, "title": "Password", "description": "密码"}, "real_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Real Name"}, "role": {"$ref": "#/components/schemas/UserRole"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/Gender"}, {"type": "null"}]}, "birthday": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Birthday"}}, "type": "object", "required": ["username", "password", "role"], "title": "UserCreate", "description": "创建用户请求模型"}, "UserRead": {"properties": {"tenant_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Tenant Id", "description": "租户ID，super_admin为null"}, "username": {"type": "string", "maxLength": 50, "title": "Username", "description": "用户名"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email", "description": "邮箱"}, "phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Phone", "description": "手机号"}, "real_name": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Real Name", "description": "真实姓名"}, "avatar_url": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Avatar Url", "description": "头像URL"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/Gender"}, {"type": "null"}], "description": "性别"}, "birthday": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Birthday", "description": "生日"}, "role": {"$ref": "#/components/schemas/UserRole", "description": "用户角色"}, "status": {"$ref": "#/components/schemas/UserStatus", "description": "用户状态", "default": "active"}, "last_login_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Login At", "description": "最后登录时间"}, "login_count": {"type": "integer", "title": "Login <PERSON>", "description": "登录次数", "default": 0}, "failed_login_attempts": {"type": "integer", "title": "Failed Login Attempts", "description": "失败登录尝试次数", "default": 0}, "locked_until": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Locked Until", "description": "锁定至"}, "id": {"type": "integer", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}}, "type": "object", "required": ["username", "role", "id", "created_at"], "title": "UserRead", "description": "用户响应模型"}, "UserRole": {"type": "string", "enum": ["super_admin", "admin", "agent"], "title": "UserRole", "description": "用户角色枚举"}, "UserStatus": {"type": "string", "enum": ["active", "inactive", "locked"], "title": "UserStatus", "description": "用户状态枚举"}, "UserUpdate": {"properties": {"email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}, "real_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Real Name"}, "avatar_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Avatar Url"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/Gender"}, {"type": "null"}]}, "birthday": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Birthday"}, "status": {"anyOf": [{"$ref": "#/components/schemas/UserStatus"}, {"type": "null"}]}}, "type": "object", "title": "UserUpdate", "description": "更新用户请求模型"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}, "securitySchemes": {"HTTPBearer": {"type": "http", "scheme": "bearer"}}}}