# 充值功能优化说明

## 优化内容

### 1. 赠送金额参数优化

**问题**：之前即使赠送金额为空，也会在API请求中发送 `bonus_amount: null`

**解决方案**：
- 在UI层面进行参数过滤
- 只有当赠送金额有值且大于0时，才将 `bonus_amount` 添加到请求中
- 这样可以避免发送不必要的null值参数

**实现代码**：
```typescript
// 只有当赠送金额有值且大于0时才添加到请求中
if (bonusAmount && Number(bonusAmount) > 0) {
  rechargeData.bonus_amount = Number(bonusAmount);
}
```

### 2. 延长有效期功能

**新增功能**：在充值时可以同时延长会员卡的有效期

**功能特点**：
- 输入延长的天数（可选）
- 显示当前有效期信息
- 只有当输入值大于0时才发送到API
- 支持永久有效的会员卡

**UI设计**：
- 输入框：数字类型，提示"请输入延长天数（可选）"
- 智能提示信息：
  - 默认状态：显示当前有效期状态（灰色文字）
  - 输入延长天数时：实时计算并显示"将延期至：YYYY-MM-DD"（绿色高亮日期）
  - 清空输入时：恢复显示当前有效期状态
- 位置：在支付方式和备注之间

**实现代码**：
```typescript
// 延长有效期状态
const [extendValidityDays, setExtendValidityDays] = useState('');

// 只有当延长有效期有值且大于0时才添加到请求中
if (extendValidityDays && Number(extendValidityDays) > 0) {
  rechargeData.extend_validity_days = Number(extendValidityDays);
}
```

## API支持

充值接口已经支持以下参数：
- `bonus_amount?: number | null` - 赠送金额（可选）
- `extend_validity_days?: number | null` - 延长有效期天数（可选）

## 用户体验改进

1. **更清晰的参数传递**：避免发送无意义的null值
2. **功能集成**：充值和延长有效期可以一次性完成
3. **实时计算反馈**：输入延长天数时立即显示延长后的日期
4. **视觉层次清晰**：使用成功色高亮新的有效期日期
5. **智能状态切换**：根据输入状态动态显示不同的提示信息
6. **表单重置**：操作完成后正确重置所有字段

## 延长有效期功能演示

### 场景1：有有效期的会员卡
- **默认显示**：`当前有效期至：2024-12-31` （灰色文字）
- **输入30天后**：`将延期至：2025-01-30` （绿色高亮日期）
- **清空输入**：恢复显示 `当前有效期至：2024-12-31`

### 场景2：永久有效的会员卡
- **默认显示**：`当前为永久有效` （灰色文字）
- **输入30天后**：`将延期至：2025-02-25` （从今天开始计算，绿色高亮日期）
- **清空输入**：恢复显示 `当前为永久有效`

### 技术实现亮点
```typescript
// 实时计算延长后的日期
if (extendValidityDays && Number(extendValidityDays) > 0) {
  const currentExpiry = card.expires_at
    ? new Date(card.expires_at)
    : new Date(); // 永久有效从今天开始计算

  const extendedDate = new Date(currentExpiry);
  extendedDate.setDate(extendedDate.getDate() + Number(extendValidityDays));

  return (
    <span>
      将延期至：
      <span className="text-success font-medium">
        {format(extendedDate, 'yyyy-MM-dd')}
      </span>
    </span>
  );
}
```

## 测试建议

1. **赠送金额测试**：
   - 不填写赠送金额，确认API请求中不包含 `bonus_amount` 字段
   - 填写赠送金额，确认API请求中包含正确的 `bonus_amount` 值

2. **延长有效期测试**：
   - 不填写延长天数，确认API请求中不包含 `extend_validity_days` 字段
   - 填写延长天数，确认API请求中包含正确的 `extend_validity_days` 值
   - 测试有有效期和永久有效的会员卡

3. **表单重置测试**：
   - 确认充值完成后所有字段都被正确重置
