
# 项目构想
生成一个通用、面向中小公司的中后台管理系统前端脚手架，对接已有的 FastAPI 后端。

## 🛠️ 技术栈

### 核心框架
- **Next.js 14.2.x** - App Router + TypeScript
- **React 18.3.x** - 稳定版本，生态兼容性最佳
- **TypeScript 5.6.x** - 类型安全

### UI 组件库
- **Tailwind CSS 3.4.x** - 原子化 CSS
- **shadcn/ui** - 基于 Radix UI 的组件库
- **lucide-react** - 图标库
- **next-themes** - 主题切换

### 数据管理
- **TanStack Query v5** - 数据获取和缓存
- **TanStack Table v8** - 表格组件
- **Zustand 4.5.x** - 轻量状态管理
- **Axios 1.7.x** - HTTP 请求

### 表单处理
- **React Hook Form 7.53.x** - 表单管理
- **Zod 3.23.x** - 数据验证
- **@hookform/resolvers** - 表单解析器

### 增强功能
- **sonner** - 通知组件
- **cmdk** - 命令面板
- **date-fns** - 日期处理
- **recharts** - 图表库

### 开发工具
- **ESLint 9.x** - 代码检查
- **Prettier 3.3.x** - 代码格式化