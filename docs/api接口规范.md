# 后端api接口规范
📊 响应格式
所有API响应都遵循统一的格式：

成功响应
{
  "success": true,
  "message": "操作成功",
  "http_code": 200,
  "business_code": "SUCCESS",
  "data": { ... },
  "timestamp": "2024-01-01T00:00:00Z"
}
不分页列表响应
{
  "success": true,
  "message": "获取列表成功",
  "http_code": 200,
  "business_code": "SUCCESS",
  "data": [ ... ],
  "total": 10,
  "timestamp": "2024-01-01T00:00:00Z"
}
分页列表响应
{
  "success": true,
  "message": "获取数据成功",
  "http_code": 200,
  "business_code": "SUCCESS",
  "data": [...],
  "total": 100,
  "page": 1,
  "size": 20,
  "pages": 5,
  "timestamp": "2024-01-01T00:00:00Z"
}
错误响应
{
  "success": false,
  "message": "错误描述",
  "http_code": 400,
  "business_code": "SPECIFIC_ERROR_CODE",
  "level": "error",
  "details": { ... },
  "timestamp": "2024-01-01T00:00:00Z"
}
