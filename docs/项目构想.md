# 项目构想
生成一个通用、面向中小公司的中后台管理系统前端脚手架，对接已有的 FastAPI 后端。

技术栈：
- Next.js App Router (TypeScript)
- React
- Tailwind CSS
- shadcn/ui (基于 Radix UI 的组件库)
- lucide-react 图标库
- React Hook Form + Zod (表单处理和验证)
- TanStack Table v8 (表格组件)
- TanStack Query (数据获取和状态管理)
- Axios (HTTP 请求)
- next-themes (主题切换)

API 集成：
- 对接 FastAPI 后端接口 (http://127.0.0.1:3000)
- JWT 认证机制 (登录接口: /api/v1/auth/admin/login)
- 统一的 API 请求封装，处理标准响应格式
- 错误处理和用户提示

布局要求：
- 左侧侧边栏：支持两层嵌套导航，可折叠，当前路由高亮
- 顶部栏：
  ‑ 左侧：当前页面标题（随路由变化）
  ‑ 右侧：搜索图标、管理员头像（带下拉菜单：个人资料 / 退出登录）、主题切换按钮
- 内容区：顶部面包屑 + 实际页面内容

功能要求：
1. 认证功能：
   - 登录页面 (/login)
   - JWT Token 存储和自动刷新
   - 路由保护和权限控制

2. 亮色 / 暗色 / 跟随系统 三档主题切换，使用 next-themes 实现

3. 路由示例：
   /dashboard               → 首页仪表盘
   /users                   → 用户列表（对接 /api/v1/admin/users/ 接口）
   /users/create            → 新增用户（对接 /api/v1/admin/users/ 接口）
   /users/[id]              → 用户详情/编辑
   /members                 → 会员列表（对接 /api/v1/admin/members/ 接口）
   /members/create          → 新增会员
   /members/[id]            → 会员详情/编辑
   /settings                → 设置页面

4. 组件示例：
   - 用户列表页：使用 TanStack Table + 分页 + 搜索（对接真实接口）
   - 新增用户页：使用 React Hook Form + Zod 校验（对接真实接口）

5. 代码结构：
   src/app                  → App Router 目录
   src/components/ui        → shadcn/ui 组件
   src/components/layout    → 侧边栏 + 顶部栏
   src/lib                  → utils、hooks、类型定义
   src/api                  → API 请求函数和类型定义
   src/hooks                → 自定义 hooks (包括数据获取 hooks)
   src/types                → 全局类型定义（基于 OpenAPI 规范）
   src/config               → 导航配置、API 配置等
   src/styles               → 全局 tailwind.css

6. 样式：完全使用 Tailwind 原子类，不要出现任何自定义 CSS 文件

输出格式：
- 完整可运行的 Next.js 项目源码（包含 package.json）

测试登录数据用这个
{
  "username": "demo_admin",
  "password": "demo123456"
}