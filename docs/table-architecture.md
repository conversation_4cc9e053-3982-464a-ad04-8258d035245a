# 📊 表格组件架构设计

## 🏗️ 目录结构

```
src/components/tables/
├── index.ts                      # 统一导出文件
├── common/                       # 通用表格组件
│   ├── compact-data-table.tsx    # 紧凑型数据表格
│   ├── data-table.tsx           # 标准数据表格
│   └── mobile-card-list.tsx     # 通用移动端卡片组件
├── columns/                      # 表格列定义
│   ├── card-template-columns.tsx # 会员卡模板列定义
│   ├── member-columns.tsx        # 会员列定义
│   └── user-columns.tsx          # 用户列定义
└── mobile/                       # 移动端专用组件
    ├── card-template-mobile.tsx  # 会员卡模板移动端
    ├── member-mobile.tsx         # 会员移动端（待创建）
    └── user-mobile.tsx           # 用户移动端（待创建）
```

## 🎯 设计原则

### 1. 关注点分离
- **通用组件**：可复用的基础表格和卡片组件
- **列定义**：特定数据类型的表格列配置
- **移动端组件**：针对特定数据类型的移动端优化

### 2. 复用性优先
- **MobileCardList**：通用的移动端卡片列表组件
- **MobileCardRenderer**：灵活的卡片渲染器接口
- **辅助组件**：MobileCardHeader、MobileCardGrid 等

### 3. 类型安全
- 完整的 TypeScript 类型定义
- 泛型支持，适配不同数据类型
- 接口约束，确保组件正确使用

## 🔧 组件详解

### 通用组件 (common/)

#### MobileCardList<T>
通用的移动端卡片列表组件，支持：
- 泛型数据类型
- 自定义渲染器
- 加载状态
- 空状态处理

```tsx
interface MobileCardRenderer<T> {
  renderHeader: (item: T) => ReactNode;
  renderContent: (item: T) => ReactNode;
  renderFooter?: (item: T) => ReactNode;
  cardClassName?: string;
  contentClassName?: string;
}
```

#### CompactDataTable
紧凑型表格组件，特点：
- 中国用户习惯的紧凑设计
- 响应式列隐藏支持
- 内置排序和分页
- 加载状态处理

### 移动端组件 (mobile/)

#### CardTemplateMobile
会员卡模板的移动端实现：
- 使用 MobileCardList 作为基础
- 实现 MobileCardRenderer 接口
- 针对会员卡模板数据优化
- 包含完整的操作功能

### 列定义 (columns/)

#### createCardTemplateColumns
会员卡模板的表格列定义：
- 响应式列隐藏配置
- 操作按钮集成
- 数据格式化
- 状态显示优化

## 📱 响应式策略

### 断点设计
| 屏幕尺寸 | 组件选择 | 显示策略 |
|---------|---------|----------|
| < 640px | MobileCard | 卡片布局，完整信息 |
| ≥ 640px | DataTable | 表格布局，响应式列 |

### 列隐藏策略
```tsx
// 不同断点隐藏不同重要性的列
meta: { className: 'hidden sm:table-cell' }  // 小屏隐藏
meta: { className: 'hidden md:table-cell' }  // 中屏隐藏
meta: { className: 'hidden lg:table-cell' }  # 大屏隐藏
```

## 🚀 使用指南

### 1. 创建新的数据类型表格

#### 步骤1：定义列
```tsx
// src/components/tables/columns/my-data-columns.tsx
export function createMyDataColumns({ onEdit, onDelete }) {
  return [
    { accessorKey: 'name', header: '名称' },
    // ... 其他列
  ];
}
```

#### 步骤2：创建移动端组件
```tsx
// src/components/tables/mobile/my-data-mobile.tsx
export function MyDataMobile({ data, onEdit, onDelete }) {
  const renderer: MobileCardRenderer<MyDataType> = {
    renderHeader: (item) => (
      <MobileCardHeader
        title={item.name}
        actions={/* 操作按钮 */}
      />
    ),
    renderContent: (item) => (
      <MobileCardGrid items={[/* 数据项 */]} />
    ),
  };

  return <MobileCardList data={data} renderer={renderer} />;
}
```

#### 步骤3：在页面中使用
```tsx
// 响应式布局
<div className="hidden sm:block">
  <CompactDataTable columns={columns} data={data} />
</div>
<div className="block sm:hidden">
  <MyDataMobile data={data} onEdit={onEdit} onDelete={onDelete} />
</div>
```

### 2. 扩展现有组件

#### 添加新的卡片布局组件
```tsx
// src/components/tables/common/mobile-card-list.tsx
export function MobileCardActions({ actions }) {
  return (
    <div className="flex items-center gap-2">
      {actions.map((action, index) => (
        <Button key={index} {...action.props}>
          {action.label}
        </Button>
      ))}
    </div>
  );
}
```

## 🔄 迁移指南

### 从旧架构迁移

#### 更新导入路径
```tsx
// 旧的导入
import { CompactDataTable } from '@/components/tables/compact-data-table';

// 新的导入
import { CompactDataTable } from '@/components/tables/common/compact-data-table';
// 或使用统一导出
import { CompactDataTable } from '@/components/tables';
```

#### 替换专用移动端组件
```tsx
// 旧的专用组件
<MobileCardTemplateList data={data} ... />

// 新的通用组件
<CardTemplateMobile data={data} ... />
```

## 📈 未来扩展

### 计划中的功能
1. **虚拟滚动**：支持大数据量的性能优化
2. **手势操作**：滑动删除、拖拽排序
3. **主题定制**：支持更多视觉主题
4. **国际化**：多语言支持

### 组件扩展点
1. **自定义渲染器**：支持更复杂的卡片布局
2. **插件系统**：可插拔的功能模块
3. **状态管理**：集成全局状态管理

这个架构设计既解决了当前的复用性问题，又为未来的扩展提供了良好的基础。通过清晰的目录结构和组件分层，开发者可以轻松地添加新的数据类型支持，同时保持代码的可维护性。
