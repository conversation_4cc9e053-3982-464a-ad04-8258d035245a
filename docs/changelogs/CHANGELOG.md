# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Fixed

- **API错误处理重复问题** - 修复API Client拦截器、useApiMutation Hook和页面组件三层重复错误提示的问题
- **网络异常处理增强** - 添加离线状态检测，区分网络断开和其他网络错误
- **401认证错误防重复跳转** - 使用全局锁防止并发请求导致的多次登录页跳转
- **错误堆栈信息保留** - 在生产环境中保留完整的错误堆栈和调试信息
- **API错误消息优先显示** - 优化错误处理逻辑，优先显示API返回的错误消息，提供更精确的错误提示

### Added

- **全局错误边界** - 添加React错误边界组件，捕获组件渲染期间的JavaScript错误
  - 全局错误边界：防止整个应用崩溃，显示友好的错误页面
  - 页面级错误边界：局部错误处理，只影响出错的组件区域
  - 错误测试页面：开发环境专用，用于测试各种错误场景
  - 错误监控集成：预留Sentry等错误监控服务的集成接口
  - 修改API Client响应拦截器，只处理系统级错误（401、403、500），业务错误由Hook层统一处理
  - 移除页面组件中重复的toast错误提示和成功提示
  - 清理不再使用的错误处理函数（handleApiError、handleValidationError）
  - 影响文件：
    - `src/lib/api/client.ts` - 优化响应拦截器错误处理策略
    - `src/app/members/create/page.tsx` - 移除重复错误处理
    - `src/app/members/[id]/page.tsx` - 移除重复错误和删除处理
    - `src/app/users/create/page.tsx` - 移除重复成功提示
    - `src/app/users/[id]/page.tsx` - 移除重复成功提示
    - `src/app/users/page.tsx` - 移除重复删除成功提示

### Changed

- **错误处理最佳实践** - 更新开发文档，明确分层错误处理策略
  - 更新 `docs/guide/快速开发指南.md` 添加错误处理最佳实践
  - 更新 `.augment/rules/imported/dev-quick-guid.md` 添加API交互规范示例
  - 建立统一的错误处理原则：API Client层处理系统错误，Hook层处理业务错误，组件层处理UI逻辑
- **错误处理逻辑优化** - 改进useApiMutation钩子，建立明确的错误消息优先级
  - API返回的错误消息 > 自定义错误消息 > 默认错误消息
  - 影响文件：
    - `src/hooks/use-api.ts` - 优化错误处理逻辑
    - `src/lib/api/client.ts` - 完善错误消息提取

### Technical Details

- **问题背景**：用户在API调用失败时会看到2-3个相似的错误提示，严重影响用户体验
- **解决方案**：采用分层错误处理策略，避免重复的toast提示
- **影响范围**：所有使用useApiMutation的业务模块（用户管理、会员管理等）
- **向后兼容**：完全兼容，只是移除了重复的错误提示，不影响功能

## [Previous Versions]

### [1.0.0] - 2025-01-XX

- Initial release
- 基础的用户管理和会员管理功能
- 完整的认证和权限系统
- 响应式UI设计
