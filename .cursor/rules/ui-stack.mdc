---
description: 
globs: 
alwaysApply: false
---
# UI开发规范

## 项目目标用户

- 中后台admin管理系统
- 用户：中国用户，习惯紧凑型中后台系统界面风格
- 已经针对 shadcn/ui 做了定制，以满足类似 Element Plus 的 紧凑型务实风格


## UI 组件库

- **Tailwind CSS 3.4.x** - 原子化 CSS
- **shadcn/ui** - 基于 Radix UI 的组件库
- **lucide-react** - 图标库
- **next-themes** - 主题切换

## 1. 设计系统

- 基于 shadcn/ui 组件库，统一设计语言
- 使用 Tailwind CSS 原子化类，禁止自定义 CSS
- 支持亮色/暗色/系统主题切换
- 响应式设计优先(mobile-first)

### 具体规范：

- 样式 ：100 % Tailwind 原子类   

  目的：与 shadcn/ui 同构，AI、开发同事都能一眼看懂

- 主题：强制使用 next-themes <ThemeProvider>	

  目的：保证所有自定义组件自动继承暗黑/亮色

- 响应式：强制 mobile-first

  用 shadcn/ui 的 `sm:`、`md:` 断点即可

- 单文件组件：一个组件一个文件，index.ts 统一导出

  目的：方便 tree-shaking

- 关于 `components/ui` 的铁律： `components/ui` 是 shadcn/ui 的圣域，禁止手改

  - **永远只放 shadcn/ui 原始组件**（Button、Dialog 等）。

  - **不允许放任何业务自定义组件**，哪怕只是 wrapper。

## 2. 布局规范

```tsx
// 页面布局结构
<MainLayout>
  {/* 页面内容 */}
</MainLayout>

// 布局层级
- 侧边栏: fixed, w-64/w-16, z-40
- 顶部栏: sticky top-0, h-16, z-30  
- 面包屑: sticky top-16, z-20
- 内容区: p-4, 自适应宽度
```

## 3. 组件开发规范

- 组件文件使用 PascalCase 命名
- 优先使用函数式组件和 hooks
- 组件 props 使用 TypeScript 接口定义
- 保持组件单一职责原则