---
description: 
globs: 
alwaysApply: true
---

# 项目结构规范

## 目录结构

本项目采用清晰的目录结构，遵循 Next.js App Router 的最佳实践，同时结合了模块化的组织方式。以下是项目的主要目录结构：

```
src/
├── app/                    # Next.js App Router 页面
│   ├── (auth)/            # 认证路由组
│   │   └── login/         # 登录页面
│   ├── dashboard/         # 仪表盘
│   ├── users/             # 用户管理
│   │   ├── page.tsx       # 用户列表
│   │   ├── create/        # 新增用户
│   │   └── [id]/          # 用户详情/编辑
│   ├── members/           # 会员管理
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 首页
├── components/            # 组件库
│   ├── ui/               # shadcn/ui 基础组件
│   ├── layout/           # 布局组件
│   ├── forms/            # 表单组件
│   ├── tables/           # 表格组件
│   │   ├── common/       # 通用表格组件
│   │   ├── columns/      # 表格列定义
│   │   ├── mobile/       # 移动端专用组件
│   │   └── index.ts      # 统一导出
│   ├── charts/           # 图表组件
│   └── providers/        # 上下文提供者
├── lib/                  # 核心库
│   ├── api/              # API 层
│   ├── auth/             # 认证模块
│   ├── stores/           # 状态管理
│   └── utils/            # 工具函数
├── hooks/                # 自定义 hooks
├── types/                # 类型定义
├── constants/            # 常量定义
└── config/               # 配置文件
```

## 核心目录详解

### 0. 关于 `src/components/ui` 的铁律： `src/components/ui` 是 shadcn/ui 的圣域，禁止手改

- **永远只放 shadcn/ui 原始组件**（Button、Dialog 等）。
- **不允许放任何业务自定义组件**，哪怕只是 wrapper。

### 1. `src/app` - 页面路由

采用 Next.js 14 的 App Router 架构，每个文件夹对应一个路由路径，每个 `page.tsx` 对应一个页面。

### 2. `src/components` - 组件库

包含所有可复用的 UI 组件，按功能分类。

- `ui` - shadcn/ui 基础组件，永远只放 shadcn/ui 原始组件
  - `button.tsx`, `input.tsx` 等基础 UI 组件
- `layout` - 布局组件
  - `sidebar.tsx` - 侧边栏
  - `header.tsx` - 顶部栏
  - `breadcrumb.tsx` - 面包屑导航
  - `main-layout.tsx` - 主布局容器
  - `theme-toggle.tsx` - 主题切换
- `forms` - 表单组件
  - `user-form.tsx` - 用户表单
- `tables` - 表格组件
  - `common` - 通用表格组件
    - `compact-data-table.tsx` - 紧凑型数据表格
    - `data-table.tsx` - 标准数据表格
    - `mobile-card-list.tsx` - 通用移动端卡片组件
  - `columns` - 表格列定义
    - `user-columns.tsx` - 用户表格列
    - `member-columns.tsx` - 会员表格列
    - `card-template-columns.tsx` - 会员卡模板表格列
  - `mobile` - 移动端专用组件
    - `card-template-mobile.tsx` - 会员卡模板移动端
  - `index.ts` - 统一导出
- `providers` - 上下文提供者
  - `query-provider.tsx` - TanStack Query 提供者
  - `theme-provider.tsx` - 主题提供者
  - `toast-provider.tsx` - 通知提供者

## 数据流向

1. **用户交互** → 页面组件 → 自定义 hooks → API 层 → 后端
2. **API 响应** → API 层 → 自定义 hooks → 页面组件 → 用户界面更新
3. **认证状态** → 认证 store → 组件访问 → UI 更新

## 扩展性设计

项目结构设计考虑了扩展性，新增功能模块时：

1. 在 `src/app` 下创建对应路由目录
2. 在 `src/components` 下添加相关组件
3. 在 `src/lib/api` 下添加 API 交互函数
4. 在 `src/hooks` 下创建相应的自定义 hooks
5. 在 `src/types` 下定义相关类型
6. 在 `src/constants` 下添加相关常量
7. 在 `src/config/navigation.ts` 中添加导航项

这种模块化的结构使得新功能可以平滑地集成到现有系统中，同时保持代码的清晰和可维护性。
