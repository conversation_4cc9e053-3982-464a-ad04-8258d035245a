# 中后台管理系统

基于 Next.js 14 的现代化中后台管理系统，对接 FastAPI 后端。

## ✨ 特性

- 🚀 **Next.js 14** - App Router + TypeScript
- 🎨 **shadcn/ui** - 现代化 UI 组件库
- 🔐 **JWT 认证** - 完整的认证系统
- 📱 **响应式设计** - 完美适配各种设备
- 🌙 **主题切换** - 支持亮色/暗色模式
- 📊 **数据表格** - 强大的表格组件
- 🔧 **TypeScript** - 全链路类型安全

## 🚀 快速开始

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

访问 [http://localhost:3005](http://localhost:3005) 查看应用。

## 🛠️ 技术栈

- **框架**: Next.js 14, React 18, TypeScript
- **UI**: Tailwind CSS, shadcn/ui, Lucide React
- **状态管理**: TanStack Query, Zustand
- **表单**: React Hook Form, Zod
- **HTTP**: Axios

## 📁 项目结构

```text
src/
├── app/          # Next.js App Router 页面
├── components/   # UI 组件
├── lib/          # 核心库 (API, 认证, 状态管理)
├── hooks/        # 自定义 hooks
├── types/        # TypeScript 类型定义
└── config/       # 配置文件
```

## 📖 文档

详细的开发文档请查看 [docs/guide/](./docs/guide/) 目录：

- [项目状况概览](./docs/guide/项目状况概览.md)
- [项目结构概览](./docs/guide/项目结构概览.md)
- [页面布局结构](./docs/guide/页面布局结构.md)
- [快速开发指南](./docs/guide/快速开发指南.md)
- [精简版开发文档](./docs/guide/精简版开发文档.md)

## 🔧 开发命令

```bash
npm run dev      # 启动开发服务器
npm run build    # 构建生产版本
npm run start    # 启动生产服务器
npm run lint     # 代码检查
npm run format   # 代码格式化
```

## 🌐 环境配置

创建 `.env.local` 文件：

```env
NEXT_PUBLIC_API_BASE_URL=http://127.0.0.1:3000
NEXT_PUBLIC_APP_NAME=管理系统
```

## 📝 License

MIT
