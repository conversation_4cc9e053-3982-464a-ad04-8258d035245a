/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    // 启用 App Router
    appDir: true,
  },
  // 图片优化配置
  images: {
    domains: [],
    unoptimized: false,
  },
  // 严格模式
  reactStrictMode: true,
  // 启用 SWC 压缩
  swcMinify: true,
  // 环境变量配置
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },
  // 重定向配置
  async redirects() {
    return [
      {
        source: '/',
        destination: '/dashboard',
        permanent: false,
      },
    ];
  },
};

module.exports = nextConfig;
