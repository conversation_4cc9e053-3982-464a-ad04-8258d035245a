import { useApiQuery, useApiMutation } from './use-api';
import {
  getMemberCardsByMember,
  getMemberCardDetail,
  createMemberCard,
  updateMemberCard,
  freezeMemberCard,
  unfreezeMemberCard,
  cancelMemberCard,
  rechargeMemberCard,
  deductMemberCard,
  getMemberCardRechargeHistory,
  getMemberCardConsumptionHistory,
  getCardOperations,
  getCardOperationDetail,
} from '@/lib/api';
import type {
  MemberCardCreate,
  MemberCardUpdate,
  RechargeRequest,
  DeductRequest,
} from '@/types/api';
import { MESSAGES } from '@/constants/messages';

// 直接从对应的API文件引入类型
import type { CardOperationQueryParams } from '@/lib/api/member-cards';

/**
 * 获取指定会员的会员卡列表
 */
export function useMemberCardsByMember(
  memberId: string,
  enabled: boolean = true
) {
  return useApiQuery(
    ['member-cards', 'by-member', memberId],
    () => getMemberCardsByMember(memberId),
    {
      enabled: enabled && !!memberId,
      staleTime: 60 * 1000, // 1分钟
    }
  );
}

/**
 * 获取会员卡详情
 */
export function useMemberCardDetail(id: string, enabled: boolean = true) {
  return useApiQuery(
    ['member-cards', 'detail', id],
    () => getMemberCardDetail(id),
    {
      enabled: enabled && !!id,
    }
  );
}

/**
 * 创建会员卡
 */
export function useCreateMemberCard() {
  return useApiMutation((data: MemberCardCreate) => createMemberCard(data), {
    successMessage: MESSAGES.MEMBER_CARDS.CREATE_SUCCESS,
    errorMessage: MESSAGES.MEMBER_CARDS.CREATE_FAILED,
    invalidateQueries: [
      ['member-cards', 'list'],
      ['member-cards', 'by-member'],
    ],
  });
}

/**
 * 更新会员卡
 */
export function useUpdateMemberCard() {
  return useApiMutation(
    ({ id, data }: { id: string; data: MemberCardUpdate }) =>
      updateMemberCard(id, data),
    {
      successMessage: MESSAGES.MEMBER_CARDS.UPDATE_SUCCESS,
      errorMessage: MESSAGES.MEMBER_CARDS.UPDATE_FAILED,
      invalidateQueries: [
        ['member-cards', 'list'],
        ['member-cards', 'by-member'],
        ['member-cards', 'detail'],
      ],
    }
  );
}

/**
 * 冻结会员卡
 */
export function useFreezeMemberCard() {
  return useApiMutation(
    ({ id, reason }: { id: string; reason?: string }) =>
      freezeMemberCard(id, reason),
    {
      successMessage: MESSAGES.MEMBER_CARDS.FREEZE_SUCCESS,
      errorMessage: MESSAGES.MEMBER_CARDS.FREEZE_FAILED,
      invalidateQueries: [
        ['member-cards', 'list'],
        ['member-cards', 'by-member'],
        ['member-cards', 'detail'],
      ],
    }
  );
}

/**
 * 解冻会员卡
 */
export function useUnfreezeMemberCard() {
  return useApiMutation((id: string) => unfreezeMemberCard(id), {
    successMessage: MESSAGES.MEMBER_CARDS.UNFREEZE_SUCCESS,
    errorMessage: MESSAGES.MEMBER_CARDS.UNFREEZE_FAILED,
    invalidateQueries: [
      ['member-cards', 'list'],
      ['member-cards', 'by-member'],
      ['member-cards', 'detail'],
    ],
  });
}

/**
 * 注销会员卡
 */
export function useCancelMemberCard() {
  return useApiMutation(
    ({ id, reason }: { id: string; reason?: string }) =>
      cancelMemberCard(id, reason),
    {
      successMessage: MESSAGES.MEMBER_CARDS.CANCEL_SUCCESS,
      errorMessage: MESSAGES.MEMBER_CARDS.CANCEL_FAILED,
      invalidateQueries: [
        ['member-cards', 'list'],
        ['member-cards', 'by-member'],
        ['member-cards', 'detail'],
      ],
    }
  );
}

/**
 * 充值会员卡
 */
export function useRechargeMemberCard() {
  return useApiMutation(
    ({ id, data }: { id: string; data: RechargeRequest }) =>
      rechargeMemberCard(id, data),
    {
      successMessage: MESSAGES.MEMBER_CARDS.RECHARGE_SUCCESS,
      errorMessage: MESSAGES.MEMBER_CARDS.RECHARGE_FAILED,
      invalidateQueries: [
        ['member-cards', 'list'],
        ['member-cards', 'by-member'],
        ['member-cards', 'detail'],
        ['member-cards', 'recharge-history'],
      ],
    }
  );
}

/**
 * 扣费会员卡
 */
export function useDeductMemberCard() {
  return useApiMutation(
    ({ id, data }: { id: string; data: DeductRequest }) =>
      deductMemberCard(id, data),
    {
      successMessage: MESSAGES.MEMBER_CARDS.DEDUCT_SUCCESS || '扣费成功',
      errorMessage: MESSAGES.MEMBER_CARDS.DEDUCT_FAILED || '扣费失败',
      invalidateQueries: [
        ['member-cards', 'list'],
        ['member-cards', 'by-member'],
        ['member-cards', 'detail'],
        ['member-cards', 'recharge-history'],
        ['member-cards', 'consumption-history'],
      ],
    }
  );
}

/**
 * 获取会员卡充值历史
 */
export function useMemberCardRechargeHistory(
  id: string,
  limit?: number,
  enabled: boolean = true
) {
  return useApiQuery(
    ['member-cards', 'recharge-history', id],
    () => getMemberCardRechargeHistory(id, limit),
    {
      enabled: enabled && !!id,
    }
  );
}

/**
 * 获取会员卡消费历史
 */
export function useMemberCardConsumptionHistory(
  id: string,
  limit?: number,
  enabled: boolean = true
) {
  return useApiQuery(
    ['member-cards', 'consumption-history', id],
    () => getMemberCardConsumptionHistory(id, limit),
    {
      enabled: enabled && !!id,
    }
  );
}

/**
 * 获取会员卡操作记录
 */
export function useCardOperations(
  params?: CardOperationQueryParams,
  enabled: boolean = true
) {
  return useApiQuery(
    ['member-cards', 'operations', params],
    () => getCardOperations(params),
    {
      enabled,
    }
  );
}

/**
 * 获取会员卡操作记录详情
 */
export function useCardOperationDetail(id: string, enabled: boolean = true) {
  return useApiQuery(
    ['member-cards', 'operation-detail', id],
    () => getCardOperationDetail(id),
    {
      enabled: enabled && !!id,
    }
  );
}
