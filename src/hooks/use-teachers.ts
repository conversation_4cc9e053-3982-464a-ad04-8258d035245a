// 教师管理相关 hooks

import { useApiQuery, useApiMutation } from './use-api';
import {
  getTeachers,
  getAllTeachers,
  getTeacher,
  createTeacher,
  updateTeacher,
  deleteTeacher,
  updateTeacherStatus,
  activateTeacher,
  deactivateTeacher,
  updateTeacherStats,
  getTeacherStatistics,
  getTeachersByPriority,
  getAvailableTeachers,
  searchTeachers,
  getTeacherTagList,
  assignTeacherTags,
  removeTeacherTags,
  batchManageTeacherTagsOperation,
  uploadTeacherAvatar,
  deleteTeacherAvatar,
} from '@/lib/api/teachers';
import { TEACHER_MESSAGES } from '@/constants/teachers';
import type {
  TeacherCreate,
  TeacherUpdate,
  TeacherQueryParams,
  TeacherSearchParams,
  TeacherAvailableParams,
  TeacherStatusUpdate,
  TeacherTagAssign,
  TeacherTagBatch,
} from '@/types/api';

// ==================== 数据查询 hooks ====================

/**
 * 获取教师列表
 */
export function useTeachers(params?: TeacherQueryParams) {
  return useApiQuery(
    ['teachers', 'list', params],
    () => getTeachers(params),
    {
      staleTime: 2 * 60 * 1000, // 2分钟缓存
    }
  );
}

/**
 * 获取所有教师列表
 */
export function useAllTeachers() {
  return useApiQuery(
    ['teachers', 'list'],
    () => getAllTeachers(),
    {
      staleTime: 2 * 60 * 1000, // 2分钟缓存
    }
  );
}


/**
 * 获取教师详情
 */
export function useTeacher(id: string) {
  return useApiQuery(
    ['teachers', 'detail', id],
    () => getTeacher(id),
    {
      enabled: !!id,
      staleTime: 5 * 60 * 1000, // 5分钟缓存
    }
  );
}

/**
 * 获取教师统计信息
 */
export function useTeacherStatistics() {
  return useApiQuery(
    ['teachers', 'statistics'],
    () => getTeacherStatistics(),
    {
      staleTime: 10 * 60 * 1000, // 10分钟缓存
    }
  );
}

/**
 * 按优先级获取教师
 */
export function useTeachersByPriority(limit?: number) {
  return useApiQuery(
    ['teachers', 'priority', limit],
    () => getTeachersByPriority(limit),
    {
      staleTime: 5 * 60 * 1000, // 5分钟缓存
    }
  );
}

/**
 * 获取对会员可见的教师
 */
export function useAvailableTeachers(params?: TeacherAvailableParams) {
  return useApiQuery(
    ['teachers', 'available', params],
    () => getAvailableTeachers(params),
    {
      staleTime: 5 * 60 * 1000, // 5分钟缓存
    }
  );
}

/**
 * 搜索教师
 */
export function useSearchTeachers(params: TeacherSearchParams) {
  return useApiQuery(
    ['teachers', 'search', params],
    () => searchTeachers(params),
    {
      enabled: !!params.keyword,
      staleTime: 1 * 60 * 1000, // 1分钟缓存
    }
  );
}

/**
 * 获取教师标签
 */
export function useTeacherTags(id: string) {
  return useApiQuery(
    ['teachers', 'tags', id],
    () => getTeacherTagList(id),
    {
      enabled: !!id,
      staleTime: 5 * 60 * 1000, // 5分钟缓存
    }
  );
}

// ==================== 数据变更 hooks ====================

/**
 * 创建教师
 */
export function useCreateTeacher() {
  return useApiMutation(
    (data: TeacherCreate) => createTeacher(data),
    {
      successMessage: TEACHER_MESSAGES.CREATE_SUCCESS,
      errorMessage: TEACHER_MESSAGES.CREATE_FAILED,
      invalidateQueries: [
        ['teachers', 'list'],
        ['teachers', 'statistics'],
        ['teachers', 'priority'],
        ['teachers', 'available'],
      ],
    }
  );
}

/**
 * 更新教师信息
 */
export function useUpdateTeacher() {
  return useApiMutation(
    ({ id, data }: { id: string; data: TeacherUpdate }) => updateTeacher(id, data),
    {
      successMessage: TEACHER_MESSAGES.UPDATE_SUCCESS,
      errorMessage: TEACHER_MESSAGES.UPDATE_FAILED,
      invalidateQueries: [
        ['teachers', 'list'],
        ['teachers', 'detail'],
        ['teachers', 'statistics'],
        ['teachers', 'priority'],
        ['teachers', 'available'],
      ],
    }
  );
}

/**
 * 删除教师
 */
export function useDeleteTeacher() {
  return useApiMutation(
    (id: string) => deleteTeacher(id),
    {
      successMessage: TEACHER_MESSAGES.DELETE_SUCCESS,
      errorMessage: TEACHER_MESSAGES.DELETE_FAILED,
      invalidateQueries: [
        ['teachers', 'list'],
        ['teachers', 'statistics'],
        ['teachers', 'priority'],
        ['teachers', 'available'],
      ],
    }
  );
}

/**
 * 更新教师状态
 */
export function useUpdateTeacherStatus() {
  return useApiMutation(
    ({ id, data }: { id: string; data: TeacherStatusUpdate }) => updateTeacherStatus(id, data),
    {
      successMessage: TEACHER_MESSAGES.STATUS_UPDATE_SUCCESS,
      errorMessage: TEACHER_MESSAGES.STATUS_UPDATE_FAILED,
      invalidateQueries: [
        ['teachers', 'list'],
        ['teachers', 'detail'],
        ['teachers', 'statistics'],
        ['teachers', 'priority'],
        ['teachers', 'available'],
      ],
    }
  );
}

/**
 * 激活教师
 */
export function useActivateTeacher() {
  return useApiMutation(
    (id: string) => activateTeacher(id),
    {
      successMessage: TEACHER_MESSAGES.ACTIVATE_SUCCESS,
      errorMessage: TEACHER_MESSAGES.ACTIVATE_FAILED,
      invalidateQueries: [
        ['teachers', 'list'],
        ['teachers', 'detail'],
        ['teachers', 'statistics'],
        ['teachers', 'priority'],
        ['teachers', 'available'],
      ],
    }
  );
}

/**
 * 停用教师
 */
export function useDeactivateTeacher() {
  return useApiMutation(
    (id: string) => deactivateTeacher(id),
    {
      successMessage: TEACHER_MESSAGES.DEACTIVATE_SUCCESS,
      errorMessage: TEACHER_MESSAGES.DEACTIVATE_FAILED,
      invalidateQueries: [
        ['teachers', 'list'],
        ['teachers', 'detail'],
        ['teachers', 'statistics'],
        ['teachers', 'priority'],
        ['teachers', 'available'],
      ],
    }
  );
}

/**
 * 更新教师统计信息
 */
export function useUpdateTeacherStats() {
  return useApiMutation(
    ({ id, params }: { id: string; params: { class_completed?: boolean; class_cancelled?: boolean; class_no_show?: boolean; earnings_added?: number } }) => updateTeacherStats(id, params),
    {
      successMessage: TEACHER_MESSAGES.STATS_UPDATE_SUCCESS,
      errorMessage: TEACHER_MESSAGES.STATS_UPDATE_FAILED,
      invalidateQueries: [
        ['teachers', 'list'],
        ['teachers', 'detail'],
        ['teachers', 'statistics'],
      ],
    }
  );
}

// ==================== 标签管理 hooks ====================

/**
 * 分配教师标签
 */
export function useAssignTeacherTags() {
  return useApiMutation(
    ({ id, data }: { id: string; data: TeacherTagAssign }) => assignTeacherTags(id, data),
    {
      successMessage: TEACHER_MESSAGES.TAG_ASSIGN_SUCCESS,
      errorMessage: TEACHER_MESSAGES.TAG_ASSIGN_FAILED,
      invalidateQueries: [
        ['teachers', 'list'],
        ['teachers', 'detail'],
        ['teachers', 'tags'],
      ],
    }
  );
}

/**
 * 移除教师标签
 */
export function useRemoveTeacherTags() {
  return useApiMutation(
    ({ id, tagIds }: { id: string; tagIds: number[] }) => removeTeacherTags(id, tagIds),
    {
      successMessage: TEACHER_MESSAGES.TAG_REMOVE_SUCCESS,
      errorMessage: TEACHER_MESSAGES.TAG_REMOVE_FAILED,
      invalidateQueries: [
        ['teachers', 'list'],
        ['teachers', 'detail'],
        ['teachers', 'tags'],
      ],
    }
  );
}

/**
 * 批量管理教师标签
 */
export function useBatchManageTeacherTags() {
  return useApiMutation(
    (data: TeacherTagBatch) => batchManageTeacherTagsOperation(data),
    {
      successMessage: TEACHER_MESSAGES.BATCH_TAG_SUCCESS,
      errorMessage: TEACHER_MESSAGES.BATCH_TAG_FAILED,
      invalidateQueries: [
        ['teachers', 'list'],
        ['teachers', 'detail'],
        ['teachers', 'tags'],
      ],
    }
  );
}

// ==================== 头像管理 hooks ====================

/**
 * 上传教师头像
 */
export function useUploadTeacherAvatar() {
  return useApiMutation(
    ({ id, file }: { id: string; file: File }) => uploadTeacherAvatar(id, file),
    {
      successMessage: TEACHER_MESSAGES.AVATAR_UPLOAD_SUCCESS,
      errorMessage: TEACHER_MESSAGES.AVATAR_UPLOAD_FAILED,
      invalidateQueries: [
        ['teachers', 'list'],
        ['teachers', 'detail'],
      ],
    }
  );
}

/**
 * 删除教师头像
 */
export function useDeleteTeacherAvatar() {
  return useApiMutation(
    (id: string) => deleteTeacherAvatar(id),
    {
      successMessage: TEACHER_MESSAGES.AVATAR_DELETE_SUCCESS,
      errorMessage: TEACHER_MESSAGES.AVATAR_DELETE_FAILED,
      invalidateQueries: [
        ['teachers', 'list'],
        ['teachers', 'detail'],
      ],
    }
  );
}
