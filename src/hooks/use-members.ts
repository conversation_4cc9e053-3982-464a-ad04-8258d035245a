import { useApiQuery, useApiMutation } from './use-api';
import {
  getMemberList,
  getMemberDetail,
  createMember,
  updateMember,
  deleteMember,
  MemberListParams,
} from '@/lib/api/members';
import type { MemberCreate, MemberUpdate } from '@/types/api';
import { MESSAGES } from '@/constants/messages';

/**
 * 获取会员列表
 */
export function useMemberList(params?: MemberListParams) {
  return useApiQuery(['members', 'list', params], () => getMemberList(params), {
    staleTime: 2 * 60 * 1000, // 2分钟
  });
}

/**
 * 获取会员详情
 */
export function useMemberDetail(id: string, enabled: boolean = true) {
  return useApiQuery(['members', 'detail', id], () => getMemberDetail(id), {
    enabled: enabled && !!id,
  });
}

/**
 * 创建会员
 */
export function useCreateMember() {
  return useApiMutation((data: MemberCreate) => createMember(data), {
    successMessage: MESSAGES.MEMBERS.CREATE_SUCCESS,
    errorMessage: MESSAGES.MEMBERS.CREATE_FAILED,
    invalidateQueries: [['members', 'list']],
  });
}

/**
 * 更新会员
 */
export function useUpdateMember() {
  return useApiMutation(
    ({ id, data }: { id: string; data: MemberUpdate }) =>
      updateMember(id, data),
    {
      successMessage: MESSAGES.MEMBERS.UPDATE_SUCCESS,
      errorMessage: MESSAGES.MEMBERS.UPDATE_FAILED,
      invalidateQueries: [
        ['members', 'list'],
        ['members', 'detail'],
      ],
    }
  );
}

/**
 * 删除会员
 */
export function useDeleteMember() {
  return useApiMutation((id: string) => deleteMember(id), {
    successMessage: MESSAGES.MEMBERS.DELETE_SUCCESS,
    errorMessage: MESSAGES.MEMBERS.DELETE_FAILED,
    invalidateQueries: [['members', 'list']],
  });
}

/**
 * 解绑微信
 */
export function useUnbindWechat() {
  return useApiMutation(
    (id: string) => updateMember(id, { wechat_avatar: null }),
    {
      successMessage: '微信解绑成功',
      errorMessage: '微信解绑失败',
      invalidateQueries: [
        ['members', 'list'],
        ['members', 'detail'],
      ],
    }
  );
}
