import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { AxiosError } from 'axios';

/**
 * API 查询 hook
 */
export function useApiQuery<T>(
  queryKey: (string | number | object | undefined)[],
  queryFn: () => Promise<T>,
  options?: {
    enabled?: boolean;
    staleTime?: number;
    cacheTime?: number;
  }
) {
  return useQuery({
    queryKey,
    queryFn,
    staleTime: options?.staleTime || 5 * 60 * 1000, // 5分钟
    gcTime: options?.cacheTime || 10 * 60 * 1000, // 10分钟
    enabled: options?.enabled,
  });
}

/**
 * API 变更 hook
 */
export function useApiMutation<TData, TVariables>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  options?: {
    onSuccess?: (data: TData, variables: TVariables) => void;
    onError?: (error: Error, variables: TVariables) => void;
    invalidateQueries?: string[][];
    successMessage?: string;
    errorMessage?: string;
  }
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn,
    onSuccess: (data, variables) => {
      // 显示成功消息
      if (options?.successMessage) {
        toast.success(options.successMessage);
      }

      // 刷新相关查询
      if (options?.invalidateQueries) {
        options.invalidateQueries.forEach(queryKey => {
          queryClient.invalidateQueries({ queryKey });
        });
      }

      // 执行自定义成功回调
      options?.onSuccess?.(data, variables);
    },
    onError: (error: unknown, variables) => {
      // 错误消息优先级：
      // 1. API返回的错误消息（通过Error.message或response.data.message）
      // 2. 自定义错误消息（options.errorMessage）
      // 3. 默认错误消息（"操作失败"）
      let errorMessage = '';

      // 从Error对象中获取消息（API客户端已经处理过的业务错误）
      if (error instanceof Error) {
        const message = error.message;
        // 排除默认的Axios错误消息格式
        if (message && !message.startsWith('Request failed with status code')) {
          errorMessage = message;
        }
      }

      // 如果没有获取到有效的错误消息，尝试从Axios错误响应中获取
      if (
        !errorMessage &&
        error instanceof AxiosError &&
        error.response?.data
      ) {
        const apiError = error.response.data;
        if (apiError.message) {
          errorMessage = apiError.message;
        }
      }

      // 如果仍然没有错误消息，使用自定义错误消息或默认消息
      if (!errorMessage) {
        errorMessage = options?.errorMessage || '操作失败';
      }

      // 显示错误消息
      toast.error(errorMessage);

      // 执行自定义错误回调
      if (error instanceof Error && options?.onError) {
        options.onError(error, variables);
      }
    },
  });
}
