import { useApiQuery, useApiMutation } from './use-api';
import {
  getTagCategories,
  getTagCategory,
  createTagCategory,
  updateTagCategory,
  deleteTagCategory,
  getTagsByCategory,
  getTags,
  getActiveTags,
  getTag,
  createTag,
  updateTag,
  deleteTag,
  batchCreateTags,
  batchUpdateTags,
  getTeacherTags,
  assignTagsToTeacher,
  removeTagsFromTeacher,
  batchManageTeacherTags,
  TagCategoryListParams,
  TagListParams,
  TagsByCategoryParams,
} from '@/lib/api/tags';
import type {
  TagCategoryCreate,
  TagCategoryUpdate,
  TagCreate,
  TagUpdate,
  TagBatchCreate,
  TagBatchUpdate,
  TeacherTagAssign,
  TeacherTagBatch,
} from '@/types/api';
import { MESSAGES } from '@/constants/messages';

// ==================== 标签分类相关 hooks ====================

/**
 * 获取标签分类列表
 */
export function useTagCategories(params?: TagCategoryListParams) {
  return useApiQuery(
    ['tag-categories', 'list', params],
    () => getTagCategories(params),
    {
      staleTime: 2 * 60 * 1000, // 2分钟
    }
  );
}

/**
 * 获取标签分类详情
 */
export function useTagCategory(id: string, enabled: boolean = true) {
  return useApiQuery(
    ['tag-categories', 'detail', id],
    () => getTagCategory(id),
    {
      enabled: enabled && !!id,
    }
  );
}

/**
 * 创建标签分类
 */
export function useCreateTagCategory() {
  return useApiMutation(
    (data: TagCategoryCreate) => createTagCategory(data),
    {
      successMessage: MESSAGES.TAGS.CATEGORY_CREATE_SUCCESS,
      errorMessage: MESSAGES.TAGS.CATEGORY_CREATE_FAILED,
      invalidateQueries: [['tag-categories', 'list']],
    }
  );
}

/**
 * 更新标签分类
 */
export function useUpdateTagCategory() {
  return useApiMutation(
    ({ id, data }: { id: string; data: TagCategoryUpdate }) =>
      updateTagCategory(id, data),
    {
      successMessage: MESSAGES.TAGS.CATEGORY_UPDATE_SUCCESS,
      errorMessage: MESSAGES.TAGS.CATEGORY_UPDATE_FAILED,
      invalidateQueries: [
        ['tag-categories', 'list'],
        ['tag-categories', 'detail'],
      ],
    }
  );
}

/**
 * 删除标签分类
 */
export function useDeleteTagCategory() {
  return useApiMutation((id: string) => deleteTagCategory(id), {
    successMessage: MESSAGES.TAGS.CATEGORY_DELETE_SUCCESS,
    errorMessage: MESSAGES.TAGS.CATEGORY_DELETE_FAILED,
    invalidateQueries: [['tag-categories', 'list']],
  });
}

/**
 * 获取指定分类下的标签
 */
export function useTagsByCategory(
  categoryId: string,
  params?: TagsByCategoryParams,
  enabled: boolean = true
) {
  return useApiQuery(
    ['tags', 'by-category', categoryId, params],
    () => getTagsByCategory(categoryId, params),
    {
      enabled: enabled && !!categoryId,
      staleTime: 2 * 60 * 1000, // 2分钟
    }
  );
}

// ==================== 标签相关 hooks ====================

/**
 * 获取标签列表
 */
export function useTags(params?: TagListParams) {
  return useApiQuery(['tags', 'list', params], () => getTags(params), {
    staleTime: 2 * 60 * 1000, // 2分钟
  });
}

/**
 * 获取激活状态的标签
 */
export function useActiveTags() {
  return useApiQuery(['tags', 'active'], () => getActiveTags(), {
    staleTime: 5 * 60 * 1000, // 5分钟
  });
}

/**
 * 获取标签详情
 */
export function useTag(id: string, enabled: boolean = true) {
  return useApiQuery(['tags', 'detail', id], () => getTag(id), {
    enabled: enabled && !!id,
  });
}

/**
 * 创建标签
 */
export function useCreateTag() {
  return useApiMutation((data: TagCreate) => createTag(data), {
    successMessage: MESSAGES.TAGS.CREATE_SUCCESS,
    errorMessage: MESSAGES.TAGS.CREATE_FAILED,
    invalidateQueries: [
      ['tags', 'list'],
      ['tags', 'by-category'],
      ['tags', 'active'],
    ],
  });
}

/**
 * 更新标签
 */
export function useUpdateTag() {
  return useApiMutation(
    ({ id, data }: { id: string; data: TagUpdate }) => updateTag(id, data),
    {
      successMessage: MESSAGES.TAGS.UPDATE_SUCCESS,
      errorMessage: MESSAGES.TAGS.UPDATE_FAILED,
      invalidateQueries: [
        ['tags', 'list'],
        ['tags', 'detail'],
        ['tags', 'by-category'],
        ['tags', 'active'],
      ],
    }
  );
}

/**
 * 删除标签
 */
export function useDeleteTag() {
  return useApiMutation((id: string) => deleteTag(id), {
    successMessage: MESSAGES.TAGS.DELETE_SUCCESS,
    errorMessage: MESSAGES.TAGS.DELETE_FAILED,
    invalidateQueries: [
      ['tags', 'list'],
      ['tags', 'by-category'],
      ['tags', 'active'],
    ],
  });
}

/**
 * 批量创建标签
 */
export function useBatchCreateTags() {
  return useApiMutation((data: TagBatchCreate) => batchCreateTags(data), {
    successMessage: MESSAGES.TAGS.BATCH_CREATE_SUCCESS,
    errorMessage: MESSAGES.TAGS.BATCH_CREATE_FAILED,
    invalidateQueries: [
      ['tags', 'list'],
      ['tags', 'by-category'],
      ['tags', 'active'],
    ],
  });
}

/**
 * 批量更新标签
 */
export function useBatchUpdateTags() {
  return useApiMutation((data: TagBatchUpdate) => batchUpdateTags(data), {
    successMessage: MESSAGES.TAGS.BATCH_UPDATE_SUCCESS,
    errorMessage: MESSAGES.TAGS.BATCH_UPDATE_FAILED,
    invalidateQueries: [
      ['tags', 'list'],
      ['tags', 'by-category'],
      ['tags', 'active'],
    ],
  });
}

// ==================== 教师标签关联 hooks ====================

/**
 * 获取教师的标签列表
 */
export function useTeacherTags(teacherId: string, enabled: boolean = true) {
  return useApiQuery(
    ['teachers', 'tags', teacherId],
    () => getTeacherTags(teacherId),
    {
      enabled: enabled && !!teacherId,
      staleTime: 2 * 60 * 1000, // 2分钟
    }
  );
}

/**
 * 为教师分配标签
 */
export function useAssignTagsToTeacher() {
  return useApiMutation(
    ({ teacherId, data }: { teacherId: string; data: TeacherTagAssign }) =>
      assignTagsToTeacher(teacherId, data),
    {
      successMessage: MESSAGES.TAGS.ASSIGN_SUCCESS,
      errorMessage: MESSAGES.TAGS.ASSIGN_FAILED,
      invalidateQueries: [['teachers', 'tags']],
    }
  );
}

/**
 * 移除教师标签
 */
export function useRemoveTagsFromTeacher() {
  return useApiMutation(
    ({ teacherId, tagIds }: { teacherId: string; tagIds: number[] }) =>
      removeTagsFromTeacher(teacherId, tagIds),
    {
      successMessage: MESSAGES.TAGS.REMOVE_SUCCESS,
      errorMessage: MESSAGES.TAGS.REMOVE_FAILED,
      invalidateQueries: [['teachers', 'tags']],
    }
  );
}

/**
 * 批量管理教师标签
 */
export function useBatchManageTeacherTags() {
  return useApiMutation((data: TeacherTagBatch) => batchManageTeacherTags(data), {
    successMessage: MESSAGES.TAGS.BATCH_ASSIGN_SUCCESS,
    errorMessage: MESSAGES.TAGS.BATCH_ASSIGN_FAILED,
    invalidateQueries: [['teachers', 'tags']],
  });
}
