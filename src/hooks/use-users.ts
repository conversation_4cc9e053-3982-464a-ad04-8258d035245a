import { useApiQuery, useApiMutation } from './use-api';
import {
  getUserList,
  getUserDetail,
  createUser,
  updateUser,
  deleteUser,
  getSalesmenList,
  UserListParams,
} from '@/lib/api/users';
import type { UserCreate, UserUpdate } from '@/types/api';
import { MESSAGES } from '@/constants/messages';

/**
 * 获取用户列表
 */
export function useUserList(params?: UserListParams) {
  return useApiQuery(['users', 'list', params], () => getUserList(params), {
    staleTime: 2 * 60 * 1000, // 2分钟
  });
}

/**
 * 获取用户详情
 */
export function useUserDetail(id: string, enabled: boolean = true) {
  return useApiQuery(['users', 'detail', id], () => getUserDetail(id), {
    enabled: enabled && !!id,
  });
}

/**
 * 创建用户
 */
export function useCreateUser() {
  return useApiMutation((data: UserCreate) => createUser(data), {
    successMessage: MESSAGES.USERS.CREATE_SUCCESS,
    errorMessage: MESSAGES.USERS.CREATE_FAILED,
    invalidateQueries: [['users', 'list']],
  });
}

/**
 * 更新用户
 */
export function useUpdateUser() {
  return useApiMutation(
    ({ id, data }: { id: string; data: UserUpdate }) => updateUser(id, data),
    {
      successMessage: MESSAGES.USERS.UPDATE_SUCCESS,
      errorMessage: MESSAGES.USERS.UPDATE_FAILED,
      invalidateQueries: [
        ['users', 'list'],
        ['users', 'detail'],
      ],
    }
  );
}

/**
 * 删除用户
 */
export function useDeleteUser() {
  return useApiMutation((id: string) => deleteUser(id), {
    successMessage: MESSAGES.USERS.DELETE_SUCCESS,
    errorMessage: MESSAGES.USERS.DELETE_FAILED,
    invalidateQueries: [['users', 'list']],
  });
}

/**
 * 获取销售人员列表
 */
export function useSalesmenList() {
  return useApiQuery(['users', 'salesmen'], () => getSalesmenList(), {
    staleTime: 5 * 60 * 1000, // 5分钟
  });
}
