import { useApiQuery, useApiMutation } from './use-api';
import {
  getCardTemplateList,
  getCardTemplateDetail,
  createCardTemplate,
  updateCardTemplate,
  deleteCardTemplate,
  toggleCardTemplateStatus,
} from '@/lib/api/card-templates';
import type { CardTemplateCreate, CardTemplateUpdate } from '@/types/api';
import { MESSAGES } from '@/constants/messages';

/**
 * 获取会员卡模板列表
 */
export function useCardTemplateList() {
  return useApiQuery(['card-templates', 'list'], () => getCardTemplateList(), {
    staleTime: 2 * 60 * 1000, // 2分钟
  });
}

/**
 * 获取会员卡模板详情
 */
export function useCardTemplateDetail(id: string, enabled: boolean = true) {
  return useApiQuery(
    ['card-templates', 'detail', id],
    () => getCardTemplateDetail(id),
    {
      enabled: enabled && !!id,
    }
  );
}

/**
 * 创建会员卡模板
 */
export function useCreateCardTemplate() {
  return useApiMutation(
    (data: CardTemplateCreate) => createCardTemplate(data),
    {
      successMessage: MESSAGES.CARD_TEMPLATES.CREATE_SUCCESS,
      errorMessage: MESSAGES.CARD_TEMPLATES.CREATE_FAILED,
      invalidateQueries: [['card-templates', 'list']],
    }
  );
}

/**
 * 更新会员卡模板
 */
export function useUpdateCardTemplate() {
  return useApiMutation(
    ({ id, data }: { id: string; data: CardTemplateUpdate }) =>
      updateCardTemplate(id, data),
    {
      successMessage: MESSAGES.CARD_TEMPLATES.UPDATE_SUCCESS,
      errorMessage: MESSAGES.CARD_TEMPLATES.UPDATE_FAILED,
      invalidateQueries: [
        ['card-templates', 'list'],
        ['card-templates', 'detail'],
      ],
    }
  );
}

/**
 * 删除会员卡模板
 */
export function useDeleteCardTemplate() {
  return useApiMutation((id: string) => deleteCardTemplate(id), {
    successMessage: MESSAGES.CARD_TEMPLATES.DELETE_SUCCESS,
    errorMessage: MESSAGES.CARD_TEMPLATES.DELETE_FAILED,
    invalidateQueries: [['card-templates', 'list']],
  });
}

/**
 * 切换会员卡模板状态
 */
export function useToggleCardTemplateStatus() {
  return useApiMutation((id: string) => toggleCardTemplateStatus(id), {
    successMessage: MESSAGES.CARD_TEMPLATES.TOGGLE_STATUS_SUCCESS,
    errorMessage: MESSAGES.CARD_TEMPLATES.TOGGLE_STATUS_FAILED,
    invalidateQueries: [
      ['card-templates', 'list'],
      ['card-templates', 'detail'],
    ],
  });
}
