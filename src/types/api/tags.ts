// 标签状态枚举
export enum TagStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

// 标签分类基础类型
export interface TagCategoryBase {
  name: string;
  description?: string;
}

// 标签分类创建类型
export type TagCategoryCreate = TagCategoryBase;

// 标签分类更新类型
export type TagCategoryUpdate = Partial<TagCategoryBase>;

// 标签分类读取类型
export interface TagCategoryRead extends TagCategoryBase {
  id: number;
  created_at: string;
  updated_at: string;
}

// 标签分类列表项类型
export interface TagCategoryList extends TagCategoryRead {
  tag_count?: number; // 该分类下的标签数量
}

// 标签基础类型
export interface TagBase {
  name: string;
  category_id: number;
  description?: string;
  color?: string;
  status?: TagStatus;
}

// 标签创建类型
export interface TagCreate extends TagBase {
  status?: TagStatus;
}

// 标签更新类型
export interface TagUpdate extends Partial<Omit<TagBase, 'category_id'>> {
  category_id?: number;
}

// 标签读取类型
export interface TagRead extends TagBase {
  id: number;
  status: TagStatus;
  created_at: string;
  updated_at: string;
}

// 标签列表项类型
export type TagList = TagRead;

// 带分类信息的标签类型
export interface TagWithCategory extends TagRead {
  category: {
    id: number;
    name: string;
  };
}

// 批量创建标签类型
export interface TagBatchCreate {
  category_id: number;
  tags: Array<{
    name: string;
    description?: string;
    color?: string;
    status?: TagStatus;
  }>;
}

// 批量更新标签类型
export interface TagBatchUpdate {
  tag_ids: number[];
  updates: {
    category_id?: number;
    status?: TagStatus;
    color?: string;
  };
}

// 标签查询参数
export interface TagQueryParams {
  name?: string;
  category_id?: number;
  status?: TagStatus;
}

// 标签分类查询参数
export interface TagCategoryQueryParams {
  name?: string;
}

// 教师标签分配类型
export interface TeacherTagAssign {
  tag_ids: number[];
}

// 教师标签批量管理类型
export interface TeacherTagBatch {
  teacher_ids: number[];
  tag_ids: number[];
  action: 'assign' | 'remove';
}

// API响应类型
export interface TagCategoryResponse {
  success: boolean;
  data: TagCategoryRead;
  message: string;
}

export interface TagCategoryListResponse {
  success: boolean;
  data: TagCategoryList[];
  total: number;
  message: string;
}

export interface TagResponse {
  success: boolean;
  data: TagRead;
  message: string;
}

export interface TagListResponse {
  success: boolean;
  data: TagList[];
  total: number;
  message: string;
}

export interface TagWithCategoryListResponse {
  success: boolean;
  data: TagWithCategory[];
  total: number;
  message: string;
}

export interface TagBatchResponse {
  success: boolean;
  data: TagRead[];
  message: string;
}

// 标签统计类型
export interface TagStatistics {
  total_categories: number;
  total_tags: number;
  active_tags: number;
  inactive_tags: number;
  categories_with_tags: Array<{
    category_id: number;
    category_name: string;
    tag_count: number;
  }>;
}

export interface TagStatisticsResponse {
  success: boolean;
  data: TagStatistics;
  message: string;
}
