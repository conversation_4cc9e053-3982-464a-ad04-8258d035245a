import { BaseEntity } from '../global';
import { CardType, CardStatus } from './card-template';

// 会员卡列表项
export interface MemberCardList {
  id: number;
  name: string;
  member_id: number;
  card_type: CardType;
  balance: number;
  status: CardStatus;
  card_number: string | null;
  expires_at: string | null;
  last_used_at: string | null;
  created_at: string;
}

// 会员卡详情
export interface MemberCardRead extends BaseEntity {
  name: string;
  tenant_id: number;
  member_id: number;
  template_id: number | null;
  card_type: CardType;
  balance: number;
  status: CardStatus;
  card_number: string | null;
  total_recharged: number;
  total_consumed: number;
  expires_at: string | null;
  last_used_at: string | null;
  freeze_reason: string | null;
  cancel_reason: string | null;
  created_by: number | null;
}

// 创建会员卡请求
export interface MemberCardCreate {
  member_id: number;
  template_id: number;
}

// 更新会员卡请求
export interface MemberCardUpdate {
  status?: CardStatus | null;
  balance?: number | null;
  expires_at?: string | null;
  freeze_reason?: string | null;
  cancel_reason?: string | null;
}

// 会员卡操作记录类型
export enum MemberCardOperationType {
  CREATE_CARD = 'create_card',
  UPDATE_CARD_INFO = 'update_card_info',
  FREEZE_CARD = 'freeze_card',
  UNFREEZE_CARD = 'unfreeze_card',
  CANCEL_CARD = 'cancel_card',
  RECHARGE = 'recharge',
  INITIAL_BINDING = 'initial_binding',
  DIRECT_BOOKING = 'direct_booking',
  FIXED_SCHEDULE_BOOKING = 'fixed_schedule_booking',
  ADMIN_BOOKING = 'admin_booking',
  MANUAL_DEDUCTION = 'manual_deduction',
  MEMBER_CANCEL_BOOKING = 'member_cancel_booking',
  ADMIN_CANCEL_BOOKING = 'admin_cancel_booking',
  REFUND = 'refund',
  OTHER = 'other',
}

// 操作类型名称映射
export const MemberCardOperationTypeNames = {
  [MemberCardOperationType.CREATE_CARD]: '创建会员卡',
  [MemberCardOperationType.UPDATE_CARD_INFO]: '更新会员卡信息',
  [MemberCardOperationType.FREEZE_CARD]: '冻结会员卡',
  [MemberCardOperationType.UNFREEZE_CARD]: '解冻会员卡',
  [MemberCardOperationType.CANCEL_CARD]: '注销会员卡',
  [MemberCardOperationType.RECHARGE]: '充值',
  [MemberCardOperationType.INITIAL_BINDING]: '初始绑定',
  [MemberCardOperationType.DIRECT_BOOKING]: '直接预订',
  [MemberCardOperationType.FIXED_SCHEDULE_BOOKING]: '固定排课预订',
  [MemberCardOperationType.ADMIN_BOOKING]: '管理员预订',
  [MemberCardOperationType.MANUAL_DEDUCTION]: '手动扣费',
  [MemberCardOperationType.MEMBER_CANCEL_BOOKING]: '会员取消预订',
  [MemberCardOperationType.ADMIN_CANCEL_BOOKING]: '管理员取消预订',
  [MemberCardOperationType.REFUND]: '退款',
  [MemberCardOperationType.OTHER]: '其他操作',
};

// 支付方式枚举
export enum PaymentMethod {
  WECHAT = 'wechat',
  ALIPAY = 'alipay',
  MANUAL = 'manual',
  BANK_TRANSFER = 'bank_transfer',
}

// 支付方式名称映射
export const PaymentMethodNames = {
  [PaymentMethod.WECHAT]: '微信支付',
  [PaymentMethod.ALIPAY]: '支付宝',
  [PaymentMethod.MANUAL]: '人工充值',
  [PaymentMethod.BANK_TRANSFER]: '银行转账',
};

// 会员卡操作记录
export interface MemberCardOperationRead extends BaseEntity {
  tenant_id: number;
  member_id: number;
  member_card_id: number;
  operation_type: MemberCardOperationType;
  operation_description: string;
  amount_change: number | null;
  balance_before: number | null;
  balance_after: number | null;
  status_before: CardStatus | null;
  status_after: CardStatus | null;
  member_card_name: string | null;
  bonus_amount: number | null;
  actual_amount: number | null;
  payment_method: PaymentMethod | null;
  payment_status: string | null;
  transaction_id: string | null;
  scheduled_class_id: number | null;
  operator_id: number | null;
  operator_name: string | null;
  operator_type: string | null;
  reason: string | null;
  notes: string | null;
  client_ip: string | null;
  user_agent: string | null;
  status: string | null;
}

// 充值请求
export interface RechargeRequest {
  member_card_id: number;
  amount: number;
  bonus_amount?: number | null;
  actual_amount?: number | null;
  payment_method: PaymentMethod;
  extend_validity_days?: number | null;
  notes?: string | null;
}

// 充值响应
export interface RechargeResponse {
  operation_id: number;
  member_card_id: number;
  amount: number;
  bonus_amount: number;
  actual_amount: number;
  total_amount: number;
  balance_before: number;
  balance_after: number;
  payment_method: PaymentMethod;
  transaction_id: string | null;
  created_at: string;
}

// 扣费请求
export interface DeductRequest {
  member_card_id: number;
  amount: number;
  reduce_validity_days?: number | null;
  notes?: string | null;
}

// 扣费响应
export interface DeductResponse {
  operation_id: number;
  member_card_id: number;
  amount: number;
  balance_before: number;
  balance_after: number;
  reduce_validity_days?: number | null;
  expires_at?: string | null;
  transaction_id?: string | null;
  created_at: string;
}
