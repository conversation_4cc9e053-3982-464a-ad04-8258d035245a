// 会员卡模板相关类型定义

import type { BaseEntity } from '../global';

// 卡片类型枚举
export enum CardType {
  TIMES_LIMITED = 'times_limited',
  TIMES_UNLIMITED = 'times_unlimited',
  VALUE_LIMITED = 'value_limited',
  VALUE_UNLIMITED = 'value_unlimited',
}

// 卡片类型名称映射
export const CardTypeNames = {
  [CardType.TIMES_LIMITED]: '次数限制卡',
  [CardType.TIMES_UNLIMITED]: '次数不限卡',
  [CardType.VALUE_LIMITED]: '金额限制卡',
  [CardType.VALUE_UNLIMITED]: '金额不限卡',
};

// 卡片状态枚举
export enum CardStatus {
  ACTIVE = 'active',
  FROZEN = 'frozen',
  EXPIRED = 'expired',
  CANCELLED = 'cancelled',
}

// 卡片状态名称映射
export const CardStatusNames = {
  [CardStatus.ACTIVE]: '正常',
  [CardStatus.FROZEN]: '冻结',
  [CardStatus.EXPIRED]: '已过期',
  [CardStatus.CANCELLED]: '已注销',
};

// 会员卡模板创建请求
export interface CardTemplateCreate {
  name: string;
  card_type: CardType;
  sale_price: number;
  available_balance?: number | null;
  validity_days?: number | null;
  is_agent_exclusive?: boolean;
  allow_repeat_purchase?: boolean;
  allow_renewal?: boolean;
  description?: string | null;
}

// 会员卡模板更新请求
export interface CardTemplateUpdate {
  name?: string;
  sale_price?: number;
  available_balance?: number | null;
  validity_days?: number | null;
  is_agent_exclusive?: boolean;
  allow_repeat_purchase?: boolean;
  allow_renewal?: boolean;
  description?: string | null;
  is_active?: boolean;
}

// 会员卡模板列表项
export interface CardTemplateList {
  id: number;
  name: string;
  card_type: CardType;
  sale_price: number;
  available_balance?: number | null;
  validity_days?: number | null;
  is_agent_exclusive: boolean;
  is_active: boolean;
  created_at: string;
}

// 会员卡模板详情
export interface CardTemplateRead extends BaseEntity {
  tenant_id: number;
  name: string;
  card_type: CardType;
  sale_price: number;
  available_balance?: number | null;
  validity_days?: number | null;
  is_agent_exclusive: boolean;
  allow_repeat_purchase: boolean;
  allow_renewal: boolean;
  description?: string | null;
  is_active: boolean;
  created_by?: number | null;
}
