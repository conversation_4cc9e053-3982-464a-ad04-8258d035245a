// 会员相关类型定义

import type { BaseEntity } from '../global';
import type { Gender } from './common';

// 会员类型枚举
export enum MemberType {
  TRIAL = 'trial',
  FORMAL = 'formal',
  VIP = 'vip',
}

// 会员状态枚举
export enum MemberStatus {
  ACTIVE = 'active',
  SILENT = 'silent',
  FROZEN = 'frozen',
  CANCELLED = 'cancelled',
}

// 来源渠道枚举
export enum SourceChannel {
  WECHAT = 'wechat',
  REFERRAL = 'referral',
  ONLINE = 'online',
  OFFLINE = 'offline',
  OTHER = 'other',
}

// 课程数据
export interface CourseData {
  one_on_one_completed: number; // 1对1已上课节
  one_on_one_total: number; // 1对1总课节
  group_completed: number; // 团课已上课节
  group_total: number; // 团课总课节
}

// 会员创建请求
export interface MemberCreate {
  name: string;
  phone: string;
  email?: string | null;
  gender?: Gender | null;
  birthday?: string | null;
  member_type?: MemberType;
  source_channel?: string | null;
  notes?: string | null;
  agent_id?: number | null;
}

// 会员更新请求
export interface MemberUpdate {
  name?: string;
  phone?: string;
  email?: string | null;
  gender?: Gender | null;
  birthday?: string | null;
  wechat_avatar?: string | null;
  member_type?: MemberType | null;
  member_status?: MemberStatus | null;
  source_channel?: string | null;
  address?: string | null;
  city?: string | null;
  province?: string | null;
  postal_code?: string | null;
  notes?: string | null;
  tags?: string[] | null;
  agent_id?: number | null;
}

// 会员响应
export interface MemberRead extends BaseEntity {
  // 统计信息
  total_classes: number;
  completed_classes: number;
  cancelled_classes: number;
  no_show_classes: number;
  total_spent: number;
  current_month_spent: number;
  total_recharged: number;
  avg_rating: string;
  rating_count: number;
  last_class_at?: string | null;
  last_login_at?: string | null;

  // 基本信息
  tenant_id: number;
  name: string;
  phone: string;
  email?: string | null;
  gender?: Gender | null;
  birthday?: string | null;
  avatar_url?: string | null;

  // 微信信息
  wechat_openid?: string | null;
  wechat_unionid?: string | null;
  wechat_nickname?: string | null;
  wechat_avatar?: string | null;

  // 会员属性
  member_type: MemberType;
  member_status: MemberStatus;
  source_channel?: string | null;
  agent_id?: number | null;
  agent_name?: string | null;

  // 地址信息
  address?: string | null;
  city?: string | null;
  province?: string | null;
  country: string;
  postal_code?: string | null;

  // 其他信息
  notes?: string | null;
  tags: string[];
  primary_member_card_id?: number | null;
  primary_member_card_name?: string | null;
  registered_at: string;
}
