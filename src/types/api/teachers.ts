// 教师管理相关类型定义

// ==================== 枚举类型 ====================

/** 教师区域枚举 */
export type TeacherRegion = 'europe' | 'north_america' | 'south_africa' | 'philippines' | 'china' | 'other';

/** 教师分类枚举 */
export type TeacherCategory = 'european' | 'south_african' | 'filipino' | 'chinese' | 'other';

/** 教师状态枚举 */
export type TeacherStatus = 'active' | 'inactive' | 'pending' | 'suspended';

/** 性别枚举 */
export type Gender = 'male' | 'female' | 'other';

// ==================== 基础类型 ====================

/** 教师基础信息 */
export interface TeacherBase {
  /** 教师姓名 */
  name: string;
  /** 教师显示编号（给会员看的标识） */
  display_code?: string;
  /** 性别 */
  gender?: Gender;
  /** 头像URL */
  avatar?: string;
  /** 手机号 */
  phone?: string;
  /** 邮箱 */
  email?: string;
  /** 单节课价格（整数，单位：元） */
  price_per_class: number;
  /** 教师分类 */
  teacher_category: TeacherCategory;
  /** 教师区域 */
  region: TeacherRegion;
  /** 是否绑定微信 */
  wechat_bound: boolean;
  /** 微信OpenID */
  wechat_openid?: string;
  /** 微信UnionID */
  wechat_unionid?: string;
  /** 是否对会员端展示 */
  show_to_members: boolean;
  /** 教师介绍 */
  introduction?: string;
  /** 教学经验(年) */
  teaching_experience?: number;
  /** 专业特长 */
  specialties: string[];
  /** 资质证书 */
  certifications: string[];
  /** 排课优先级 */
  priority_level: number;
  /** 备注 */
  notes?: string;
}

// ==================== CRUD 操作类型 ====================

/** 创建教师请求 */
export interface TeacherCreate extends TeacherBase {
  /** 标签ID列表 */
  tag_ids: number[];
}

/** 更新教师请求 */
export interface TeacherUpdate extends Partial<Omit<TeacherBase, 'teacher_category' | 'region'>> {
  /** 教师分类 */
  teacher_category?: TeacherCategory;
  /** 教师区域 */
  region?: TeacherRegion;
}

/** 教师响应基础信息 */
export interface TeacherRead extends TeacherBase {
  /** 教师ID */
  id: number;
  /** 租户ID */
  tenant_id: number;
  /** 教师状态 */
  status: TeacherStatus;
  /** 创建时间 */
  created_at: string;
  /** 更新时间 */
  updated_at?: string;
  /** 创建者ID */
  created_by?: number;
  /** 总上课数 */
  total_classes: number;
  /** 完成上课数 */
  completed_classes: number;
  /** 取消上课数 */
  cancelled_classes: number;
  /** 缺席上课数 */
  no_show_classes: number;
  /** 总收入（元） */
  total_earnings: number;
  /** 当月收入（元） */
  current_month_earnings: number;
  /** 平均评分 */
  avg_rating: string;
  /** 评价次数 */
  rating_count: number;
  /** 最后上课时间 */
  last_class_at?: string;
}

// ==================== 业务特定类型 ====================

/** 教师列表项 */
export interface TeacherList {
  /** 教师ID */
  id: number;
  /** 教师姓名 */
  name: string;
  /** 教师显示编号 */
  display_code?: string;
  /** 性别 */
  gender?: Gender;
  /** 头像URL */
  avatar?: string;
  /** 教师分类 */
  teacher_category: TeacherCategory;
  /** 教师区域 */
  region: TeacherRegion;
  /** 单节课价格 */
  price_per_class: number;
  /** 教师状态 */
  status: TeacherStatus;
  /** 是否对会员端展示 */
  show_to_members: boolean;
  /** 排课优先级 */
  priority_level: number;
  /** 教学经验(年) */
  teaching_experience?: number;
  /** 标签数量 */
  tag_count: number;
  /** 教师标签列表 */
  tags?: Array<{
    id: number;
    name: string;
    status: string;
    category_id: number;
    category_name: string;
  }>;
}

/** 教师详情 */
export interface TeacherDetail extends TeacherRead {
  /** 关联的标签列表 */
  tags: Array<{
    id: number;
    name: string;
    category_name: string;
    status: string;
  }>;
}

// ==================== 状态更新类型 ====================

/** 教师状态更新 */
export interface TeacherStatusUpdate {
  /** 新状态 */
  status: TeacherStatus;
  /** 状态变更原因 */
  reason?: string;
}

// ==================== 标签管理类型 ====================

/** 教师标签分配 */
export interface TeacherTagAssign {
  /** 标签ID列表 */
  tag_ids: number[];
}

/** 教师标签批量操作 */
export interface TeacherTagBatch {
  /** 教师ID列表 */
  teacher_ids: number[];
  /** 标签ID列表 */
  tag_ids: number[];
  /** 操作类型：add-添加，remove-移除 */
  operation: 'add' | 'remove';
}

// ==================== 查询参数类型 ====================

/** 教师列表查询参数 */
export interface TeacherQueryParams {
  /** 页码 */
  page?: number;
  /** 每页大小 */
  size?: number;
  /** 教师姓名 */
  name?: string;
  /** 教师分类 */
  teacher_category?: TeacherCategory;
  /** 教师区域 */
  region?: TeacherRegion;
  /** 教师状态 */
  status?: TeacherStatus;
  /** 是否对会员端展示 */
  show_to_members?: boolean;
  /** 最低价格 */
  min_price?: number;
  /** 最高价格 */
  max_price?: number;
  /** 标签ID列表（逗号分隔） */
  tag_ids?: string;
  /** 排序字段 */
  sort_by?: string;
  /** 排序顺序 */
  sort_order?: 'asc' | 'desc';
}

/** 教师搜索参数 */
export interface TeacherSearchParams {
  /** 搜索关键词 */
  keyword: string;
  /** 返回数量限制 */
  limit?: number;
}

/** 教师可用性查询参数 */
export interface TeacherAvailableParams {
  /** 教师区域筛选 */
  region?: TeacherRegion;
  /** 教师分类筛选 */
  teacher_category?: TeacherCategory;
  /** 最高价格筛选 */
  max_price?: number;
}

// ==================== API 响应类型 ====================

/** 教师响应 */
export interface TeacherResponse {
  success: boolean;
  data: TeacherRead;
  message: string;
}

/** 教师详情响应 */
export interface TeacherDetailResponse {
  success: boolean;
  data: TeacherDetail;
  message: string;
}

/** 教师列表响应 */
export interface TeacherListResponse {
  success: boolean;
  data: TeacherList[];
  total: number;
  page: number;
  size: number;
  pages: number;
  message: string;
}

/** 教师简单列表响应 */
export interface TeacherSimpleListResponse {
  success: boolean;
  data: TeacherList[];
  total: number;
  message: string;
}

/** 教师统计响应 */
export interface TeacherStatisticsResponse {
  success: boolean;
  data: {
    total_teachers: number;
    active_teachers: number;
    pending_teachers: number;
    suspended_teachers: number;
    region_distribution: Record<TeacherRegion, number>;
    category_distribution: Record<TeacherCategory, number>;
  };
  message: string;
}
