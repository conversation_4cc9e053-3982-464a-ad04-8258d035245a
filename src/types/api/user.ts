// 用户相关类型定义

import type { BaseEntity } from '../global';
import type { Gender } from './common';

// 用户角色枚举
export type UserRole = 'super_admin' | 'admin' | 'user';

// 用户创建请求
export interface UserCreate {
  username: string;
  email?: string | null;
  phone?: string | null;
  password: string;
  real_name?: string | null;
  role: UserRole;
  gender?: Gender | null;
  birthday?: string | null; // ISO date string
}

// 用户更新请求
export interface UserUpdate {
  username?: string;
  email?: string | null;
  phone?: string | null;
  real_name?: string | null;
  role?: UserRole;
  gender?: Gender | null;
  birthday?: string | null;
  is_active?: boolean;
}

// 用户响应
export interface UserRead extends BaseEntity {
  tenant_id?: number | null;
  username: string;
  email?: string | null;
  phone?: string | null;
  real_name?: string | null;
  avatar_url?: string | null;
  role: UserRole;
  gender?: Gender | null;
  birthday?: string | null;
  is_active: boolean;
  last_login_at?: string | null;
}
