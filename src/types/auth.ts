import type { AdminLoginResponse, UserRead } from './api';

/**
 * 认证状态
 */
export interface AuthState {
  user: AdminUser | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

/**
 * 管理员用户
 */
export type AdminUser = UserRead;

/**
 * 登录请求
 */
export interface LoginRequest {
  username: string;
  password: string;
}

/**
 * 登录响应
 */
export type LoginResponse = AdminLoginResponse;
