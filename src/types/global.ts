// 全局类型定义

export interface BaseEntity {
  id: string;
  created_at: string;
  updated_at: string;
}

export interface PaginationParams {
  page?: number;
  size?: number;
  skip?: number;
  limit?: number;
}

export interface PaginationResponse<T> {
  data: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

export interface ApiResponse<T = unknown> {
  success: boolean;
  message: string;
  http_code: number;
  business_code: string;
  data?: T;
  total?: number;
  page?: number;
  size?: number;
  pages?: number;
  timestamp: string;
}

export interface ApiError {
  success: false;
  message: string;
  http_code: number;
  business_code: string;
  level: 'error' | 'warning' | 'info';
  details?: Record<string, unknown>;
  timestamp: string;
}

export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export interface SelectOption {
  label: string;
  value: string;
  disabled?: boolean;
}
