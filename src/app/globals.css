@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    /* 基础颜色变量 */
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;

    /* 主题色系统 */
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;

    /* 主题色变量 - 用于主题切换 */
    --theme-primary: var(--primary);
    --theme-primary-hover: 0 0% 0%;
    --theme-primary-active: 0 0% 0%;

    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    /* 状态色系统 */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 9%;
    --info: 199 89% 48%;
    --info-foreground: 0 0% 98%;

    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    /* 图表色系统 - 浅色模式 */
    --chart-1: 210 100% 56%; /* 蓝色 */
    --chart-2: 142 76% 36%; /* 绿色 */
    --chart-3: 38 92% 50%; /* 橙色 */
    --chart-4: 250 100% 60%; /* 紫色 */
    --chart-5: 175 80% 40%; /* 青绿色 */

    /* 调整圆角为更小的值，更符合中国用户习惯 */
    --radius: 0.25rem;

    /* 新增间距变量，用于组件内边距控制 */
    --spacing-1: 0.25rem; /* 4px */
    --spacing-2: 0.5rem; /* 8px */
    --spacing-3: 0.75rem; /* 12px */
    --spacing-4: 1rem; /* 16px */

    /* 新增组件内边距变量 */
    --card-padding: var(--spacing-3);
    --input-padding-y: 0.375rem; /* 6px */
    --input-padding-x: 0.5rem; /* 8px */
    --button-padding-y: 0.375rem; /* 6px */
    --button-padding-x: 0.75rem; /* 12px */

    /* 阴影调整为更锐利的风格 */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.1);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.15), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md:
      0 4px 6px -1px rgb(0 0 0 / 0.15), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg:
      0 10px 15px -3px rgb(0 0 0 / 0.15), 0 4px 6px -4px rgb(0 0 0 / 0.1);

    /* 侧边栏相关变量 */
    --sidebar-bg: 0 0% 100%;
    --sidebar-width: 200px;
    --sidebar-collapsed-width: 64px;
    --sidebar-border-radius: 12px;
    --sidebar-item-height: 42px;
    --sidebar-item-radius: 4px;
    --sidebar-icon-size: 16px;
    --sidebar-chevron-size: 12px;

    /* 侧边栏颜色 - 基础变量 */
    --sidebar-text: 220 9% 20%;
    --sidebar-icon: 206 16% 60%;

    /* 这些变量会被 .sidebar-theme 类覆盖 */
    --sidebar-text-active: var(--theme-primary);
    --sidebar-icon-active: var(--theme-primary);
    --sidebar-indicator: var(--theme-primary);

    /* 这些变量会被具体主题类覆盖 */
    --sidebar-bg-hover: 220 14% 97%;
    --sidebar-bg-active: 210 100% 97%;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;

    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    /* 暗色模式下的状态色系统 */
    --success: 142 70% 45%;
    --success-foreground: 0 0% 98%;
    --warning: 38 95% 60%;
    --warning-foreground: 0 0% 9%;
    --info: 199 89% 60%;
    --info-foreground: 0 0% 98%;

    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    /* 图表色系统 - 暗色模式 */
    --chart-1: 210 100% 70%; /* 蓝色 - 提高亮度 */
    --chart-2: 142 70% 50%; /* 绿色 - 提高亮度 */
    --chart-3: 38 95% 65%; /* 橙色 - 提高亮度 */
    --chart-4: 250 100% 75%; /* 紫色 - 提高亮度 */
    --chart-5: 175 80% 55%; /* 青绿色 - 提高亮度 */

    /* 暗色模式下的阴影调整 */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.3), 0 1px 2px -1px rgb(0 0 0 / 0.3);
    --shadow-md:
      0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
    --shadow-lg:
      0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);

    /* 暗色模式下的主题色变量 */
    --theme-primary: var(--primary);
    --theme-primary-hover: 0 0% 90%;
    --theme-primary-active: 0 0% 80%;

    /* 暗色模式下的侧边栏颜色 - 基础变量 */
    --sidebar-bg: 220 26% 14%;
    --sidebar-text: 220 14% 80%;
    --sidebar-icon: 220 10% 70%;

    /* 这些变量会被 .dark .sidebar-theme 类覆盖 */
    --sidebar-text-active: var(--theme-primary);
    --sidebar-icon-active: var(--theme-primary);

    /* 这些变量会被具体暗色主题类覆盖 */
    --sidebar-bg-hover: 220 14% 20%;
    --sidebar-bg-active: 210 100% 14%;
  }

  /* 主题色系统 - 蓝色系 */
  .theme-blue {
    --theme-primary: 210 100% 62%;
    --theme-primary-hover: 210 100% 55%;
    --theme-primary-active: 210 100% 50%;
  }

  /* 主题色系统 - 紫色系 */
  .theme-purple {
    --theme-primary: 250 100% 60%;
    --theme-primary-hover: 250 100% 55%;
    --theme-primary-active: 250 100% 50%;
  }

  /* 主题色系统 - 青绿色系 */
  .theme-teal {
    --theme-primary: 175 80% 40%;
    --theme-primary-hover: 175 80% 35%;
    --theme-primary-active: 175 80% 30%;
  }

  /* 主题色系统 - 灰色系 */
  .theme-gray {
    --theme-primary: 220 10% 40%;
    --theme-primary-hover: 220 10% 35%;
    --theme-primary-active: 220 10% 30%;
  }

  /* 侧边栏主题基础设置 - 所有主题通用 */
  .sidebar-theme {
    --sidebar-text-active: var(--theme-primary);
    --sidebar-icon-active: var(--theme-primary);
    --sidebar-indicator: var(--theme-primary);
  }

  /* 侧边栏主题 - 蓝色系 */
  .sidebar-blue {
    --sidebar-bg-hover: 220 14% 97%;
    --sidebar-bg-active: 210 100% 97%;
  }

  /* 侧边栏主题 - 紫色系 */
  .sidebar-purple {
    --sidebar-bg-hover: 250 14% 97%;
    --sidebar-bg-active: 250 100% 97%;
  }

  /* 侧边栏主题 - 青绿色系 */
  .sidebar-teal {
    --sidebar-bg-hover: 175 14% 97%;
    --sidebar-bg-active: 175 100% 97%;
  }

  /* 侧边栏主题 - 灰色系 */
  .sidebar-gray {
    --sidebar-bg-hover: 220 10% 97%;
    --sidebar-bg-active: 220 10% 94%;
  }

  /* 暗色模式下的配色调整 */
  /* 暗色模式下的主题色系统 */
  .dark.theme-blue,
  .dark .theme-blue {
    --theme-primary: 210 100% 70%;
    --theme-primary-hover: 210 100% 65%;
    --theme-primary-active: 210 100% 60%;
  }

  .dark.theme-purple,
  .dark .theme-purple {
    --theme-primary: 250 100% 70%;
    --theme-primary-hover: 250 100% 65%;
    --theme-primary-active: 250 100% 60%;
  }

  .dark.theme-teal,
  .dark .theme-teal {
    --theme-primary: 175 80% 60%;
    --theme-primary-hover: 175 80% 55%;
    --theme-primary-active: 175 80% 50%;
  }

  .dark.theme-gray,
  .dark .theme-gray {
    --theme-primary: 220 10% 70%;
    --theme-primary-hover: 220 10% 65%;
    --theme-primary-active: 220 10% 60%;
  }

  /* 暗色模式下的侧边栏主题基础设置 */
  .dark .sidebar-theme {
    --sidebar-text-active: var(--theme-primary);
    --sidebar-icon-active: var(--theme-primary);
  }

  /* 暗色模式下的侧边栏主题 */
  .dark.sidebar-blue,
  .dark .sidebar-blue {
    --sidebar-bg-active: 210 100% 14%;
    --sidebar-bg-hover: 210 30% 20%;
  }

  .dark.sidebar-purple,
  .dark .sidebar-purple {
    --sidebar-bg-active: 250 100% 14%;
    --sidebar-bg-hover: 250 30% 20%;
  }

  .dark.sidebar-teal,
  .dark .sidebar-teal {
    --sidebar-bg-active: 175 80% 14%;
    --sidebar-bg-hover: 175 30% 20%;
  }

  .dark.sidebar-gray,
  .dark .sidebar-gray {
    --sidebar-bg-active: 220 10% 20%;
    --sidebar-bg-hover: 220 15% 25%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground text-sm; /* 默认字体大小调整为 14px (text-sm) */
  }

  /* 调整标题大小 */
  h1 {
    @apply text-2xl font-semibold;
  }
  h2 {
    @apply text-xl font-semibold;
  }
  h3 {
    @apply text-lg font-medium;
  }
  h4 {
    @apply text-base font-medium;
  }
}

/* 侧边栏自定义样式 */
.sidebar-container {
  @apply shadow-lg;
  border-radius: var(--sidebar-border-radius);
  background-color: hsl(var(--sidebar-bg));
}

.sidebar-item {
  height: var(--sidebar-item-height);
  border-radius: var(--sidebar-item-radius);
  transition: all 0.3s ease;
  color: hsl(var(--sidebar-text));
}

/* 非激活状态的悬停效果 */
.sidebar-item:not(.active):hover {
  background-color: hsl(var(--sidebar-bg-hover));
}

.sidebar-item.active {
  background-color: hsl(var(--sidebar-bg-active));
  color: hsl(var(--sidebar-text-active));
}

/* 激活状态下不应该有悬停效果 */
.sidebar-item.active:hover {
  background-color: hsl(var(--sidebar-bg-active));
  color: hsl(var(--sidebar-text-active));
}

.sidebar-icon {
  color: hsl(var(--sidebar-icon));
  width: var(--sidebar-icon-size);
  height: var(--sidebar-icon-size);
}

.sidebar-item.active .sidebar-icon {
  color: hsl(var(--sidebar-icon-active));
}

.sidebar-chevron {
  width: var(--sidebar-chevron-size);
  height: var(--sidebar-chevron-size);
  transition: transform 0.3s;
}

.sidebar-notification {
  @apply absolute top-1/2 right-4 w-2 h-2 rounded-full;
  background-color: hsl(var(--destructive));
  transform: translate(50%, -50%);
}

/* 侧边栏状态指示器 */
.sidebar-item-indicator {
  @apply absolute left-0 top-1/2 w-1 h-6 rounded-r-full;
  background-color: hsl(var(--sidebar-indicator));
  transform: translateY(-50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.sidebar-item.active .sidebar-item-indicator {
  opacity: 1;
}

/* 折叠状态下的工具提示 */
.sidebar-tooltip {
  @apply absolute left-full ml-2 px-2 py-1 text-xs rounded-md shadow-lg z-50;
  background-color: hsl(var(--popover));
  color: hsl(var(--popover-foreground));
  border: 1px solid hsl(var(--border));
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;
  white-space: nowrap;
}

.sidebar-item:hover .sidebar-tooltip {
  opacity: 1;
  visibility: visible;
}
