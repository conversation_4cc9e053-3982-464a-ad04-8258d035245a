'use client';

import { useState } from 'react';
import { Plus, Search, Filter, Tags, Tag } from 'lucide-react';
import { MainLayout } from '@/components/layout/main-layout';
import { PageErrorBoundary } from '@/components/providers/error-boundary';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { CompactDataTable, DataStateWrapper } from '@/components/tables';
import { ConfirmDialog } from '@/components/ui/confirm-dialog';
import { createTagCategoryColumns, createTagColumns } from '@/components/tables';
import {
  useTagCategories,
  useTags,
  useDeleteTagCategory,
  useDeleteTag,
} from '@/hooks/use-tags';
import { TAG_MESSAGES } from '@/constants/tags';
import type { TagCategoryList, TagWithCategory } from '@/types/api';

export default function TagsPage() {
  // 状态管理
  const [activeTab, setActiveTab] = useState('categories');
  const [categoryFilters, setCategoryFilters] = useState({
    search: '',
  });
  const [tagFilters, setTagFilters] = useState({
    search: '',
    category_id: undefined as number | undefined,
    status: undefined as string | undefined,
  });

  // 删除确认对话框
  const [deleteCategory, setDeleteCategory] = useState<TagCategoryList | null>(null);
  const [deleteTag, setDeleteTag] = useState<TagWithCategory | null>(null);

  // API hooks
  const { data: categoriesData, isLoading: categoriesLoading } = useTagCategories(categoryFilters);
  const { data: tagsData, isLoading: tagsLoading } = useTags(tagFilters);
  const deleteCategoryMutation = useDeleteTagCategory();
  const deleteTagMutation = useDeleteTag();

  // 处理分类操作
  const handleCategoryEdit = (category: TagCategoryList) => {
    // TODO: 打开编辑分类对话框
    console.log('编辑分类:', category);
  };

  const handleCategoryDelete = (category: TagCategoryList) => {
    setDeleteCategory(category);
  };

  const handleCategoryView = (category: TagCategoryList) => {
    // 切换到标签页面并筛选该分类
    setTagFilters(prev => ({ ...prev, category_id: category.id }));
    setActiveTab('tags');
  };

  const confirmCategoryDelete = async () => {
    if (!deleteCategory) return;
    await deleteCategoryMutation.mutateAsync(deleteCategory.id.toString());
    setDeleteCategory(null);
  };

  // 处理标签操作
  const handleTagEdit = (tag: TagWithCategory) => {
    // TODO: 打开编辑标签对话框
    console.log('编辑标签:', tag);
  };

  const handleTagDelete = (tag: TagWithCategory) => {
    setDeleteTag(tag);
  };

  const handleTagStatusToggle = (tag: TagWithCategory) => {
    // TODO: 切换标签状态
    console.log('切换标签状态:', tag);
  };

  const confirmTagDelete = async () => {
    if (!deleteTag) return;
    await deleteTagMutation.mutateAsync(deleteTag.id.toString());
    setDeleteTag(null);
  };

  // 筛选处理
  const handleCategoryFilterChange = (key: string, value: string) => {
    setCategoryFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleTagFilterChange = (key: string, value: string | number | undefined) => {
    setTagFilters(prev => ({ ...prev, [key]: value }));
  };

  // 表格列配置
  const categoryColumns = createTagCategoryColumns({
    onEdit: handleCategoryEdit,
    onDelete: handleCategoryDelete,
    onView: handleCategoryView,
  });

  const tagColumns = createTagColumns({
    onEdit: handleTagEdit,
    onDelete: handleTagDelete,
    onStatusToggle: handleTagStatusToggle,
    showCategory: true,
  });

  return (
    <MainLayout>
      <PageErrorBoundary
        onError={error => {
          console.error('标签管理页面错误:', error);
        }}
      >
        <div className="space-y-6">
          {/* 页面标题 */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold tracking-tight">标签管理</h1>
              <p className="text-muted-foreground">
                管理标签分类和标签，用于教师分类和筛选
              </p>
            </div>
          </div>

          {/* 统计卡片 */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">标签分类</CardTitle>
                <Tags className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {categoriesData?.total || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  总分类数量
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总标签</CardTitle>
                <Tag className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {tagsData?.total || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  所有标签数量
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">激活标签</CardTitle>
                <Badge variant="success" className="h-4 w-4 p-0" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {tagsData?.data?.filter(tag => tag.status === 'active').length || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  可用标签数量
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">停用标签</CardTitle>
                <Badge variant="secondary" className="h-4 w-4 p-0" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {tagsData?.data?.filter(tag => tag.status === 'inactive').length || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  停用标签数量
                </p>
              </CardContent>
            </Card>
          </div>

          {/* 主要内容 */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList>
              <TabsTrigger value="categories">标签分类</TabsTrigger>
              <TabsTrigger value="tags">标签管理</TabsTrigger>
            </TabsList>

            {/* 标签分类管理 */}
            <TabsContent value="categories" className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>标签分类</CardTitle>
                      <CardDescription>
                        管理标签分类，用于组织和分组标签
                      </CardDescription>
                    </div>
                    <Button>
                      <Plus className="mr-2 h-4 w-4" />
                      新增分类
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  {/* 筛选栏 */}
                  <div className="flex items-center space-x-2 mb-4">
                    <div className="relative flex-1 max-w-sm">
                      <Search className="absolute left-2.5 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                      <Input
                        placeholder="搜索分类名称..."
                        value={categoryFilters.search}
                        onChange={e => handleCategoryFilterChange('search', e.target.value)}
                        className="pl-8"
                      />
                    </div>
                  </div>

                  {/* 分类列表 */}
                  <DataStateWrapper
                    isLoading={categoriesLoading}
                    isEmpty={!categoriesData?.data?.length}
                    emptyTitle="暂无标签分类"
                    emptyDescription="还没有创建任何标签分类，点击上方按钮创建第一个分类"
                  >
                    <CompactDataTable
                      columns={categoryColumns}
                      data={categoriesData?.data || []}
                      isLoading={categoriesLoading}
                    />
                  </DataStateWrapper>
                </CardContent>
              </Card>
            </TabsContent>

            {/* 标签管理 */}
            <TabsContent value="tags" className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>标签管理</CardTitle>
                      <CardDescription>
                        管理具体的标签项，可以分配给教师进行分类
                      </CardDescription>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline">
                        批量操作
                      </Button>
                      <Button>
                        <Plus className="mr-2 h-4 w-4" />
                        新增标签
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {/* 筛选栏 */}
                  <div className="flex items-center space-x-2 mb-4">
                    <div className="relative flex-1 max-w-sm">
                      <Search className="absolute left-2.5 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                      <Input
                        placeholder="搜索标签名称..."
                        value={tagFilters.search}
                        onChange={e => handleTagFilterChange('search', e.target.value)}
                        className="pl-8"
                      />
                    </div>
                    <Button variant="outline" size="sm">
                      <Filter className="mr-2 h-4 w-4" />
                      筛选
                    </Button>
                  </div>

                  {/* 标签列表 */}
                  <DataStateWrapper
                    isLoading={tagsLoading}
                    isEmpty={!tagsData?.data?.length}
                    emptyTitle="暂无标签"
                    emptyDescription="还没有创建任何标签，点击上方按钮创建第一个标签"
                  >
                    <CompactDataTable
                      columns={tagColumns}
                      data={tagsData?.data || []}
                      isLoading={tagsLoading}
                    />
                  </DataStateWrapper>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* 删除确认对话框 */}
        <ConfirmDialog
          open={!!deleteCategory}
          onOpenChange={() => setDeleteCategory(null)}
          title="确认删除分类"
          description={TAG_MESSAGES.CATEGORY.DELETE_CONFIRM}
          variant="destructive"
          onConfirm={confirmCategoryDelete}
          isLoading={deleteCategoryMutation.isPending}
        />

        <ConfirmDialog
          open={!!deleteTag}
          onOpenChange={() => setDeleteTag(null)}
          title="确认删除标签"
          description={TAG_MESSAGES.TAG.DELETE_CONFIRM}
          variant="destructive"
          onConfirm={confirmTagDelete}
          isLoading={deleteTagMutation.isPending}
        />
      </PageErrorBoundary>
    </MainLayout>
  );
}
