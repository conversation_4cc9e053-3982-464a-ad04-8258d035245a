'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';

import { MainLayout } from '@/components/layout/main-layout';
import { CompactDataTable } from '@/components/tables/common/compact-data-table';
import { CardTemplateMobile } from '@/components/tables/mobile/card-template-mobile';
import { createCardTemplateColumns } from '@/components/tables/columns/card-template-columns';
import { DataStateWrapper } from '@/components/ui/loading-states';
import { PageErrorBoundary } from '@/components/providers/error-boundary';
import { ConfirmDialog, createDeleteConfirmProps } from '@/components/ui/confirm-dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, Plus } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import {
  useCardTemplateList,
  useDeleteCardTemplate,
  useToggleCardTemplateStatus,
} from '@/hooks/use-card-templates';
import { MESSAGES } from '@/constants/messages';
import { CardType, CardTemplateList } from '@/types/api';

// 卡片类型选项
const cardTypeOptions = [
  { value: 'all', label: '全部类型' },
  { value: CardType.TIMES_LIMITED, label: '次数限制卡' },
  { value: CardType.TIMES_UNLIMITED, label: '次数不限卡' },
  { value: CardType.VALUE_LIMITED, label: '金额限制卡' },
  { value: CardType.VALUE_UNLIMITED, label: '金额不限卡' },
];

export default function CardTemplatesPage() {
  const router = useRouter();
  const [templateToDelete, setTemplateToDelete] =
    useState<CardTemplateList | null>(null);

  // 筛选状态
  const [filters, setFilters] = useState({
    card_type: 'all',
    search: '',
  });

  // 获取会员卡模板列表
  const { data: templates, isLoading } = useCardTemplateList();
  const deleteCardTemplate = useDeleteCardTemplate();
  const toggleCardTemplateStatus = useToggleCardTemplateStatus();

  const handleAdd = () => {
    router.push('/card-templates/create');
  };

  const handleEdit = (id: number) => {
    router.push(`/card-templates/${id}`);
  };

  const handleDelete = (id: number) => {
    const template = templates?.find(t => t.id === id) || null;
    setTemplateToDelete(template);
  };

  const confirmDelete = async () => {
    if (!templateToDelete) return;

    await deleteCardTemplate.mutateAsync(templateToDelete.id.toString());
    setTemplateToDelete(null);
    // 成功和错误消息都由 useDeleteCardTemplate Hook 统一处理，避免重复提示
  };

  // 处理切换模板状态
  const handleToggleStatus = (id: number) => {
    toggleCardTemplateStatus.mutate(id.toString());
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  // 根据筛选条件过滤数据
  const filteredTemplates = templates
    ? templates.filter(template => {
        // 筛选卡片类型
        if (
          filters.card_type !== 'all' &&
          template.card_type !== filters.card_type
        ) {
          return false;
        }

        // 搜索名称
        if (
          filters.search &&
          !template.name.toLowerCase().includes(filters.search.toLowerCase())
        ) {
          return false;
        }

        return true;
      })
    : [];

  // 判断是否有筛选条件
  const hasActiveFilters = filters.card_type !== 'all' || filters.search.trim() !== '';

  // 判断是否为筛选后的空结果
  const isFilteredEmpty = !isLoading && templates && templates.length > 0 && filteredTemplates.length === 0;

  // 判断是否为真正的空数据
  const isTrulyEmpty = !isLoading && (!templates || templates.length === 0);

  const columns = createCardTemplateColumns({
    onEdit: handleEdit,
    onDelete: handleDelete,
    onToggleStatus: handleToggleStatus,
  });

  return (
    <MainLayout>
      <PageErrorBoundary
        onError={error => {
          console.error('会员卡模板管理页面错误:', error);
        }}
      >
        <div className="space-y-4">
          {/* 紧凑型筛选区域 - 优化移动端布局 */}
          <div className="bg-card border rounded-lg p-3">
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
              {/* 筛选条件区域 */}
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 flex-1 w-full sm:w-auto">
                <div className="flex items-center gap-2 w-full sm:w-auto">
                  <span className="text-sm font-medium whitespace-nowrap">
                    卡片类型:
                  </span>
                  <Select
                    value={filters.card_type}
                    onValueChange={value =>
                      handleFilterChange('card_type', value)
                    }
                  >
                    <SelectTrigger className="h-8 w-full sm:w-36 text-xs">
                      <SelectValue placeholder="选择卡片类型" />
                    </SelectTrigger>
                    <SelectContent>
                      {cardTypeOptions.map(option => (
                        <SelectItem
                          key={option.value}
                          value={option.value}
                          className="text-xs"
                        >
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="relative flex-1 min-w-[200px] w-full sm:w-auto">
                  <Search className="absolute left-2.5 top-1/2 h-3.5 w-3.5 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    placeholder="搜索模板名称..."
                    value={filters.search}
                    onChange={e => handleFilterChange('search', e.target.value)}
                    className="pl-8 h-8 text-xs w-full"
                  />
                </div>
              </div>

              {/* 操作按钮区域 */}
              <div className="flex items-center gap-2 w-full sm:w-auto">
                <Button
                  size="sm"
                  className="h-8 whitespace-nowrap flex-1 sm:flex-none"
                  onClick={handleAdd}
                >
                  <Plus className="mr-1.5 h-3.5 w-3.5" />
                  新增模板
                </Button>
              </div>
            </div>
          </div>

          {/* 会员卡模板列表 */}
          <DataStateWrapper
            isLoading={isLoading}
            isEmpty={isTrulyEmpty}
            emptyTitle="还没有会员卡模板"
            emptyDescription="系统中还没有任何会员卡模板，点击下方按钮添加第一个模板"
            emptyActionLabel="添加模板"
            onEmptyAction={handleAdd}
          >
            {/* 筛选结果为空的特殊处理 */}
            {isFilteredEmpty ? (
              <div className="min-h-[200px] flex items-center justify-center">
                <div className="text-center space-y-3">
                  <div className="text-muted-foreground">
                    <Search className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  </div>
                  <div className="space-y-1">
                    <h3 className="text-sm font-medium text-foreground">
                      没有找到符合条件的模板
                    </h3>
                    <p className="text-xs text-muted-foreground">
                      {hasActiveFilters && (
                        <>
                          当前筛选条件：
                          {filters.card_type !== 'all' && (
                            <span className="ml-1 text-primary">
                              {cardTypeOptions.find(opt => opt.value === filters.card_type)?.label}
                            </span>
                          )}
                          {filters.search && (
                            <span className="ml-1 text-primary">
                              "{filters.search}"
                            </span>
                          )}
                        </>
                      )}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      尝试调整筛选条件或添加新的模板
                    </p>
                  </div>
                  <Button variant="outline" size="sm" onClick={handleAdd}>
                    <Plus className="mr-1.5 h-3.5 w-3.5" />
                    添加新模板
                  </Button>
                </div>
              </div>
            ) : (
              <>
                {/* 桌面端：表格布局 */}
                <div className="hidden sm:block">
                  <CompactDataTable
                    columns={columns}
                    data={filteredTemplates}
                    isLoading={false}
                  />
                </div>

                {/* 移动端：卡片布局 */}
                <div className="block sm:hidden">
                  <CardTemplateMobile
                    data={filteredTemplates}
                    onEdit={handleEdit}
                    onDelete={handleDelete}
                    onToggleStatus={handleToggleStatus}
                  />
                </div>
              </>
            )}
          </DataStateWrapper>

          {/* 删除确认对话框 */}
          <ConfirmDialog
            {...createDeleteConfirmProps({
              open: !!templateToDelete,
              onOpenChange: () => setTemplateToDelete(null),
              itemName: templateToDelete?.name || '',
              itemType: '模板',
              onConfirm: confirmDelete,
              isLoading: deleteCardTemplate.isPending,
            })}
          />
        </div>
      </PageErrorBoundary>
    </MainLayout>
  );
}
