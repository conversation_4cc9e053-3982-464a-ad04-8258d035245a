'use client';

import { useRouter } from 'next/navigation';
import { MainLayout } from '@/components/layout/main-layout';
import { CardTemplateForm } from '@/components/forms/card-template-form';
import {
  useCardTemplateDetail,
  useUpdateCardTemplate,
} from '@/hooks/use-card-templates';
import type { CardTemplateCreate, CardTemplateUpdate } from '@/types/api';
import { Skeleton } from '@/components/ui/skeleton';

interface EditCardTemplatePageProps {
  params: {
    id: string;
  };
}

export default function EditCardTemplatePage({
  params,
}: EditCardTemplatePageProps) {
  const { id } = params;
  const router = useRouter();
  const { data: template, isLoading } = useCardTemplateDetail(id);
  const updateCardTemplate = useUpdateCardTemplate();

  // 处理表单提交
  const handleSubmit = (data: CardTemplateCreate | CardTemplateUpdate) => {
    // 由于这是编辑页面，我们知道这里的数据一定是 CardTemplateUpdate 类型
    updateCardTemplate.mutate(
      { id, data: data as CardTemplateUpdate },
      {
        onSuccess: () => {
          router.push('/card-templates');
        },
      }
    );
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold">编辑会员卡模板</h1>
          <p className="text-muted-foreground">
            修改会员卡模板信息，包括名称、价格、有效期等
          </p>
        </div>

        {isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-60 w-full" />
          </div>
        ) : template ? (
          <CardTemplateForm
            template={template}
            mode="edit"
            onSubmit={handleSubmit}
            isLoading={updateCardTemplate.isPending}
          />
        ) : (
          <div className="p-4 text-center">
            <p className="text-muted-foreground">未找到模板信息</p>
          </div>
        )}
      </div>
    </MainLayout>
  );
}
