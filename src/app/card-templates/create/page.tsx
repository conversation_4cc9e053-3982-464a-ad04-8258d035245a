'use client';

import { useRouter } from 'next/navigation';
import { MainLayout } from '@/components/layout/main-layout';
import { CardTemplateForm } from '@/components/forms/card-template-form';
import { useCreateCardTemplate } from '@/hooks/use-card-templates';
import type { CardTemplateCreate, CardTemplateUpdate } from '@/types/api';

export default function CreateCardTemplatePage() {
  const router = useRouter();
  const createCardTemplate = useCreateCardTemplate();

  // 处理表单提交
  const handleSubmit = (data: CardTemplateCreate | CardTemplateUpdate) => {
    // 由于这是创建页面，我们知道这里的数据一定是 CardTemplateCreate 类型
    createCardTemplate.mutate(data as CardTemplateCreate, {
      onSuccess: () => {
        router.push('/card-templates');
      },
    });
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold">新增会员卡模板</h1>
          <p className="text-muted-foreground">
            创建新的会员卡模板，设置卡片类型、价格和有效期等信息
          </p>
        </div>

        <CardTemplateForm
          mode="create"
          onSubmit={handleSubmit}
          isLoading={createCardTemplate.isPending}
        />
      </div>
    </MainLayout>
  );
}
