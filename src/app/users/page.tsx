'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';

import { MainLayout } from '@/components/layout/main-layout';
import { CompactDataTable } from '@/components/tables/common/compact-data-table';
import { createUserColumns } from '@/components/tables/columns/user-columns';
import { DataStateWrapper } from '@/components/ui/loading-states';
import { PageErrorBoundary } from '@/components/providers/error-boundary';
import { ConfirmDialog, createDeleteConfirmProps } from '@/components/ui/confirm-dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, Plus } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { useUserList, useDeleteUser } from '@/hooks/use-users';
import { ROUTES } from '@/constants/routes';
import { MESSAGES } from '@/constants/messages';
import type { UserRead } from '@/types/api';

// 用户角色选项
const userRoleOptions = [
  { value: 'all', label: '全部角色' },
  { value: 'super_admin', label: '超级管理员' },
  { value: 'admin', label: '管理员' },
  { value: 'agent', label: '代理商' },
  { value: 'sale', label: '销售' },
];

export default function UsersPage() {
  const router = useRouter();
  const [deleteUser, setDeleteUser] = useState<UserRead | null>(null);

  // 筛选状态
  const [filters, setFilters] = useState({
    role: 'all',
    search: '',
  });

  // 获取用户列表
  const { data: usersResponse, isLoading } = useUserList({
    role: filters.role === 'all' ? undefined : filters.role,
    search: filters.search || undefined,
  });

  const users = Array.isArray(usersResponse?.data)
    ? usersResponse.data
    : usersResponse?.data?.data || [];

  // 删除用户
  const deleteUserMutation = useDeleteUser();

  const handleAdd = () => {
    router.push(ROUTES.USERS_CREATE);
  };

  const handleEdit = (user: UserRead) => {
    router.push(ROUTES.USERS_EDIT(user.id));
  };

  const handleView = (user: UserRead) => {
    router.push(ROUTES.USERS_EDIT(user.id));
  };

  const handleDelete = (user: UserRead) => {
    setDeleteUser(user);
  };

  const confirmDelete = async () => {
    if (!deleteUser) return;

    await deleteUserMutation.mutateAsync(deleteUser.id);
    setDeleteUser(null);
    // 成功和错误消息都由 useDeleteUser Hook 统一处理，避免重复提示
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const columns = createUserColumns({
    onEdit: handleEdit,
    onDelete: handleDelete,
    onView: handleView,
  });

  return (
    <MainLayout>
      <PageErrorBoundary
        onError={error => {
          console.error('用户管理页面错误:', error);
        }}
      >
        <div className="space-y-4">
          {/* 紧凑型筛选区域 */}
          <div className="bg-card border rounded-lg p-3">
            <div className="flex flex-wrap items-center gap-3">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium whitespace-nowrap">
                  用户角色:
                </span>
                <Select
                  value={filters.role}
                  onValueChange={value => handleFilterChange('role', value)}
                >
                  <SelectTrigger className="h-8 w-36 text-xs">
                    <SelectValue placeholder="选择用户角色" />
                  </SelectTrigger>
                  <SelectContent>
                    {userRoleOptions.map(option => (
                      <SelectItem
                        key={option.value}
                        value={option.value}
                        className="text-xs"
                      >
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="relative flex-1 min-w-[200px]">
                <Search className="absolute left-2.5 top-1/2 h-3.5 w-3.5 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="搜索用户名、邮箱或手机号..."
                  value={filters.search}
                  onChange={e => handleFilterChange('search', e.target.value)}
                  className="pl-8 h-8 text-xs"
                />
              </div>

              <Button
                size="sm"
                className="h-8 ml-auto whitespace-nowrap"
                onClick={handleAdd}
              >
                <Plus className="mr-1.5 h-3.5 w-3.5" />
                新增用户
              </Button>
            </div>
          </div>

          {/* 用户列表 */}
          <DataStateWrapper
            isLoading={isLoading}
            isEmpty={!isLoading && users.length === 0}
            emptyTitle="还没有用户"
            emptyDescription="系统中还没有任何用户，点击下方按钮添加第一个用户"
            emptyActionLabel="添加用户"
            onEmptyAction={handleAdd}
          >
            <CompactDataTable
              columns={columns}
              data={users}
              isLoading={false}
            />
          </DataStateWrapper>

          {/* 删除确认对话框 */}
          <ConfirmDialog
            {...createDeleteConfirmProps({
              open: !!deleteUser,
              onOpenChange: () => setDeleteUser(null),
              itemName: deleteUser?.username || '',
              itemType: '用户',
              onConfirm: confirmDelete,
              isLoading: deleteUserMutation.isPending,
            })}
          />
        </div>
      </PageErrorBoundary>
    </MainLayout>
  );
}
