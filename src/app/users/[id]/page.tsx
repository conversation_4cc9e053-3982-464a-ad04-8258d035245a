'use client';

import { useRouter } from 'next/navigation';

import { MainLayout } from '@/components/layout/main-layout';
import { UserForm } from '@/components/forms/user-form';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, Loader2 } from 'lucide-react';

import { useUserDetail, useUpdateUser } from '@/hooks/use-users';
import { ROUTES } from '@/constants/routes';
import type { UserUpdate } from '@/types/api';

interface EditUserPageProps {
  params: {
    id: string;
  };
}

export default function EditUserPage({ params }: EditUserPageProps) {
  const router = useRouter();
  const { id } = params;

  // 获取用户详情
  const { data: user, isLoading: isLoadingUser, error } = useUserDetail(id);
  
  // 更新用户
  const updateUserMutation = useUpdateUser();

  const handleSubmit = async (data: UserUpdate) => {
    await updateUserMutation.mutateAsync({ id, data });
    router.push(ROUTES.USERS);
    // 成功和错误消息都由 useUpdateUser Hook 统一处理，避免重复提示
  };

  const handleCancel = () => {
    router.push(ROUTES.USERS);
  };

  if (isLoadingUser) {
    return (
      <MainLayout>
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="flex items-center space-x-2">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span>加载用户信息中...</span>
            </div>
          </CardContent>
        </Card>
      </MainLayout>
    );
  }

  if (error || !user) {
    return (
      <MainLayout>
        <Card>
          <CardHeader>
            <CardTitle>用户不存在</CardTitle>
            <CardDescription>
              无法找到指定的用户，可能已被删除或您没有访问权限。
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={handleCancel}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回用户列表
            </Button>
          </CardContent>
        </Card>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* 返回按钮 */}
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={handleCancel}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回用户列表
          </Button>
        </div>

        {/* 用户表单 */}
        <UserForm
          mode="edit"
          user={user}
          onSubmit={handleSubmit}
          isLoading={updateUserMutation.isPending}
        />
      </div>
    </MainLayout>
  );
}
