'use client';

import { useRouter } from 'next/navigation';

import { MainLayout } from '@/components/layout/main-layout';
import { UserForm } from '@/components/forms/user-form';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

import { useCreateUser } from '@/hooks/use-users';
import { ROUTES } from '@/constants/routes';
import type { UserCreate, UserUpdate } from '@/types/api';

export default function CreateUserPage() {
  const router = useRouter();
  const createUserMutation = useCreateUser();

  const handleSubmit = async (data: UserCreate | UserUpdate) => {
    // 在创建模式下，数据应该是UserCreate类型
    await createUserMutation.mutateAsync(data as UserCreate);
    router.push(ROUTES.USERS);
    // 成功和错误消息都由 useCreateUser Hook 统一处理，避免重复提示
  };

  const handleCancel = () => {
    router.push(ROUTES.USERS);
  };

  return (
    <MainLayout>
      <div className="space-y-4">
        {/* 返回按钮 */}
        <div className="flex items-center">
          <Button
            variant="outline"
            size="sm"
            onClick={handleCancel}
            className="h-8"
          >
            <ArrowLeft className="mr-1.5 h-3.5 w-3.5" />
            返回用户列表
          </Button>
        </div>

        {/* 用户表单 */}
        <UserForm
          mode="create"
          onSubmit={handleSubmit}
          isLoading={createUserMutation.isPending}
        />
      </div>
    </MainLayout>
  );
}
