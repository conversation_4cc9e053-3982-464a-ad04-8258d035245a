'use client';

import { useState } from 'react';
import { MainLayout } from '@/components/layout/main-layout';
import {
  PageErrorBoundary,
  SimpleErrorFallback,
} from '@/components/providers/error-boundary';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { AlertTriangle, Bug, Zap } from 'lucide-react';

/**
 * 故意抛出错误的组件 - 用于测试错误边界
 */
function ErrorComponent({ shouldError }: { shouldError: boolean }) {
  if (shouldError) {
    throw new Error('这是一个测试错误：组件渲染失败');
  }

  return (
    <div className="p-4 bg-green-50 dark:bg-green-900/10 border border-green-200 dark:border-green-800 rounded-lg">
      <p className="text-green-800 dark:text-green-400">✅ 组件正常渲染</p>
    </div>
  );
}

/**
 * 异步错误组件 - 测试异步操作中的错误
 */
function AsyncErrorComponent() {
  const [shouldError, setShouldError] = useState(false);

  const handleAsyncError = async () => {
    try {
      // 模拟异步操作失败
      await new Promise((_, reject) => {
        setTimeout(() => reject(new Error('异步操作失败')), 1000);
      });
    } catch (error) {
      // 在组件中重新抛出错误，会被错误边界捕获
      setShouldError(true);
      throw error;
    }
  };

  if (shouldError) {
    throw new Error('异步操作导致的组件错误');
  }

  return (
    <div className="space-y-4">
      <Button onClick={handleAsyncError} variant="destructive">
        <Zap className="h-4 w-4 mr-2" />
        触发异步错误
      </Button>
      <p className="text-sm text-gray-600 dark:text-gray-400">
        点击按钮模拟异步操作失败
      </p>
    </div>
  );
}

/**
 * 错误测试页面
 */
export default function ErrorTestPage() {
  const [showRenderError, setShowRenderError] = useState(false);
  const [showPageError, setShowPageError] = useState(false);

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center space-x-2">
          <Bug className="h-6 w-6 text-orange-500" />
          <h1 className="text-2xl font-bold tracking-tight">
            错误边界测试页面
          </h1>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* 全局错误边界测试 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5 text-red-500" />
                <span>全局错误边界测试</span>
              </CardTitle>
              <CardDescription>测试全局错误边界捕获页面级错误</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button
                onClick={() => setShowPageError(true)}
                variant="destructive"
                className="w-full"
              >
                触发页面级错误
              </Button>

              {showPageError && <ErrorComponent shouldError={true} />}

              <p className="text-sm text-gray-600 dark:text-gray-400">
                这个错误会被全局错误边界捕获，显示完整的错误页面
              </p>
            </CardContent>
          </Card>

          {/* 页面级错误边界测试 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5 text-orange-500" />
                <span>页面级错误边界测试</span>
              </CardTitle>
              <CardDescription>
                测试页面级错误边界的局部错误处理
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <PageErrorBoundary
                fallback={SimpleErrorFallback}
                onError={error => {
                  console.log('页面级错误边界捕获到错误:', error.message);
                }}
              >
                <Button
                  onClick={() => setShowRenderError(true)}
                  variant="destructive"
                  className="w-full"
                >
                  触发组件渲染错误
                </Button>

                {showRenderError && <ErrorComponent shouldError={true} />}
              </PageErrorBoundary>

              <p className="text-sm text-gray-600 dark:text-gray-400">
                这个错误会被页面级错误边界捕获，只影响局部区域
              </p>
            </CardContent>
          </Card>

          {/* 异步错误测试 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Zap className="h-5 w-5 text-blue-500" />
                <span>异步错误测试</span>
              </CardTitle>
              <CardDescription>测试异步操作中的错误处理</CardDescription>
            </CardHeader>
            <CardContent>
              <PageErrorBoundary
                fallback={SimpleErrorFallback}
                onError={error => {
                  console.log('异步错误被捕获:', error.message);
                }}
              >
                <AsyncErrorComponent />
              </PageErrorBoundary>
            </CardContent>
          </Card>

          {/* 正常组件测试 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <span className="text-green-500">✅</span>
                <span>正常组件</span>
              </CardTitle>
              <CardDescription>
                这个组件正常工作，不会触发错误边界
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ErrorComponent shouldError={false} />
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-4">
                即使其他组件出错，这个组件仍然正常显示
              </p>
            </CardContent>
          </Card>
        </div>

        {/* 重置按钮 */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-center">
              <Button
                onClick={() => {
                  setShowRenderError(false);
                  setShowPageError(false);
                }}
                variant="outline"
              >
                重置所有测试
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 使用说明 */}
        <Card>
          <CardHeader>
            <CardTitle>错误边界说明</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <h4 className="font-medium mb-2">全局错误边界</h4>
                <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                  <li>• 捕获整个应用的渲染错误</li>
                  <li>• 显示完整的错误页面</li>
                  <li>• 防止应用完全崩溃</li>
                  <li>• 提供重试和返回首页功能</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2">页面级错误边界</h4>
                <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                  <li>• 捕获局部组件的渲染错误</li>
                  <li>• 只影响出错的组件区域</li>
                  <li>• 其他功能继续正常工作</li>
                  <li>• 可自定义错误回退UI</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
