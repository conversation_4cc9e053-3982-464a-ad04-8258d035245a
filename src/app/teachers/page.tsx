// 教师管理页面

'use client';

import { useState } from 'react';
import { Plus, Search, ChevronDown, ChevronUp, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { CompactDataTable } from '@/components/tables';
import { DataStateWrapper } from '@/components/ui/loading-states';
import { ConfirmDialog } from '@/components/ui/confirm-dialog';
import { MainLayout } from '@/components/layout/main-layout';
import { PageErrorBoundary } from '@/components/providers/error-boundary';
import { TeacherFormDialog } from '@/components/forms/teacher-form-dialog';
import { TeacherTagManagementDialog } from '@/components/forms/teacher-tag-management-dialog';
import { createTeacherColumns } from '@/components/tables/columns/teacher-columns';
import {
  useAllTeachers,
  useDeleteTeacher,
  useActivateTeacher,
  useDeactivateTeacher,
  useUpdateTeacher,
} from '@/hooks/use-teachers';
import { useTags } from '@/hooks/use-tags';
import { TagStatus, TagWithTeacherCount } from '@/types/api';
import {
  TEACHER_REGION_OPTIONS,
  TEACHER_CATEGORY_OPTIONS,
  TEACHER_STATUS_OPTIONS,
  SHOW_TO_MEMBERS_OPTIONS,

} from '@/constants/teachers';
import type { TeacherList } from '@/types/api';

// 本地筛选状态类型
interface LocalFilters {
  name?: string;
  teacher_category?: string;
  region?: string;
  status?: string;
  show_to_members?: boolean;
  min_price?: number;
  max_price?: number;
  tag_ids: number[];
}

export default function TeachersPage() {
  // 状态管理
  const [localFilters, setLocalFilters] = useState<LocalFilters>({
    name: '',
    tag_ids: [],
  });

  const [selectedTagIds, setSelectedTagIds] = useState<number[]>([]);
  const [filterExpanded, setFilterExpanded] = useState(false);
  const [createOpen, setCreateOpen] = useState(false);
  const [editOpen, setEditOpen] = useState(false);
  const [editingTeacher, setEditingTeacher] = useState<TeacherList | null>(null);
  const [deleteOpen, setDeleteOpen] = useState(false);
  const [deleteTeacher, setDeleteTeacher] = useState<TeacherList | null>(null);
  const [tagManagementOpen, setTagManagementOpen] = useState(false);
  const [managingTeacher, setManagingTeacher] = useState<TeacherList | null>(null);

  // API hooks
  const { data: teachersData, isLoading: teachersLoading, refetch: refetchTeachers } = useAllTeachers();
  const { data: tagsData } = useTags({ status: TagStatus.ACTIVE }); // 获取激活状态的标签
  const deleteMutation = useDeleteTeacher();
  const activateMutation = useActivateTeacher();
  const deactivateMutation = useDeactivateTeacher();
  const updateMutation = useUpdateTeacher();

  // 前端筛选逻辑
  const filterTeachers = (teachers: TeacherList[], filters: LocalFilters): TeacherList[] => {
    return teachers.filter(teacher => {
      // 姓名筛选
      if (filters.name && !teacher.name.toLowerCase().includes(filters.name.toLowerCase())) {
        return false;
      }

      // 分类筛选
      if (filters.teacher_category && teacher.teacher_category !== filters.teacher_category) {
        return false;
      }

      // 区域筛选
      if (filters.region && teacher.region !== filters.region) {
        return false;
      }

      // 状态筛选
      if (filters.status && teacher.status !== filters.status) {
        return false;
      }

      // 会员可见筛选
      if (filters.show_to_members !== undefined && teacher.show_to_members !== filters.show_to_members) {
        return false;
      }

      // 价格区间筛选
      if (filters.min_price !== undefined && teacher.price_per_class < filters.min_price) {
        return false;
      }
      if (filters.max_price !== undefined && teacher.price_per_class > filters.max_price) {
        return false;
      }

      // 标签筛选
      if (filters.tag_ids.length > 0) {
        const teacherTagIds = teacher.tags?.map(tag => tag.id) || [];
        const hasMatchingTag = filters.tag_ids.some(tagId => teacherTagIds.includes(tagId));
        if (!hasMatchingTag) {
          return false;
        }
      }

      return true;
    });
  };

  // 计算筛选后的数据
  const filteredTeachers = filterTeachers(teachersData?.data || [], localFilters);

  // 重置筛选
  const handleResetFilters = () => {
    setLocalFilters({
      name: '',
      tag_ids: [],
    });
    setSelectedTagIds([]);
    refetchTeachers();
  };

  // 获取当前筛选条件的显示信息
  const getActiveFilters = () => {
    const filters = [];

    if (localFilters.name) {
      filters.push({ key: 'name', label: '姓名', value: localFilters.name });
    }

    if (localFilters.teacher_category) {
      const option = TEACHER_CATEGORY_OPTIONS.find(opt => opt.value === localFilters.teacher_category);
      filters.push({ key: 'teacher_category', label: '分类', value: option?.label || localFilters.teacher_category });
    }

    if (localFilters.region) {
      const option = TEACHER_REGION_OPTIONS.find(opt => opt.value === localFilters.region);
      filters.push({ key: 'region', label: '区域', value: option?.label || localFilters.region });
    }

    if (localFilters.status) {
      const option = TEACHER_STATUS_OPTIONS.find(opt => opt.value === localFilters.status);
      filters.push({ key: 'status', label: '状态', value: option?.label || localFilters.status });
    }

    if (localFilters.show_to_members !== undefined) {
      filters.push({
        key: 'show_to_members',
        label: '会员可见',
        value: localFilters.show_to_members ? '展示' : '隐藏'
      });
    }

    if (localFilters.min_price !== undefined || localFilters.max_price !== undefined) {
      const min = localFilters.min_price || 0;
      const max = localFilters.max_price || '∞';
      filters.push({ key: 'price', label: '价格区间', value: `${min} - ${max}元` });
    }

    if (localFilters.tag_ids.length > 0) {
      const tagNames = localFilters.tag_ids.map(id => {
        const tag = tagsData?.data?.find((tag: TagWithTeacherCount) => tag.id === id);
        return tag?.name || `标签${id}`;
      }).join(', ');
      filters.push({ key: 'tags', label: '标签', value: tagNames });
    }

    return filters;
  };

  // 移除单个筛选条件
  const removeFilter = (key: string) => {
    const newFilters = { ...localFilters };

    switch (key) {
      case 'name':
        newFilters.name = '';
        break;
      case 'teacher_category':
        newFilters.teacher_category = undefined;
        break;
      case 'region':
        newFilters.region = undefined;
        break;
      case 'status':
        newFilters.status = undefined;
        break;
      case 'show_to_members':
        newFilters.show_to_members = undefined;
        break;
      case 'price':
        newFilters.min_price = undefined;
        newFilters.max_price = undefined;
        break;
      case 'tags':
        newFilters.tag_ids = [];
        setSelectedTagIds([]);
        break;
    }

    setLocalFilters(newFilters);
  };

  // 事件处理
  const handleFilterChange = (key: keyof LocalFilters, value: string | number | boolean | undefined) => {
    setLocalFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  };



  const handleCreate = () => {
    setCreateOpen(true);
  };

  const handleEdit = (teacher: TeacherList) => {
    setEditingTeacher(teacher);
    setEditOpen(true);
  };

  const handleView = (teacher: TeacherList) => {
    // TODO: 实现查看详情
    console.log('查看教师详情:', teacher);
  };

  const handleDelete = (teacher: TeacherList) => {
    setDeleteTeacher(teacher);
    setDeleteOpen(true);
  };

  const handleStatusToggle = async (teacher: TeacherList) => {
    try {
      if (teacher.status === 'active') {
        await deactivateMutation.mutateAsync(teacher.id.toString());
      } else {
        await activateMutation.mutateAsync(teacher.id.toString());
      }
    } catch (error) {
      console.error('状态切换失败:', error);
    }
  };

  const handleShowToMembersToggle = async (teacher: TeacherList) => {
    try {
      await updateMutation.mutateAsync({
        id: teacher.id.toString(),
        data: {
          show_to_members: !teacher.show_to_members
        }
      });
    } catch (error) {
      console.error('会员可见状态切换失败:', error);
    }
  };

  const handleManageTags = (teacher: TeacherList) => {
    setManagingTeacher(teacher);
    setTagManagementOpen(true);
  };

  // 标签多选处理
  const handleTagToggle = (tagId: number) => {
    setSelectedTagIds(prev => {
      const newIds = prev.includes(tagId)
        ? prev.filter(id => id !== tagId)
        : [...prev, tagId];

      // 更新本地筛选条件
      setLocalFilters(prevFilters => ({
        ...prevFilters,
        tag_ids: newIds,
      }));

      return newIds;
    });
  };

  const confirmDelete = async () => {
    if (!deleteTeacher) return;
    
    try {
      await deleteMutation.mutateAsync(deleteTeacher.id.toString());
      setDeleteOpen(false);
      setDeleteTeacher(null);
    } catch (error) {
      console.error('删除失败:', error);
    }
  };



  // 表格列配置
  const columns = createTeacherColumns({
    onEdit: handleEdit,
    onDelete: handleDelete,
    onView: handleView,
    onStatusToggle: handleStatusToggle,
    onShowToMembersToggle: handleShowToMembersToggle,
    onManageTags: handleManageTags,
  });



  return (
    <MainLayout>
      <PageErrorBoundary>
        <div className="space-y-6">
          {/* 主要内容 */}
          <Card>
            <CardHeader>
              {!filterExpanded && (
                <div className="flex items-left justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="relative flex-1 max-w-sm">
                      <Search className="absolute left-2.5 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                      <Input
                        placeholder="搜索教师姓名..."
                        value={localFilters.name || ''}
                        onChange={e => handleFilterChange('name', e.target.value)}
                        className="pl-8"
                      />
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button onClick={handleCreate}>
                      <Plus className="mr-2 h-4 w-4" />
                      新增教师
                    </Button>
                  </div>
                </div>
              )}
            </CardHeader>
            <CardContent>
              {/* 筛选区域 */}
              <div className="space-y-4 mb-6">
                <Collapsible open={filterExpanded} onOpenChange={setFilterExpanded}>
                  {/* 筛选条件显示条 */}
                  <div className="flex items-center justify-between p-3 border rounded-lg bg-muted/10">
                    <div className="flex items-center gap-2 flex-wrap">
                      {getActiveFilters().map((filter) => (
                        <Badge
                          key={filter.key}
                          variant="secondary"
                          className="flex items-center gap-1 cursor-pointer hover:bg-destructive/10 hover:text-destructive transition-colors"
                          onClick={() => removeFilter(filter.key)}
                        >
                          <span className="text-xs font-medium">{filter.label}:</span>
                          <span className="text-xs">{filter.value}</span>
                          <X className="h-3 w-3 ml-1" />
                        </Badge>
                      ))}

                      {getActiveFilters().length === 0 && (
                        <span className="text-sm text-muted-foreground">暂无筛选条件</span>
                      )}
                    </div>

                    <div className="flex items-center gap-2">
                      {getActiveFilters().length > 0 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={handleResetFilters}
                          className="text-xs h-7"
                        >
                          清空全部
                        </Button>
                      )}

                      <CollapsibleTrigger asChild>
                        <Button variant="outline" size="sm" className="text-xs h-7">
                          {filterExpanded ? (
                            <>
                              <ChevronUp className="h-3 w-3 mr-1" />
                              收起筛选
                            </>
                          ) : (
                            <>
                              <ChevronDown className="h-3 w-3 mr-1" />
                              展开筛选
                            </>
                          )}
                        </Button>
                      </CollapsibleTrigger>
                    </div>
                  </div>

                  {/* 可折叠的详细筛选面板 */}
                  <CollapsibleContent>
                    <div className="mt-2 space-y-2 p-4 border rounded-lg bg-muted/20">
                      

                  {/* 基础筛选 - 超紧凑布局 */}
                  <div className="flex flex-wrap gap-4">
                    <div className="flex items-center gap-2">
                      <label className="text-xs font-medium text-muted-foreground min-w-fit shrink-0">教师分类:</label>
                      <Select
                        value={localFilters.teacher_category || 'all'}
                        onValueChange={(value) =>
                          handleFilterChange('teacher_category', value === 'all' ? undefined : value)
                        }
                      >
                        <SelectTrigger className="h-8 text-xs w-28">
                          <SelectValue placeholder="选择分类" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">全部分类</SelectItem>
                          {TEACHER_CATEGORY_OPTIONS.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-center gap-2">
                      <label className="text-xs font-medium text-muted-foreground min-w-fit shrink-0">教师区域:</label>
                      <Select
                        value={localFilters.region || 'all'}
                        onValueChange={(value) =>
                          handleFilterChange('region', value === 'all' ? undefined : value)
                        }
                      >
                        <SelectTrigger className="h-8 text-xs w-28">
                          <SelectValue placeholder="选择区域" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">全部区域</SelectItem>
                          {TEACHER_REGION_OPTIONS.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-center gap-2">
                      <label className="text-xs font-medium text-muted-foreground min-w-fit shrink-0">教师状态:</label>
                      <Select
                        value={localFilters.status || 'all'}
                        onValueChange={(value) =>
                          handleFilterChange('status', value === 'all' ? undefined : value)
                        }
                      >
                        <SelectTrigger className="h-8 text-xs w-24">
                          <SelectValue placeholder="选择状态" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">全部状态</SelectItem>
                          {TEACHER_STATUS_OPTIONS.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-center gap-2">
                      <label className="text-xs font-medium text-muted-foreground min-w-fit shrink-0">会员可见:</label>
                      <Select
                        value={localFilters.show_to_members?.toString() || 'all'}
                        onValueChange={(value) =>
                          handleFilterChange('show_to_members', value === 'all' ? undefined : value === 'true')
                        }
                      >
                        <SelectTrigger className="h-8 text-xs w-24">
                          <SelectValue placeholder="选择可见性" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">全部</SelectItem>
                          {SHOW_TO_MEMBERS_OPTIONS.map((option) => (
                            <SelectItem key={option.value.toString()} value={option.value.toString()}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* 价格区间筛选 - 超紧凑布局 */}
                  <div className="flex items-center gap-2">
                    <label className="text-xs font-medium text-muted-foreground min-w-fit shrink-0">课时费区间:</label>
                    <div className="flex items-center space-x-2">
                      <Input
                        type="number"
                        placeholder="最低"
                        value={localFilters.min_price || ''}
                        onChange={(e) => handleFilterChange('min_price', e.target.value ? Number(e.target.value) : undefined)}
                        className="w-20 h-8 text-xs"
                      />
                      <span className="text-xs text-muted-foreground">-</span>
                      <Input
                        type="number"
                        placeholder="最高"
                        value={localFilters.max_price || ''}
                        onChange={(e) => handleFilterChange('max_price', e.target.value ? Number(e.target.value) : undefined)}
                        className="w-20 h-8 text-xs"
                      />
                      <span className="text-xs text-muted-foreground">元</span>
                    </div>
                  </div>

                  {/* 标签多选区域 - 横向布局 */}
                  <div className="space-y-2">
                    <label className="text-md font-medium text-muted-foreground">教师标签</label>
                    <div className="space-y-2">
                      {/* 按分类展示标签 - 横向布局 */}
                      {tagsData?.data && (
                        <div className="space-y-2">
                          {/* 获取所有分类 */}
                          {Array.from(new Set(tagsData.data.map((tag: TagWithTeacherCount) => tag.category_name).filter(Boolean))).map((categoryName) => (
                            <div key={categoryName as string} className="flex items-center gap-3">
                              <h4 className="text-xs font-medium text-muted-foreground min-w-fit shrink-0">{categoryName as string}:</h4>
                              <div className="flex flex-wrap gap-2">
                                {tagsData.data
                                  .filter((tag: TagWithTeacherCount) => tag.category_name === categoryName)
                                  .map((tag: TagWithTeacherCount) => (
                                    <Badge
                                      key={tag.id}
                                      variant={selectedTagIds.includes(tag.id) ? 'default' : 'outline'}
                                      className="cursor-pointer hover:opacity-80 transition-opacity text-xs px-2 py-1 h-6"
                                      onClick={() => handleTagToggle(tag.id)}
                                    >
                                      {tag.name}
                                      {tag.teacher_count > 0 && (
                                        <span className="ml-1 text-xs opacity-70">({tag.teacher_count})</span>
                                      )}
                                    </Badge>
                                  ))}
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              </div>

              {/* 数据表格 */}
              <DataStateWrapper
                isLoading={teachersLoading}
                isEmpty={!filteredTeachers.length}
                emptyTitle={
                  localFilters.name || localFilters.teacher_category || localFilters.region ||
                  localFilters.status || localFilters.show_to_members !== undefined ||
                  localFilters.min_price !== undefined || localFilters.max_price !== undefined ||
                  localFilters.tag_ids.length > 0
                    ? "搜索结果为空"
                    : "暂无教师"
                }
                emptyDescription={
                  localFilters.name || localFilters.teacher_category || localFilters.region ||
                  localFilters.status || localFilters.show_to_members !== undefined ||
                  localFilters.min_price !== undefined || localFilters.max_price !== undefined ||
                  localFilters.tag_ids.length > 0
                    ? "没有找到符合条件的教师，请调整搜索条件"
                    : "还没有添加任何教师，点击按钮创建第一个教师"
                }
                emptyActionLabel="新增教师"
                onEmptyAction={handleCreate}
              >
                <CompactDataTable
                  columns={columns}
                  data={filteredTeachers}
                  isLoading={teachersLoading}
                />
              </DataStateWrapper>
            </CardContent>
          </Card>

          {/* 弹框组件 */}
          <TeacherFormDialog
            open={createOpen}
            onOpenChange={setCreateOpen}
            teacher={null}
          />

          <TeacherFormDialog
            open={editOpen}
            onOpenChange={setEditOpen}
            teacher={editingTeacher}
          />

          <ConfirmDialog
            open={deleteOpen}
            onOpenChange={setDeleteOpen}
            title="确认删除"
            description={`确定要删除教师"${deleteTeacher?.name}"吗？此操作无法撤销。`}
            variant="destructive"
            onConfirm={confirmDelete}
            isLoading={deleteMutation.isPending}
          />

          <TeacherTagManagementDialog
            open={tagManagementOpen}
            onOpenChange={setTagManagementOpen}
            teacher={managingTeacher}
          />
        </div>
      </PageErrorBoundary>
    </MainLayout>
  );
}
