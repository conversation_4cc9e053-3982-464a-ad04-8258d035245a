'use client';

import { MainLayout } from '@/components/layout/main-layout';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { CheckCircle, AlertTriangle, Info, XCircle } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  LoadingSpinner,
  EmptyState,
  ErrorState,
  LoadingCard,
  Skeleton
} from '@/components/ui/loading-states';

export default function UIDesignSystemPage() {
  return (
    <MainLayout>
      <div className="container py-6 px-6">
        <h1 className="text-2xl font-semibold mb-6">UI 设计系统</h1>
        <p className="text-muted-foreground mb-8">
          本页面展示了项目中使用的UI设计元素规范，包括色值、字体大小、按钮大小等核心设计元素。
        </p>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 色值系统 */}
          <Card className="col-span-1 lg:col-span-2">
            <CardHeader>
              <CardTitle>色值系统</CardTitle>
              <CardDescription>项目中使用的主题色和语义色</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-3">主题色</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                    {['blue', 'purple', 'teal', 'gray'].map(theme => (
                      <div key={theme} className="space-y-2">
                        <div className={`theme-${theme} flex flex-col gap-1`}>
                          <div className="h-12 bg-primary rounded-md flex items-center justify-center text-primary-foreground font-medium">
                            Primary
                          </div>
                          <div className="grid grid-cols-2 gap-1">
                            <div className="h-8 bg-primary-hover rounded-md flex items-center justify-center text-primary-foreground text-xs">
                              Hover
                            </div>
                            <div className="h-8 bg-primary-active rounded-md flex items-center justify-center text-primary-foreground text-xs">
                              Active
                            </div>
                          </div>
                        </div>
                        <div className="text-sm font-medium text-center capitalize">
                          {theme} 主题
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <Separator />

                <div>
                  <h3 className="text-lg font-medium mb-3">语义色</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="space-y-1">
                      <div className="h-10 bg-background border rounded-md flex items-center justify-center">
                        Background
                      </div>
                      <div className="text-xs text-center text-muted-foreground">
                        bg-background
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="h-10 bg-card border rounded-md flex items-center justify-center">
                        Card
                      </div>
                      <div className="text-xs text-center text-muted-foreground">
                        bg-card
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="h-10 bg-muted rounded-md flex items-center justify-center">
                        Muted
                      </div>
                      <div className="text-xs text-center text-muted-foreground">
                        bg-muted
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="h-10 bg-accent rounded-md flex items-center justify-center">
                        Accent
                      </div>
                      <div className="text-xs text-center text-muted-foreground">
                        bg-accent
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="h-10 bg-secondary rounded-md flex items-center justify-center text-secondary-foreground">
                        Secondary
                      </div>
                      <div className="text-xs text-center text-muted-foreground">
                        bg-secondary
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="h-10 bg-destructive rounded-md flex items-center justify-center text-destructive-foreground">
                        Destructive
                      </div>
                      <div className="text-xs text-center text-muted-foreground">
                        bg-destructive
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="h-10 border rounded-md flex items-center justify-center">
                        Border
                      </div>
                      <div className="text-xs text-center text-muted-foreground">
                        border-border
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="h-10 ring-2 ring-ring rounded-md flex items-center justify-center">
                        Ring
                      </div>
                      <div className="text-xs text-center text-muted-foreground">
                        ring-ring
                      </div>
                    </div>
                  </div>
                </div>

                <Separator />

                <div>
                  <h3 className="text-lg font-medium mb-3">文本颜色</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="space-y-1">
                      <div className="h-10 flex items-center justify-center text-foreground font-medium">
                        默认文本
                      </div>
                      <div className="text-xs text-center text-muted-foreground">
                        text-foreground
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="h-10 flex items-center justify-center text-muted-foreground">
                        弱化文本
                      </div>
                      <div className="text-xs text-center text-muted-foreground">
                        text-muted-foreground
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="h-10 flex items-center justify-center text-primary font-medium">
                        主题文本
                      </div>
                      <div className="text-xs text-center text-muted-foreground">
                        text-primary
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="h-10 flex items-center justify-center text-destructive font-medium">
                        危险文本
                      </div>
                      <div className="text-xs text-center text-muted-foreground">
                        text-destructive
                      </div>
                    </div>
                  </div>
                </div>

                <Separator />

                <div>
                  <h3 className="text-lg font-medium mb-3">状态色</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="space-y-1">
                      <div className="h-10 bg-success rounded-md flex items-center justify-center text-success-foreground font-medium">
                        成功
                      </div>
                      <div className="text-xs text-center text-muted-foreground">
                        bg-success
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="h-10 bg-warning rounded-md flex items-center justify-center text-warning-foreground font-medium">
                        警告
                      </div>
                      <div className="text-xs text-center text-muted-foreground">
                        bg-warning
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="h-10 bg-info rounded-md flex items-center justify-center text-info-foreground font-medium">
                        信息
                      </div>
                      <div className="text-xs text-center text-muted-foreground">
                        bg-info
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="h-10 bg-destructive rounded-md flex items-center justify-center text-destructive-foreground font-medium">
                        危险
                      </div>
                      <div className="text-xs text-center text-muted-foreground">
                        bg-destructive
                      </div>
                    </div>
                  </div>
                </div>

                <Separator />

                <div>
                  <h3 className="text-lg font-medium mb-3">图表色</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-5 gap-4">
                    {[1, 2, 3, 4, 5].map(num => (
                      <div key={num} className="space-y-1">
                        <div
                          className={`h-10 bg-chart-${num} rounded-md flex items-center justify-center text-white font-medium`}
                        >
                          图表色 {num}
                        </div>
                        <div className="text-xs text-center text-muted-foreground">
                          bg-chart-{num}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 字体大小 */}
          <Card>
            <CardHeader>
              <CardTitle>字体大小</CardTitle>
              <CardDescription>项目中使用的文本大小规范</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-xs">文本 XS (12px)</span>
                  <Badge variant="outline" className="text-xs">
                    text-xs
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">文本 SM (14px)</span>
                  <Badge variant="outline" className="text-xs">
                    text-sm (默认)
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-base">文本 Base (14px)</span>
                  <Badge variant="outline" className="text-xs">
                    text-base
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-lg">文本 LG (16px)</span>
                  <Badge variant="outline" className="text-xs">
                    text-lg
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-xl">文本 XL (18px)</span>
                  <Badge variant="outline" className="text-xs">
                    text-xl
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-2xl">文本 2XL (24px)</span>
                  <Badge variant="outline" className="text-xs">
                    text-2xl
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-3xl">文本 3XL (30px)</span>
                  <Badge variant="outline" className="text-xs">
                    text-3xl
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 按钮大小 */}
          <Card>
            <CardHeader>
              <CardTitle>按钮大小</CardTitle>
              <CardDescription>项目中使用的按钮尺寸规范</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Button size="xs">超小按钮 (XS)</Button>
                    <Badge variant="outline" className="text-xs">
                      size="xs"
                    </Badge>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    高度: 24px (h-6) | 内边距: px-2 py-0.5 | 字体: text-xs
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Button size="sm">小按钮 (SM)</Button>
                    <Badge variant="outline" className="text-xs">
                      size="sm"
                    </Badge>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    高度: 28px (h-7) | 内边距: px-2.5 py-1 | 字体: text-xs
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Button>默认按钮 (Default)</Button>
                    <Badge variant="outline" className="text-xs">
                      size="default"
                    </Badge>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    高度: 32px (h-8) | 内边距: px-3 py-1.5 | 字体: text-sm
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Button size="lg">大按钮 (LG)</Button>
                    <Badge variant="outline" className="text-xs">
                      size="lg"
                    </Badge>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    高度: 40px (h-10) | 内边距: px-6 py-2 | 字体: text-sm
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Button size="icon">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M5 12h14" />
                        <path d="M12 5v14" />
                      </svg>
                    </Button>
                    <Badge variant="outline" className="text-xs">
                      size="icon"
                    </Badge>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    尺寸: 32px x 32px (h-8 w-8)
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 按钮变体 */}
          <Card>
            <CardHeader>
              <CardTitle>按钮变体</CardTitle>
              <CardDescription>项目中使用的按钮样式变体</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Button variant="default">默认按钮</Button>
                    <Badge variant="outline" className="text-xs">
                      variant="default"
                    </Badge>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Button variant="secondary">次要按钮</Button>
                    <Badge variant="outline" className="text-xs">
                      variant="secondary"
                    </Badge>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Button variant="outline">边框按钮</Button>
                    <Badge variant="outline" className="text-xs">
                      variant="outline"
                    </Badge>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Button variant="ghost">幽灵按钮</Button>
                    <Badge variant="outline" className="text-xs">
                      variant="ghost"
                    </Badge>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Button variant="link">链接按钮</Button>
                    <Badge variant="outline" className="text-xs">
                      variant="link"
                    </Badge>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Button variant="success">成功按钮</Button>
                    <Badge variant="outline" className="text-xs">
                      variant="success"
                    </Badge>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Button variant="warning">警告按钮</Button>
                    <Badge variant="outline" className="text-xs">
                      variant="warning"
                    </Badge>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Button variant="info">信息按钮</Button>
                    <Badge variant="outline" className="text-xs">
                      variant="info"
                    </Badge>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Button variant="destructive">危险按钮</Button>
                    <Badge variant="outline" className="text-xs">
                      variant="destructive"
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 间距系统 */}
          <Card>
            <CardHeader>
              <CardTitle>间距系统</CardTitle>
              <CardDescription>项目中使用的间距规范</CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[400px] pr-4">
                <div className="space-y-6">
                  {[
                    { name: '0.5', value: '0.125rem (2px)', class: 'p-0.5' },
                    { name: '1', value: '0.25rem (4px)', class: 'p-1' },
                    { name: '1.5', value: '0.375rem (6px)', class: 'p-1.5' },
                    { name: '2', value: '0.5rem (8px)', class: 'p-2' },
                    { name: '2.5', value: '0.625rem (10px)', class: 'p-2.5' },
                    { name: '3', value: '0.75rem (12px)', class: 'p-3' },
                    { name: '3.5', value: '0.875rem (14px)', class: 'p-3.5' },
                    { name: '4', value: '1rem (16px)', class: 'p-4' },
                  ].map(spacing => (
                    <div key={spacing.name} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div
                            className={`${spacing.class} bg-primary text-primary-foreground rounded flex items-center justify-center`}
                          >
                            示例
                          </div>
                          <span>间距 {spacing.name}</span>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          p-{spacing.name} / m-{spacing.name}
                        </Badge>
                      </div>
                      <div className="text-xs text-muted-foreground">
                        大小: {spacing.value}
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>

          {/* Alert组件展示 */}
          <Card className="col-span-1 lg:col-span-2">
            <CardHeader>
              <CardTitle>Alert 组件</CardTitle>
              <CardDescription>基于状态色系统的提示组件</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-medium mb-3">状态提示</h3>
                  <div className="space-y-4">
                    <Alert variant="success">
                      <CheckCircle className="h-4 w-4" />
                      <AlertTitle>成功</AlertTitle>
                      <AlertDescription>
                        您的操作已成功完成。数据已保存到系统中。
                      </AlertDescription>
                    </Alert>

                    <Alert variant="warning">
                      <AlertTriangle className="h-4 w-4" />
                      <AlertTitle>警告</AlertTitle>
                      <AlertDescription>
                        请注意：此操作可能会影响现有数据，建议先备份。
                      </AlertDescription>
                    </Alert>

                    <Alert variant="info">
                      <Info className="h-4 w-4" />
                      <AlertTitle>信息</AlertTitle>
                      <AlertDescription>
                        系统将在今晚23:00-01:00进行维护，期间可能无法访问。
                      </AlertDescription>
                    </Alert>

                    <Alert variant="destructive">
                      <XCircle className="h-4 w-4" />
                      <AlertTitle>错误</AlertTitle>
                      <AlertDescription>
                        操作失败：网络连接超时，请检查网络设置后重试。
                      </AlertDescription>
                    </Alert>

                    <Alert>
                      <Info className="h-4 w-4" />
                      <AlertTitle>默认提示</AlertTitle>
                      <AlertDescription>
                        这是一个默认样式的提示信息。
                      </AlertDescription>
                    </Alert>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Badge组件展示 */}
          <Card>
            <CardHeader>
              <CardTitle>Badge 组件</CardTitle>
              <CardDescription>状态标签和徽章组件</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-3">状态徽章</h3>
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="default">默认</Badge>
                    <Badge variant="secondary">次要</Badge>
                    <Badge variant="success">成功</Badge>
                    <Badge variant="warning">警告</Badge>
                    <Badge variant="info">信息</Badge>
                    <Badge variant="destructive">危险</Badge>
                    <Badge variant="outline">边框</Badge>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-3">使用示例</h3>
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <span className="text-sm">用户状态：</span>
                      <Badge variant="success">在线</Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm">订单状态：</span>
                      <Badge variant="warning">待处理</Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm">系统状态：</span>
                      <Badge variant="info">维护中</Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm">错误状态：</span>
                      <Badge variant="destructive">失败</Badge>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 表单验证样式 */}
          <Card className="col-span-1 lg:col-span-2">
            <CardHeader>
              <CardTitle>表单验证样式</CardTitle>
              <CardDescription>统一的表单验证状态展示</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-3">输入框状态</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="normal-input">正常状态</Label>
                      <Input
                        id="normal-input"
                        placeholder="请输入内容"
                      />
                      <p className="text-xs text-muted-foreground">这是正常状态的输入框</p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="error-input">错误状态</Label>
                      <Input
                        id="error-input"
                        placeholder="请输入内容"
                        className="border-destructive"
                      />
                      <p className="text-xs text-destructive">这是必填字段，请输入内容</p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="success-input">成功状态</Label>
                      <Input
                        id="success-input"
                        placeholder="请输入内容"
                        className="border-success"
                        value="验证通过的内容"
                        readOnly
                      />
                      <p className="text-xs text-success">输入格式正确</p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="warning-input">警告状态</Label>
                      <Input
                        id="warning-input"
                        placeholder="请输入内容"
                        className="border-warning"
                        value="需要注意的内容"
                        readOnly
                      />
                      <p className="text-xs text-warning">建议使用更强的密码</p>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-3">表单验证规范</h3>
                  <div className="space-y-3 text-sm">
                    <div className="flex items-start gap-2">
                      <Badge variant="destructive" className="mt-0.5">错误</Badge>
                      <div>
                        <p className="font-medium">使用 <code>border-destructive</code> 和 <code>text-destructive</code></p>
                        <p className="text-muted-foreground">用于必填字段验证失败、格式错误等</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-2">
                      <Badge variant="success" className="mt-0.5">成功</Badge>
                      <div>
                        <p className="font-medium">使用 <code>border-success</code> 和 <code>text-success</code></p>
                        <p className="text-muted-foreground">用于验证通过、操作成功等</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-2">
                      <Badge variant="warning" className="mt-0.5">警告</Badge>
                      <div>
                        <p className="font-medium">使用 <code>border-warning</code> 和 <code>text-warning</code></p>
                        <p className="text-muted-foreground">用于提醒用户注意、建议优化等</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-2">
                      <Badge variant="info" className="mt-0.5">信息</Badge>
                      <div>
                        <p className="font-medium">使用 <code>border-info</code> 和 <code>text-info</code></p>
                        <p className="text-muted-foreground">用于一般提示信息、帮助说明等</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 圆角和阴影 */}
          <Card className="col-span-1 lg:col-span-2">
            <CardHeader>
              <CardTitle>圆角和阴影</CardTitle>
              <CardDescription>项目中使用的圆角和阴影规范</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium mb-3">圆角</h3>
                  <div className="grid grid-cols-2 gap-4">
                    {[
                      { name: 'sm', value: '2px', class: 'rounded-sm' },
                      { name: 'md', value: '3px', class: 'rounded-md' },
                      { name: 'lg', value: '4px', class: 'rounded-lg' },
                      { name: 'full', value: '50%', class: 'rounded-full' },
                    ].map(radius => (
                      <div key={radius.name} className="space-y-2">
                        <div
                          className={`h-16 bg-muted ${radius.class} flex items-center justify-center`}
                        >
                          {radius.name}
                        </div>
                        <div className="text-xs text-center text-muted-foreground">
                          {radius.class} ({radius.value})
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-3">阴影</h3>
                  <div className="grid grid-cols-2 gap-4">
                    {[
                      { name: 'sm', class: 'shadow-sm' },
                      { name: 'DEFAULT', class: 'shadow' },
                      { name: 'md', class: 'shadow-md' },
                      { name: 'lg', class: 'shadow-lg' },
                    ].map(shadow => (
                      <div key={shadow.name} className="space-y-2">
                        <div
                          className={`h-16 bg-card ${shadow.class} rounded-md flex items-center justify-center`}
                        >
                          {shadow.name === 'DEFAULT' ? 'default' : shadow.name}
                        </div>
                        <div className="text-xs text-center text-muted-foreground">
                          {shadow.class}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="mt-8 p-4 bg-muted rounded-md">
          <h3 className="text-lg font-medium mb-2">最佳实践</h3>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>
              <strong>使用语义化类名</strong>：优先使用 <code>text-primary</code>{' '}
              而非直接使用颜色值如 <code>text-blue-500</code>
            </li>
            <li>
              <strong>状态色使用</strong>：使用 <code>text-success</code>、<code>text-warning</code>、
              <code>text-info</code>、<code>text-destructive</code> 表示不同状态
            </li>
            <li>
              <strong>遵循间距规范</strong>
              ：使用定义好的间距类，避免使用自定义数值
            </li>
            <li>
              <strong>响应式设计</strong>：使用Tailwind的响应式前缀{' '}
              <code>sm:</code>, <code>md:</code>, <code>lg:</code> 等
            </li>
            <li>
              <strong>暗色模式兼容</strong>
              ：确保组件在暗色模式下正常显示，使用语义化颜色变量
            </li>
            <li>
              <strong>字体大小一致性</strong>：正文使用 <code>text-sm</code>
              ，标题使用规范中定义的尺寸
            </li>
          </ul>

          {/* 状态色使用示例 */}
          <div className="mt-6 p-4 bg-card border rounded-md">
            <h4 className="text-base font-medium mb-3">状态色使用示例</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="p-3 bg-success/10 border border-success/30 rounded-md">
                  <p className="text-success font-medium">✓ 操作成功</p>
                  <p className="text-success/80 text-xs">数据已保存</p>
                </div>
                <code className="text-xs">bg-success/10 border-success/30 text-success</code>
              </div>

              <div className="space-y-2">
                <div className="p-3 bg-warning/10 border border-warning/30 rounded-md">
                  <p className="text-warning font-medium">⚠ 注意事项</p>
                  <p className="text-warning/80 text-xs">请检查输入内容</p>
                </div>
                <code className="text-xs">bg-warning/10 border-warning/30 text-warning</code>
              </div>

              <div className="space-y-2">
                <div className="p-3 bg-info/10 border border-info/30 rounded-md">
                  <p className="text-info font-medium">ℹ 提示信息</p>
                  <p className="text-info/80 text-xs">这是一条提示消息</p>
                </div>
                <code className="text-xs">bg-info/10 border-info/30 text-info</code>
              </div>

              <div className="space-y-2">
                <div className="p-3 bg-destructive/10 border border-destructive/30 rounded-md">
                  <p className="text-destructive font-medium">✗ 错误信息</p>
                  <p className="text-destructive/80 text-xs">操作失败，请重试</p>
                </div>
                <code className="text-xs">bg-destructive/10 border-destructive/30 text-destructive</code>
              </div>
            </div>
          </div>

          {/* 数据状态组件 */}
          <div className="mt-8">
            <Card>
              <CardHeader>
                <CardTitle>数据状态组件</CardTitle>
                <CardDescription>加载、空状态、错误状态的统一展示</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-8">
                  <div>
                    <h3 className="text-lg font-medium mb-4">加载状态</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="space-y-2">
                        <h4 className="text-sm font-medium">小尺寸</h4>
                        <div className="p-4 border rounded-md">
                          <LoadingSpinner size="sm" text="加载中..." />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <h4 className="text-sm font-medium">中等尺寸</h4>
                        <div className="p-4 border rounded-md">
                          <LoadingSpinner size="md" text="正在处理..." />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <h4 className="text-sm font-medium">大尺寸</h4>
                        <div className="p-4 border rounded-md">
                          <LoadingSpinner size="lg" text="数据加载中..." />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-4">空状态</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <h4 className="text-sm font-medium">基础空状态</h4>
                        <div className="p-4 border rounded-md">
                          <EmptyState />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <h4 className="text-sm font-medium">带操作的空状态</h4>
                        <div className="p-4 border rounded-md">
                          <EmptyState
                            title="还没有数据"
                            description="点击下方按钮开始添加您的第一条记录"
                            action={{
                              label: "添加数据",
                              onClick: () => alert("添加数据")
                            }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-4">错误状态</h3>
                    <div className="p-4 border rounded-md">
                      <ErrorState
                        title="数据加载失败"
                        description="网络连接异常，请检查您的网络设置后重试"
                        onRetry={() => alert("重试加载")}
                      />
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-4">骨架屏</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <h4 className="text-sm font-medium">基础骨架屏</h4>
                        <div className="p-4 border rounded-md">
                          <Skeleton lines={3} />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <h4 className="text-sm font-medium">卡片骨架屏</h4>
                        <LoadingCard title="加载中的卡片" description="内容正在加载..." />
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
