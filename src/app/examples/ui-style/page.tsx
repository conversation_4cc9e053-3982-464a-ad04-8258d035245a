'use client';

import { MainLayout } from '@/components/layout/main-layout';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { FormRow, FormGroup, FormActions } from '@/components/forms/form-row';
import { CompactDataTable } from '@/components/tables/common/compact-data-table';
import { ColumnDef } from '@tanstack/react-table';

// 示例表格数据
type User = {
  id: string;
  name: string;
  email: string;
  role: string;
  status: string;
};

const users: User[] = [
  {
    id: '1',
    name: '张三',
    email: '<EMAIL>',
    role: '管理员',
    status: '活跃',
  },
  {
    id: '2',
    name: '李四',
    email: '<EMAIL>',
    role: '编辑',
    status: '离线',
  },
  {
    id: '3',
    name: '王五',
    email: '<EMAIL>',
    role: '用户',
    status: '活跃',
  },
];

// 表格列定义
const columns: ColumnDef<User>[] = [
  {
    accessorKey: 'name',
    header: '姓名',
  },
  {
    accessorKey: 'email',
    header: '邮箱',
  },
  {
    accessorKey: 'role',
    header: '角色',
  },
  {
    accessorKey: 'status',
    // enableSorting: false,
    header: '状态',
    cell: ({ row }) => {
      const status = row.getValue('status') as string;
      return (
        <div className="flex items-center">
          <div
            className={`w-2 h-2 rounded-full mr-2 ${
              status === '活跃'
                ? 'bg-green-500'
                : 'bg-gray-300 dark:bg-gray-600'
            }`}
          />
          {status}
        </div>
      );
    },
  },
  {
    id: 'actions',
    // accessorKey: 'act',
    header: '操作',
    cell: () => (
      <div className="flex items-center gap-2">
        <Button size="xs" variant="outline">
          查看
        </Button>
        <Button size="xs" variant="outline">
          编辑
        </Button>
      </div>
    ),
  },
];

export default function UIStyleExamplePage() {
  return (
    <MainLayout>
      <div className="container py-6 px-6">
        <h1 className="text-2xl font-semibold mb-6">UI 风格示例</h1>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {/* 按钮示例 */}
          <Card>
            <CardHeader>
              <CardTitle>按钮尺寸</CardTitle>
              <CardDescription>展示不同尺寸的按钮组件</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap items-end gap-3">
                <Button size="xs">超小按钮</Button>
                <Button size="sm">小按钮</Button>
                <Button>默认按钮</Button>
                <Button size="lg">大按钮</Button>
              </div>
              <div className="flex flex-wrap items-end gap-3">
                <Button size="xs" variant="outline">
                  超小按钮
                </Button>
                <Button size="sm" variant="outline">
                  小按钮
                </Button>
                <Button variant="outline">默认按钮</Button>
                <Button size="lg" variant="outline">
                  大按钮
                </Button>
              </div>
              <div className="flex flex-wrap items-end gap-3">
                <Button size="icon" variant="outline">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M5 12h14" />
                    <path d="M12 5v14" />
                  </svg>
                </Button>
                <Button size="icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M5 12h14" />
                    <path d="M12 5v14" />
                  </svg>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 表单示例 */}
          <Card>
            <CardHeader>
              <CardTitle>水平表单布局</CardTitle>
              <CardDescription>紧凑型表单布局示例</CardDescription>
            </CardHeader>
            <CardContent>
              <FormGroup>
                <FormRow label="用户名" required>
                  <Input placeholder="请输入用户名" className="max-w-xs" />
                </FormRow>
                <FormRow label="邮箱地址" required>
                  <Input placeholder="请输入邮箱地址" className="max-w-xs" />
                </FormRow>
                <FormRow label="角色">
                  <Select>
                    <SelectTrigger className="w-52">
                      <SelectValue placeholder="选择角色" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="admin">管理员</SelectItem>
                      <SelectItem value="editor">编辑</SelectItem>
                      <SelectItem value="user">普通用户</SelectItem>
                    </SelectContent>
                  </Select>
                </FormRow>
                <FormRow label="状态">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-1.5">
                      <input
                        type="radio"
                        id="active"
                        name="status"
                        className="size-4"
                      />
                      <Label htmlFor="active" className="cursor-pointer">
                        活跃
                      </Label>
                    </div>
                    <div className="flex items-center gap-1.5">
                      <input
                        type="radio"
                        id="inactive"
                        name="status"
                        className="size-4"
                      />
                      <Label htmlFor="inactive" className="cursor-pointer">
                        禁用
                      </Label>
                    </div>
                  </div>
                </FormRow>
                <FormActions>
                  <Button variant="outline">取消</Button>
                  <Button>保存</Button>
                </FormActions>
              </FormGroup>
            </CardContent>
          </Card>
        </div>

        {/* 表格示例 */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>紧凑型表格</CardTitle>
            <CardDescription>适合中国用户习惯的表格样式</CardDescription>
          </CardHeader>
          <CardContent>
            <CompactDataTable columns={columns} data={users} />
          </CardContent>
        </Card>

        {/* 卡片和间距示例 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          {[1, 2, 3].map(i => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">紧凑型卡片 {i}</CardTitle>
              </CardHeader>
              <CardContent className="text-sm">
                <p>
                  这是一个使用了更紧凑间距的卡片示例。适合中国用户习惯的信息密度和布局风格。
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </MainLayout>
  );
}
