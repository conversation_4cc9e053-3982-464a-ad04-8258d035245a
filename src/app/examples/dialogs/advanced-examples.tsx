'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { FormDialog } from '@/components/ui/form-dialog';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Upload, BarChart3, FileText, Layers } from 'lucide-react';
import { toast } from 'sonner';

// 模拟图表数据
const chartData = [
  { name: '1月', value: 400 },
  { name: '2月', value: 300 },
  { name: '3月', value: 600 },
  { name: '4月', value: 800 },
];

export function AdvancedExamples() {
  const [chartDialog, setChartDialog] = useState(false);
  const [stepDialog, setStepDialog] = useState(false);
  const [uploadDialog, setUploadDialog] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // 步骤表单数据
  const [stepData, setStepData] = useState({
    basic: { name: '', email: '' },
    details: { phone: '', address: '' },
    preferences: { theme: '', notifications: '' }
  });

  const handleChartDialog = () => {
    setChartDialog(false);
    toast.success('数据分析完成');
  };

  const handleStepNext = () => {
    if (currentStep < 2) {
      setCurrentStep(currentStep + 1);
    } else {
      setStepDialog(false);
      setCurrentStep(0);
      toast.success('设置完成');
    }
  };

  const handleStepPrev = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleUpload = async () => {
    setIsLoading(true);
    // 模拟文件上传进度
    for (let i = 0; i <= 100; i += 10) {
      setUploadProgress(i);
      await new Promise(resolve => setTimeout(resolve, 200));
    }
    setIsLoading(false);
    setUploadDialog(false);
    setUploadProgress(0);
    toast.success('文件上传成功');
  };

  const steps = [
    { title: '基本信息', description: '填写基本资料' },
    { title: '详细信息', description: '补充详细信息' },
    { title: '偏好设置', description: '个性化设置' }
  ];

  return (
    <div className="space-y-6">
      {/* 图表展示弹框 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            图表展示弹框
          </CardTitle>
          <CardDescription>
            在弹框中嵌入复杂的图表和数据可视化组件
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button onClick={() => setChartDialog(true)}>
              查看数据分析
            </Button>
            <Badge variant="secondary">数据可视化</Badge>
          </div>
          
          <div className="bg-muted/50 p-4 rounded-lg">
            <h4 className="font-medium mb-2">适用场景</h4>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• 数据报表查看</li>
              <li>• 图表配置和预览</li>
              <li>• 复杂数据的可视化展示</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* 多步骤表单弹框 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Layers className="h-5 w-5" />
            多步骤表单弹框
          </CardTitle>
          <CardDescription>
            将复杂表单分解为多个步骤，提升用户体验
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button onClick={() => setStepDialog(true)}>
              用户设置向导
            </Button>
            <Badge variant="secondary">分步引导</Badge>
          </div>
          
          <div className="bg-muted/50 p-4 rounded-lg">
            <h4 className="font-medium mb-2">设计优势</h4>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• 降低用户认知负担</li>
              <li>• 清晰的进度指示</li>
              <li>• 支持前进后退导航</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* 文件上传弹框 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            文件上传弹框
          </CardTitle>
          <CardDescription>
            带进度条的文件上传界面，支持实时状态反馈
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button onClick={() => setUploadDialog(true)}>
              上传文件
            </Button>
            <Badge variant="secondary">进度反馈</Badge>
          </div>
          
          <div className="bg-muted/50 p-4 rounded-lg">
            <h4 className="font-medium mb-2">交互特点</h4>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• 实时进度显示</li>
              <li>• 上传状态反馈</li>
              <li>• 错误处理和重试</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* 弹框实例 */}
      
      {/* 图表弹框 */}
      <FormDialog
        open={chartDialog}
        onOpenChange={setChartDialog}
        title="销售数据分析"
        description="最近4个月的销售趋势图表"
        size="lg"
        onConfirm={handleChartDialog}
        confirmText="导出报告"
      >
        <div className="space-y-4">
          <div className="h-64 w-full">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={chartData}>
                <XAxis dataKey="name" />
                <YAxis />
                <Bar dataKey="value" fill="hsl(var(--primary))" />
              </BarChart>
            </ResponsiveContainer>
          </div>
          
          <div className="grid grid-cols-4 gap-4 text-center">
            <div className="p-3 bg-muted rounded-lg">
              <div className="text-2xl font-bold text-primary">2,100</div>
              <div className="text-sm text-muted-foreground">总销售额</div>
            </div>
            <div className="p-3 bg-muted rounded-lg">
              <div className="text-2xl font-bold text-success">+12%</div>
              <div className="text-sm text-muted-foreground">环比增长</div>
            </div>
            <div className="p-3 bg-muted rounded-lg">
              <div className="text-2xl font-bold text-info">156</div>
              <div className="text-sm text-muted-foreground">订单数量</div>
            </div>
            <div className="p-3 bg-muted rounded-lg">
              <div className="text-2xl font-bold text-warning">4.8</div>
              <div className="text-sm text-muted-foreground">平均评分</div>
            </div>
          </div>
        </div>
      </FormDialog>

      {/* 多步骤弹框 */}
      <FormDialog
        open={stepDialog}
        onOpenChange={setStepDialog}
        title={`设置向导 - ${steps[currentStep].title}`}
        description={steps[currentStep].description}
        size="md"
        onConfirm={handleStepNext}
        confirmText={currentStep === 2 ? '完成设置' : '下一步'}
        cancelText={currentStep === 0 ? '取消' : '上一步'}
        onCancel={currentStep === 0 ? undefined : handleStepPrev}
      >
        <div className="space-y-4">
          {/* 进度指示器 */}
          <div className="flex items-center justify-between mb-6">
            {steps.map((step, index) => (
              <div key={index} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  index <= currentStep 
                    ? 'bg-primary text-primary-foreground' 
                    : 'bg-muted text-muted-foreground'
                }`}>
                  {index + 1}
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-16 h-0.5 mx-2 ${
                    index < currentStep ? 'bg-primary' : 'bg-muted'
                  }`} />
                )}
              </div>
            ))}
          </div>

          {/* 步骤内容 */}
          {currentStep === 0 && (
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">姓名 *</label>
                <Input
                  placeholder="请输入姓名"
                  value={stepData.basic.name}
                  onChange={e => setStepData({
                    ...stepData,
                    basic: { ...stepData.basic, name: e.target.value }
                  })}
                />
              </div>
              <div>
                <label className="text-sm font-medium">邮箱 *</label>
                <Input
                  type="email"
                  placeholder="请输入邮箱"
                  value={stepData.basic.email}
                  onChange={e => setStepData({
                    ...stepData,
                    basic: { ...stepData.basic, email: e.target.value }
                  })}
                />
              </div>
            </div>
          )}

          {currentStep === 1 && (
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">手机号</label>
                <Input
                  placeholder="请输入手机号"
                  value={stepData.details.phone}
                  onChange={e => setStepData({
                    ...stepData,
                    details: { ...stepData.details, phone: e.target.value }
                  })}
                />
              </div>
              <div>
                <label className="text-sm font-medium">地址</label>
                <Input
                  placeholder="请输入地址"
                  value={stepData.details.address}
                  onChange={e => setStepData({
                    ...stepData,
                    details: { ...stepData.details, address: e.target.value }
                  })}
                />
              </div>
            </div>
          )}

          {currentStep === 2 && (
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">主题偏好</label>
                <select 
                  className="w-full p-2 border rounded-md"
                  value={stepData.preferences.theme}
                  onChange={e => setStepData({
                    ...stepData,
                    preferences: { ...stepData.preferences, theme: e.target.value }
                  })}
                >
                  <option value="">请选择主题</option>
                  <option value="light">浅色主题</option>
                  <option value="dark">深色主题</option>
                  <option value="auto">跟随系统</option>
                </select>
              </div>
              <div>
                <label className="text-sm font-medium">通知设置</label>
                <select 
                  className="w-full p-2 border rounded-md"
                  value={stepData.preferences.notifications}
                  onChange={e => setStepData({
                    ...stepData,
                    preferences: { ...stepData.preferences, notifications: e.target.value }
                  })}
                >
                  <option value="">请选择通知方式</option>
                  <option value="email">邮件通知</option>
                  <option value="sms">短信通知</option>
                  <option value="push">推送通知</option>
                </select>
              </div>
            </div>
          )}
        </div>
      </FormDialog>

      {/* 文件上传弹框 */}
      <FormDialog
        open={uploadDialog}
        onOpenChange={setUploadDialog}
        title="文件上传"
        description="选择文件并上传到服务器"
        size="md"
        onConfirm={handleUpload}
        confirmText={isLoading ? '上传中...' : '开始上传'}
        isLoading={isLoading}
      >
        <div className="space-y-4">
          <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
            <Upload className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-sm text-muted-foreground mb-2">
              点击选择文件或拖拽文件到此区域
            </p>
            <p className="text-xs text-muted-foreground">
              支持 PDF, DOC, DOCX, XLS, XLSX 格式，最大 10MB
            </p>
          </div>

          {isLoading && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>上传进度</span>
                <span>{uploadProgress}%</span>
              </div>
              <Progress value={uploadProgress} className="w-full" />
            </div>
          )}

          <div className="text-xs text-muted-foreground">
            <p>• 文件将被安全加密存储</p>
            <p>• 上传完成后可在文件管理中查看</p>
          </div>
        </div>
      </FormDialog>
    </div>
  );
}
