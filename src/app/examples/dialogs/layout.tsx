import { MainLayout } from '@/components/layout/main-layout';

export default function DialogExamplesLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <MainLayout>
      <div className="container py-6 px-6">
        <div className="mb-6">
          <h1 className="text-2xl font-semibold mb-2">弹框组件展示</h1>
          <p className="text-muted-foreground">
            展示通用弹框组件的各种使用场景和自定义能力
          </p>
        </div>
        {children}
      </div>
    </MainLayout>
  );
}
