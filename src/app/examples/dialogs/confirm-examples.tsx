'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ConfirmDialog, createDeleteConfirmProps, StepConfirmDialog } from '@/components/ui/confirm-dialog';
import { Badge } from '@/components/ui/badge';
import { Trash2, AlertTriangle, CheckCircle, Info } from 'lucide-react';
import { toast } from 'sonner';
import { EnhancedConfirmDialog } from '@/components/ui/enhanced-dialogs';

export function ConfirmExamples() {
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [warningDialog, setWarningDialog] = useState(false);
  const [successDialog, setSuccessDialog] = useState(false);
  const [customDialog, setCustomDialog] = useState(false);
  const [stepDialog, setStepDialog] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleDelete = async () => {
    setIsLoading(true);
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsLoading(false);
    setDeleteDialog(false);
    toast.success('删除成功');
  };

  const handleWarning = () => {
    setWarningDialog(false);
    toast.warning('操作已取消');
  };

  const handleSuccess = () => {
    setSuccessDialog(false);
    toast.success('操作完成');
  };

  const handleCustom = () => {
    setCustomDialog(false);
    toast.info('自定义操作完成');
  };

  return (
    <div className="space-y-6">
      {/* 基础确认弹框 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5" />
            删除确认弹框
          </CardTitle>
          <CardDescription>
            使用 createDeleteConfirmProps 快速创建删除确认弹框
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button 
              variant="destructive" 
              onClick={() => setDeleteDialog(true)}
            >
              删除用户
            </Button>
            <Badge variant="secondary">预设配置</Badge>
          </div>
          
          <div className="bg-muted/50 p-4 rounded-lg">
            <h4 className="font-medium mb-2">代码示例</h4>
            <pre className="text-sm overflow-x-auto">
{`<ConfirmDialog
  {...createDeleteConfirmProps({
    open: deleteDialog,
    onOpenChange: setDeleteDialog,
    itemName: "张小明",
    itemType: "用户",
    onConfirm: handleDelete,
    isLoading: isLoading,
  })}
/>`}
            </pre>
          </div>
        </CardContent>
      </Card>

      {/* 自定义确认弹框 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            自定义确认弹框
          </CardTitle>
          <CardDescription>
            完全自定义的确认弹框，支持不同的样式和内容
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2 flex-wrap">
            <Button 
              variant="outline" 
              onClick={() => setWarningDialog(true)}
            >
              <AlertTriangle className="h-4 w-4 mr-2" />
              警告操作
            </Button>
            <Button 
              variant="default" 
              onClick={() => setSuccessDialog(true)}
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              确认操作
            </Button>
            <Button 
              variant="secondary" 
              onClick={() => setCustomDialog(true)}
            >
              <Info className="h-4 w-4 mr-2" />
              复杂内容
            </Button>
          </div>

          <div className="bg-muted/50 p-4 rounded-lg">
            <h4 className="font-medium mb-2">设计特点</h4>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• 支持不同的按钮样式（default、destructive）</li>
              <li>• 可自定义标题、描述和按钮文本</li>
              <li>• 内置加载状态处理</li>
              <li>• 支持复杂的描述内容（JSX）</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* 弹框实例 */}
      <ConfirmDialog
        {...createDeleteConfirmProps({
          open: deleteDialog,
          onOpenChange: setDeleteDialog,
          itemName: "张小明",
          itemType: "用户",
          extraInfo: (
            <>
              手机号：<strong>138****8888</strong>
            </>
          ),
          onConfirm: handleDelete,
          isLoading: isLoading,
        })}
      />

      <EnhancedConfirmDialog
        open={warningDialog}
        onOpenChange={setWarningDialog}
        title="警告操作"
        description={
          <>
            <AlertTriangle className="h-5 w-5 text-warning inline mr-2" />
            此操作可能会影响系统性能，确定要继续吗？
            <br />
            <span className="text-sm text-muted-foreground mt-2 block">
              建议在系统空闲时执行此操作
            </span>
          </>
        }
        variant="destructive"
        confirmText="继续执行"
        onConfirm={handleWarning}
      />

      <ConfirmDialog
        open={successDialog}
        onOpenChange={setSuccessDialog}
        title="确认提交"
        description={
          <>
            <CheckCircle className="h-5 w-5 text-success inline mr-2" />
            数据已准备完毕，确认提交到服务器？
            <br />
            <div className="mt-3 p-3 bg-success/10 border border-success/30 rounded-md">
              <p className="text-sm text-success">✓ 数据验证通过</p>
              <p className="text-sm text-success">✓ 权限检查通过</p>
            </div>
          </>
        }
        variant="default"
        confirmText="确认提交"
        onConfirm={handleSuccess}
      />

      <ConfirmDialog
        open={customDialog}
        onOpenChange={setCustomDialog}
        title="复杂内容示例"
        description={
          <div className="space-y-3">
            <p>这是一个包含复杂内容的确认弹框示例：</p>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="p-2 bg-muted rounded">
                <strong>操作类型：</strong>批量处理
              </div>
              <div className="p-2 bg-muted rounded">
                <strong>影响范围：</strong>100条记录
              </div>
            </div>
            <div className="flex gap-2">
              <Badge variant="warning">需要审核</Badge>
              <Badge variant="info">可撤销</Badge>
            </div>
          </div>
        }
        variant="default"
        confirmText="开始处理"
        onConfirm={handleCustom}
      />
    </div>
  );
}
