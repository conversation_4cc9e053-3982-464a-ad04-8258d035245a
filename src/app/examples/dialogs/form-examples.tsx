'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { FormDialog, FormField, QuickButtons } from '@/components/ui/form-dialog';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { CreditCard, User, Settings, Upload } from 'lucide-react';
import { toast } from 'sonner';

export function FormExamples() {
  // 基础表单状态
  const [basicFormOpen, setBasicFormOpen] = useState(false);
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [role, setRole] = useState('');

  // 复杂表单状态
  const [complexFormOpen, setComplexFormOpen] = useState(false);
  const [amount, setAmount] = useState('');
  const [bonus, setBonus] = useState('');
  const [method, setMethod] = useState('');
  const [notes, setNotes] = useState('');

  // 多列布局表单状态
  const [multiColumnOpen, setMultiColumnOpen] = useState(false);
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [phone, setPhone] = useState('');
  const [address, setAddress] = useState('');

  const [isLoading, setIsLoading] = useState(false);

  const handleBasicSubmit = async () => {
    setIsLoading(true);
    await new Promise(resolve => setTimeout(resolve, 1500));
    setIsLoading(false);
    setBasicFormOpen(false);
    toast.success('用户创建成功');
    // 重置表单
    setName('');
    setEmail('');
    setRole('');
  };

  const handleComplexSubmit = async () => {
    setIsLoading(true);
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsLoading(false);
    setComplexFormOpen(false);
    toast.success('充值成功');
    // 重置表单
    setAmount('');
    setBonus('');
    setMethod('');
    setNotes('');
  };

  const handleMultiColumnSubmit = async () => {
    setIsLoading(true);
    await new Promise(resolve => setTimeout(resolve, 1500));
    setIsLoading(false);
    setMultiColumnOpen(false);
    toast.success('信息保存成功');
    // 重置表单
    setFirstName('');
    setLastName('');
    setPhone('');
    setAddress('');
  };

  return (
    <div className="space-y-6">
      {/* 基础表单弹框 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            基础表单弹框
          </CardTitle>
          <CardDescription>
            简单的表单布局，使用 FormField 组件统一字段样式
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button onClick={() => setBasicFormOpen(true)}>
              创建用户
            </Button>
            <Badge variant="secondary">单列布局</Badge>
          </div>
          
          <div className="bg-muted/50 p-4 rounded-lg">
            <h4 className="font-medium mb-2">特点</h4>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• 统一的 4:3 栅格布局（标签:内容）</li>
              <li>• 自动的必填标识和错误提示</li>
              <li>• 内置加载状态和禁用逻辑</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* 复杂表单弹框 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            复杂表单弹框
          </CardTitle>
          <CardDescription>
            包含快捷按钮、帮助文本和复杂交互的表单
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button onClick={() => setComplexFormOpen(true)}>
              会员充值
            </Button>
            <Badge variant="secondary">增强功能</Badge>
          </div>
          
          <div className="bg-muted/50 p-4 rounded-lg">
            <h4 className="font-medium mb-2">增强功能</h4>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• QuickButtons 快捷操作按钮</li>
              <li>• 动态帮助文本和实时计算</li>
              <li>• 复杂的表单验证逻辑</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* 多列布局表单 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            自定义布局表单
          </CardTitle>
          <CardDescription>
            突破默认布局限制，实现更灵活的表单设计
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button onClick={() => setMultiColumnOpen(true)}>
              编辑资料
            </Button>
            <Badge variant="secondary">自定义布局</Badge>
          </div>
          
          <div className="bg-muted/50 p-4 rounded-lg">
            <h4 className="font-medium mb-2">布局灵活性</h4>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• 支持双列、多列布局</li>
              <li>• 可混合使用 FormField 和自定义布局</li>
              <li>• 响应式设计适配</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* 表单弹框实例 */}
      <FormDialog
        open={basicFormOpen}
        onOpenChange={setBasicFormOpen}
        title="创建新用户"
        description="填写用户基本信息"
        size="md"
        onConfirm={handleBasicSubmit}
        confirmText="创建用户"
        isLoading={isLoading}
        disabled={!name || !email || !role}
      >
        <div className="space-y-4">
          <FormField label="用户名" required>
            <Input
              placeholder="请输入用户名"
              value={name}
              onChange={e => setName(e.target.value)}
            />
          </FormField>
          
          <FormField label="邮箱" required>
            <Input
              type="email"
              placeholder="请输入邮箱地址"
              value={email}
              onChange={e => setEmail(e.target.value)}
            />
          </FormField>
          
          <FormField label="角色" required>
            <Select value={role} onValueChange={setRole}>
              <SelectTrigger>
                <SelectValue placeholder="选择用户角色" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="admin">管理员</SelectItem>
                <SelectItem value="agent">代理商</SelectItem>
                <SelectItem value="sale">销售</SelectItem>
              </SelectContent>
            </Select>
          </FormField>
        </div>
      </FormDialog>

      <FormDialog
        open={complexFormOpen}
        onOpenChange={setComplexFormOpen}
        title="会员卡充值"
        description="请填写充值信息，系统将自动计算优惠"
        size="md"
        variant="default"
        onConfirm={handleComplexSubmit}
        confirmText="确认充值"
        isLoading={isLoading}
        disabled={!amount}
      >
        <div className="space-y-4">
          <FormField label="充值金额" required>
            <Input
              type="number"
              placeholder="请输入充值金额"
              value={amount}
              onChange={e => setAmount(e.target.value)}
            />
          </FormField>
          
          <FormField 
            label="赠送金额"
            help={amount && Number(amount) >= 1000 ? "充值满1000元可享受10%赠送" : "充值满1000元可享受赠送优惠"}
          >
            <Input
              type="number"
              placeholder="系统自动计算或手动输入"
              value={bonus}
              onChange={e => setBonus(e.target.value)}
            />
          </FormField>
          
          <FormField label="支付方式" required>
            <Select value={method} onValueChange={setMethod}>
              <SelectTrigger>
                <SelectValue placeholder="选择支付方式" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="wechat">微信支付</SelectItem>
                <SelectItem value="alipay">支付宝</SelectItem>
                <SelectItem value="bank">银行转账</SelectItem>
              </SelectContent>
            </Select>
          </FormField>
          
          <FormField label="备注">
            <div className="space-y-2">
              <Input
                placeholder="请输入备注信息（可选）"
                value={notes}
                onChange={e => setNotes(e.target.value)}
              />
              <QuickButtons
                buttons={[
                  { label: '生日充值', onClick: () => setNotes('生日充值优惠') },
                  { label: '推荐奖励', onClick: () => setNotes('推荐新用户奖励') },
                  { label: '活动充值', onClick: () => setNotes('活动期间充值') },
                ]}
              />
            </div>
          </FormField>
        </div>
      </FormDialog>

      <FormDialog
        open={multiColumnOpen}
        onOpenChange={setMultiColumnOpen}
        title="编辑个人资料"
        size="lg"
        onConfirm={handleMultiColumnSubmit}
        confirmText="保存修改"
        isLoading={isLoading}
        disabled={!firstName || !lastName}
      >
        <div className="space-y-4">
          {/* 双列布局 */}
          <div className="grid grid-cols-2 gap-4">
            <FormField label="姓" required>
              <Input
                placeholder="请输入姓"
                value={firstName}
                onChange={e => setFirstName(e.target.value)}
              />
            </FormField>
            <FormField label="名" required>
              <Input
                placeholder="请输入名"
                value={lastName}
                onChange={e => setLastName(e.target.value)}
              />
            </FormField>
          </div>
          
          {/* 单列布局 */}
          <FormField label="手机号">
            <Input
              placeholder="请输入手机号"
              value={phone}
              onChange={e => setPhone(e.target.value)}
            />
          </FormField>
          
          <FormField label="地址">
            <Input
              placeholder="请输入详细地址"
              value={address}
              onChange={e => setAddress(e.target.value)}
            />
          </FormField>
        </div>
      </FormDialog>
    </div>
  );
}
