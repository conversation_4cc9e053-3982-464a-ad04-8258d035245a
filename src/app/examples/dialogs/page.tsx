'use client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ConfirmExamples } from './confirm-examples';
import { FormExamples } from './form-examples';
import { AdvancedExamples } from './advanced-examples';

export default function DialogExamplesPage() {
  return (
    <div className="space-y-6">
      {/* 概述卡片 */}
      <Card>
        <CardHeader>
          <CardTitle>弹框组件系统</CardTitle>
          <CardDescription>
            基于 shadcn/ui 封装的通用弹框组件，提供一致的用户体验和高度的自定义能力
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 border rounded-lg">
              <h3 className="font-medium mb-2">ConfirmDialog</h3>
              <p className="text-sm text-muted-foreground">确认操作弹框</p>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <h3 className="font-medium mb-2">FormDialog</h3>
              <p className="text-sm text-muted-foreground">表单操作弹框</p>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <h3 className="font-medium mb-2">自定义扩展</h3>
              <p className="text-sm text-muted-foreground">复杂场景定制</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 示例展示 */}
      <Tabs defaultValue="comparison" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="comparison">设计对比</TabsTrigger>
          <TabsTrigger value="confirm">确认弹框</TabsTrigger>
          <TabsTrigger value="form">表单弹框</TabsTrigger>
          <TabsTrigger value="advanced">高级定制</TabsTrigger>
        </TabsList>

        <TabsContent value="comparison" className="space-y-4">
          <DesignComparison />
        </TabsContent>

        <TabsContent value="confirm" className="space-y-4">
          <ConfirmExamples />
        </TabsContent>

        <TabsContent value="form" className="space-y-4">
          <FormExamples />
        </TabsContent>

        <TabsContent value="advanced" className="space-y-4">
          <AdvancedExamples />
        </TabsContent>
      </Tabs>
    </div>
  );
}
