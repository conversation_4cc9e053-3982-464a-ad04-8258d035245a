'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  EnhancedConfirmDialogA, 
  EnhancedFormDialogB, 
  EnhancedFormDialogC 
} from '@/components/ui/enhanced-dialogs';
import { FormField } from '@/components/ui/form-dialog';
import { ConfirmDialog } from '@/components/ui/confirm-dialog';
import { FormDialog } from '@/components/ui/form-dialog';
import { Palette, Layers, Minus } from 'lucide-react';
import { toast } from 'sonner';

export function DesignComparison() {
  // 确认弹框状态
  const [currentConfirm, setCurrentConfirm] = useState(false);
  const [enhancedConfirmA, setEnhancedConfirmA] = useState(false);

  // 表单弹框状态
  const [currentForm, setCurrentForm] = useState(false);
  const [enhancedFormB, setEnhancedFormB] = useState(false);
  const [enhancedFormC, setEnhancedFormC] = useState(false);

  // 表单数据
  const [formData, setFormData] = useState({
    amount: '',
    bonus: '',
    method: '',
    notes: '',
  });

  const [isLoading, setIsLoading] = useState(false);

  const handleConfirm = async () => {
    setIsLoading(true);
    await new Promise(resolve => setTimeout(resolve, 1500));
    setIsLoading(false);
    setCurrentConfirm(false);
    setEnhancedConfirmA(false);
    toast.success('删除成功');
  };

  const handleFormSubmit = async () => {
    setIsLoading(true);
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsLoading(false);
    setCurrentForm(false);
    setEnhancedFormB(false);
    setEnhancedFormC(false);
    toast.success('充值成功');
    // 重置表单
    setFormData({ amount: '', bonus: '', method: '', notes: '' });
  };

  return (
    <div className="space-y-8">
      {/* 设计方案对比说明 */}
      <Card>
        <CardHeader>
          <CardTitle>设计方案对比</CardTitle>
          <CardDescription>
            以下展示了三种不同的弹框设计方案，每种方案都有其独特的视觉特点和适用场景
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 border rounded-lg">
              <Palette className="h-8 w-8 mx-auto mb-2 text-primary" />
              <h3 className="font-medium mb-1">方案A：渐变头部</h3>
              <p className="text-xs text-muted-foreground">情感化设计，视觉冲击力强</p>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <Layers className="h-8 w-8 mx-auto mb-2 text-success" />
              <h3 className="font-medium mb-1">方案B：卡片分组</h3>
              <p className="text-xs text-muted-foreground">现代化设计，层次清晰</p>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <Minus className="h-8 w-8 mx-auto mb-2 text-info" />
              <h3 className="font-medium mb-1">方案C：极简分割</h3>
              <p className="text-xs text-muted-foreground">简洁设计，保持一致性</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 确认弹框对比 */}
      <Card>
        <CardHeader>
          <CardTitle>确认弹框设计对比</CardTitle>
          <CardDescription>
            对比当前设计与方案A的视觉效果差异
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <h4 className="font-medium">当前设计</h4>
                <Badge variant="secondary">原版</Badge>
              </div>
              <Button 
                variant="destructive" 
                onClick={() => setCurrentConfirm(true)}
                className="w-full"
              >
                删除用户（当前设计）
              </Button>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• 标准的 AlertDialog 布局</li>
                <li>• 简洁但缺乏视觉层次</li>
                <li>• 危险操作警示不足</li>
              </ul>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <h4 className="font-medium">方案A：渐变头部</h4>
                <Badge variant="default">增强版</Badge>
              </div>
              <Button 
                variant="destructive" 
                onClick={() => setEnhancedConfirmA(true)}
                className="w-full"
              >
                删除用户（方案A）
              </Button>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• 渐变背景增强视觉层次</li>
                <li>• 图标强化操作类型识别</li>
                <li>• 更强的情感化设计</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 表单弹框对比 */}
      <Card>
        <CardHeader>
          <CardTitle>表单弹框设计对比</CardTitle>
          <CardDescription>
            对比三种不同的表单弹框设计方案
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <h4 className="font-medium">当前设计</h4>
                <Badge variant="secondary">原版</Badge>
              </div>
              <Button 
                onClick={() => setCurrentForm(true)}
                className="w-full"
              >
                充值（当前设计）
              </Button>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• 标准布局</li>
                <li>• 功能完整</li>
                <li>• 视觉层次一般</li>
              </ul>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <h4 className="font-medium">方案B：卡片分组</h4>
                <Badge variant="default">现代化</Badge>
              </div>
              <Button 
                onClick={() => setEnhancedFormB(true)}
                className="w-full"
              >
                充值（方案B）
              </Button>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• 卡片式内容区域</li>
                <li>• 渐变头部设计</li>
                <li>• 现代化视觉风格</li>
              </ul>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <h4 className="font-medium">方案C：极简分割</h4>
                <Badge variant="outline">简洁</Badge>
              </div>
              <Button 
                onClick={() => setEnhancedFormC(true)}
                className="w-full"
              >
                充值（方案C）
              </Button>
              <ul className="text-xs text-muted-foreground space-y-1">
                <li>• 分割线区分区域</li>
                <li>• 保持简洁风格</li>
                <li>• 微调间距层次</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 设计建议 */}
      <Card>
        <CardHeader>
          <CardTitle>设计方案建议</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-primary/5 border border-primary/20 rounded-lg">
              <h4 className="font-medium text-primary mb-2">推荐：方案A（渐变头部）</h4>
              <p className="text-sm text-muted-foreground">
                适合需要强化用户注意力的场景，如删除确认、重要操作提醒等。
                渐变背景和图标设计能够有效传达操作的重要性和类型。
              </p>
            </div>
            
            <div className="p-4 bg-success/5 border border-success/20 rounded-lg">
              <h4 className="font-medium text-success mb-2">备选：方案B（卡片分组）</h4>
              <p className="text-sm text-muted-foreground">
                适合复杂表单和信息展示，卡片式设计能够很好地组织内容，
                提升信息的可读性和视觉层次。
              </p>
            </div>
            
            <div className="p-4 bg-info/5 border border-info/20 rounded-lg">
              <h4 className="font-medium text-info mb-2">保守：方案C（极简分割）</h4>
              <p className="text-sm text-muted-foreground">
                适合希望保持当前设计风格的场景，通过微调实现视觉层次的提升，
                改动最小但效果有限。
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 弹框实例 */}
      
      {/* 当前设计的确认弹框 */}
      <ConfirmDialog
        open={currentConfirm}
        onOpenChange={setCurrentConfirm}
        title="确认删除"
        description={
          <>
            确定要删除这个用户吗？
            <br />
            用户名：<strong>张小明</strong>
            <br />
            此操作无法撤销。
          </>
        }
        variant="destructive"
        onConfirm={handleConfirm}
        isLoading={isLoading}
      />

      {/* 方案A的增强确认弹框 */}
      <EnhancedConfirmDialogA
        open={enhancedConfirmA}
        onOpenChange={setEnhancedConfirmA}
        title="确认删除用户"
        description={
          <>
            您即将删除用户 <strong>张小明</strong>，此操作将：
            <br />
            <br />
            • 永久删除用户的所有数据
            <br />
            • 清除相关的会员卡信息
            <br />
            • 无法恢复，请谨慎操作
          </>
        }
        variant="destructive"
        confirmText="确认删除"
        onConfirm={handleConfirm}
        isLoading={isLoading}
      />

      {/* 当前设计的表单弹框 */}
      <FormDialog
        open={currentForm}
        onOpenChange={setCurrentForm}
        title="会员卡充值"
        size="md"
        onConfirm={handleFormSubmit}
        confirmText="确认充值"
        isLoading={isLoading}
        disabled={!formData.amount}
      >
        <div className="space-y-4">
          <FormField label="充值金额" required>
            <Input
              type="number"
              placeholder="请输入充值金额"
              value={formData.amount}
              onChange={e => setFormData({...formData, amount: e.target.value})}
            />
          </FormField>
          
          <FormField label="赠送金额">
            <Input
              type="number"
              placeholder="请输入赠送金额（可选）"
              value={formData.bonus}
              onChange={e => setFormData({...formData, bonus: e.target.value})}
            />
          </FormField>
          
          <FormField label="支付方式" required>
            <Select value={formData.method} onValueChange={value => setFormData({...formData, method: value})}>
              <SelectTrigger>
                <SelectValue placeholder="选择支付方式" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="wechat">微信支付</SelectItem>
                <SelectItem value="alipay">支付宝</SelectItem>
                <SelectItem value="bank">银行转账</SelectItem>
              </SelectContent>
            </Select>
          </FormField>
        </div>
      </FormDialog>

      {/* 方案B的增强表单弹框 */}
      <EnhancedFormDialogB
        open={enhancedFormB}
        onOpenChange={setEnhancedFormB}
        title="会员卡充值"
        description="请填写充值信息，系统将自动处理您的充值请求"
        size="md"
        onConfirm={handleFormSubmit}
        confirmText="确认充值"
        isLoading={isLoading}
        disabled={!formData.amount}
      >
        <div className="space-y-4">
          <FormField label="充值金额" required>
            <Input
              type="number"
              placeholder="请输入充值金额"
              value={formData.amount}
              onChange={e => setFormData({...formData, amount: e.target.value})}
            />
          </FormField>
          
          <FormField label="赠送金额">
            <Input
              type="number"
              placeholder="请输入赠送金额（可选）"
              value={formData.bonus}
              onChange={e => setFormData({...formData, bonus: e.target.value})}
            />
          </FormField>
          
          <FormField label="支付方式" required>
            <Select value={formData.method} onValueChange={value => setFormData({...formData, method: value})}>
              <SelectTrigger>
                <SelectValue placeholder="选择支付方式" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="wechat">微信支付</SelectItem>
                <SelectItem value="alipay">支付宝</SelectItem>
                <SelectItem value="bank">银行转账</SelectItem>
              </SelectContent>
            </Select>
          </FormField>
        </div>
      </EnhancedFormDialogB>

      {/* 方案C的增强表单弹框 */}
      <EnhancedFormDialogC
        open={enhancedFormC}
        onOpenChange={setEnhancedFormC}
        title="会员卡充值"
        description="请填写充值信息，系统将自动处理您的充值请求"
        size="md"
        onConfirm={handleFormSubmit}
        confirmText="确认充值"
        isLoading={isLoading}
        disabled={!formData.amount}
      >
        <div className="space-y-4">
          <FormField label="充值金额" required>
            <Input
              type="number"
              placeholder="请输入充值金额"
              value={formData.amount}
              onChange={e => setFormData({...formData, amount: e.target.value})}
            />
          </FormField>
          
          <FormField label="赠送金额">
            <Input
              type="number"
              placeholder="请输入赠送金额（可选）"
              value={formData.bonus}
              onChange={e => setFormData({...formData, bonus: e.target.value})}
            />
          </FormField>
          
          <FormField label="支付方式" required>
            <Select value={formData.method} onValueChange={value => setFormData({...formData, method: value})}>
              <SelectTrigger>
                <SelectValue placeholder="选择支付方式" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="wechat">微信支付</SelectItem>
                <SelectItem value="alipay">支付宝</SelectItem>
                <SelectItem value="bank">银行转账</SelectItem>
              </SelectContent>
            </Select>
          </FormField>
        </div>
      </EnhancedFormDialogC>
    </div>
  );
}
