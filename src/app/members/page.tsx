'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

import { MainLayout } from '@/components/layout/main-layout';
import { CompactDataTable } from '@/components/tables/common/compact-data-table';
import { createMemberColumns } from '@/components/tables/columns/member-columns';
import { DataStateWrapper } from '@/components/ui/loading-states';
import { ConfirmDialog, createDeleteConfirmProps } from '@/components/ui/confirm-dialog';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Search, Plus } from 'lucide-react';

import {
  useMemberList,
  useDeleteMember,
  useUnbindWechat,
  useUpdateMember,
} from '@/hooks/use-members';
import { useSalesmenList } from '@/hooks/use-users';
import { ROUTES } from '@/constants/routes';
import { MESSAGES } from '@/constants/messages';
import { MemberRead, MemberType, MemberStatus } from '@/types/api';
import { UserRead } from '@/types/api/user';
import { MemberDetailDrawer } from '@/components/tables/member-detail/member-detail-drawer';

// 会员类型选项
const memberTypeOptions = [
  { value: 'all', label: '全部类型' },
  { value: MemberType.TRIAL, label: '试听' },
  { value: MemberType.FORMAL, label: '正式' },
  { value: MemberType.VIP, label: 'VIP' },
];

// 会员状态选项
const memberStatusOptions = [
  { value: 'all', label: '全部状态' },
  { value: MemberStatus.ACTIVE, label: '活跃' },
  { value: MemberStatus.SILENT, label: '沉默' },
  { value: MemberStatus.FROZEN, label: '冻结' },
  { value: MemberStatus.CANCELLED, label: '已取消' },
];

// 简化的销售人员类型
interface SalesmanInfo {
  id: number;
  username: string;
}

export default function MembersPage() {
  const router = useRouter();

  // 筛选状态
  const [filters, setFilters] = useState({
    member_type: 'all' as MemberType | 'all',
    member_status: 'all' as MemberStatus | 'all',
    agent_id: 'all' as number | 'all',
    search: '',
  });

  // 对话框状态
  const [deleteTarget, setDeleteTarget] = useState<MemberRead | null>(null);
  const [unbindTarget, setUnbindTarget] = useState<MemberRead | null>(null);
  const [salesTarget, setSalesTarget] = useState<MemberRead | null>(null);
  const [selectedSalesId, setSelectedSalesId] = useState<string>('');

  // 详情抽屉状态
  const [detailDrawerOpen, setDetailDrawerOpen] = useState(false);
  const [selectedMemberId, setSelectedMemberId] = useState<string | undefined>(
    undefined
  );

  // 获取销售人员列表
  const { data: salesmenResponse } = useSalesmenList();

  // 转换销售人员数据为所需格式
  const salesmen: SalesmanInfo[] = Array.isArray(salesmenResponse?.data)
    ? salesmenResponse.data.map((user: UserRead) => ({
        id: Number(user.id),
        username: user.username,
      }))
    : salesmenResponse?.data?.data?.map((user: UserRead) => ({
        id: Number(user.id),
        username: user.username,
      })) || [];

  // 获取会员列表
  const { data: membersResponse, isLoading } = useMemberList({
    member_type:
      filters.member_type === 'all'
        ? null
        : (filters.member_type as MemberType),
    member_status:
      filters.member_status === 'all'
        ? null
        : (filters.member_status as MemberStatus),
    agent_id: filters.agent_id === 'all' ? null : (filters.agent_id as number),
    search_keyword: filters.search || null,
  });

  const members = Array.isArray(membersResponse?.data)
    ? membersResponse.data
    : membersResponse?.data?.data || [];

  // 删除和解绑操作
  const deleteUserMutation = useDeleteMember();
  const unbindWechatMutation = useUnbindWechat();
  const updateMemberMutation = useUpdateMember();

  const handleAdd = () => {
    router.push(ROUTES.MEMBERS_CREATE);
  };

  const handleView = (member: MemberRead) => {
    setSelectedMemberId(member.id.toString());
    setDetailDrawerOpen(true);
  };

  const handleDelete = (member: MemberRead) => {
    setDeleteTarget(member);
  };

  const handleUnbindWechat = (member: MemberRead) => {
    setUnbindTarget(member);
  };

  const handleChangeSales = (member: MemberRead) => {
    setSalesTarget(member);
    setSelectedSalesId(member.agent_id ? member.agent_id.toString() : 'null');
  };

  const confirmDelete = async () => {
    if (!deleteTarget) return;

    try {
      await deleteUserMutation.mutateAsync(deleteTarget.id.toString());
      toast.success(MESSAGES.MEMBERS.DELETE_SUCCESS);
      setDeleteTarget(null);
    } catch (error) {
      console.error('Delete member error:', error);
    }
  };

  const confirmUnbindWechat = async () => {
    if (!unbindTarget) return;

    try {
      await unbindWechatMutation.mutateAsync(unbindTarget.id.toString());
      setUnbindTarget(null);
    } catch (error) {
      console.error('Unbind wechat error:', error);
    }
  };

  const confirmChangeSales = async () => {
    if (!salesTarget) return;

    try {
      await updateMemberMutation.mutateAsync({
        id: salesTarget.id.toString(),
        data: {
          agent_id:
            selectedSalesId && selectedSalesId !== 'null'
              ? parseInt(selectedSalesId)
              : null,
        },
      });
      toast.success('销售顾问更改成功');
      setSalesTarget(null);
    } catch (error) {
      console.error('Change sales error:', error);
      toast.error('更改销售顾问失败');
    }
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]:
        key === 'agent_id'
          ? value === 'all'
            ? 'all'
            : parseInt(value)
          : value,
    }));
  };

  // 根据会员ID获取销售名称
  const getSalesName = (agentId: number | null | undefined) => {
    if (!agentId) return '未分配';
    const salesman = salesmen.find(s => s.id === agentId);
    return salesman ? salesman.username : '未知销售';
  };

  const columns = createMemberColumns({
    onView: handleView,
    onDelete: handleDelete,
    onUnbindWechat: handleUnbindWechat,
    onChangeSales: handleChangeSales,
    salesmen: salesmen,
  });

  return (
    <MainLayout>
      <div className="space-y-4">
        {/* 紧凑型筛选区域 */}
        <div className="bg-card border rounded-lg p-3">
          <div className="flex flex-wrap items-center gap-3">
            <div className="flex items-center gap-1">
              <span className="text-sm font-medium whitespace-nowrap">
                会员类型:
              </span>
              <Select
                value={filters.member_type}
                onValueChange={value =>
                  handleFilterChange('member_type', value)
                }
              >
                <SelectTrigger className="h-8 w-22 text-xs">
                  <SelectValue placeholder="选择会员类型" />
                </SelectTrigger>
                <SelectContent>
                  {memberTypeOptions.map(option => (
                    <SelectItem
                      key={option.value}
                      value={option.value}
                      className="text-xs"
                    >
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-1">
              <span className="text-sm font-medium whitespace-nowrap">
                会员状态:
              </span>
              <Select
                value={filters.member_status}
                onValueChange={value =>
                  handleFilterChange('member_status', value)
                }
              >
                <SelectTrigger className="h-8 w-22 text-xs">
                  <SelectValue placeholder="选择会员状态" />
                </SelectTrigger>
                <SelectContent>
                  {memberStatusOptions.map(option => (
                    <SelectItem
                      key={option.value}
                      value={option.value}
                      className="text-xs"
                    >
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-1">
              <span className="text-sm font-medium whitespace-nowrap">
                销售/代理:
              </span>
              <Select
                value={filters.agent_id.toString()}
                onValueChange={value => handleFilterChange('agent_id', value)}
              >
                <SelectTrigger className="h-8 w-22 text-xs">
                  <SelectValue placeholder="选择销售/代理" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all" className="text-xs">
                    全部
                  </SelectItem>
                  {salesmen.map(salesman => (
                    <SelectItem
                      key={salesman.id}
                      value={salesman.id.toString()}
                      className="text-xs"
                    >
                      {salesman.username}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="relative w-40">
              <Search className="absolute left-2.5 top-1/2 h-3.5 w-3.5 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="输入姓名或手机号..."
                value={filters.search}
                onChange={e => handleFilterChange('search', e.target.value)}
                className="pl-8 h-8 text-xs"
              />
            </div>

            <Button
              size="sm"
              className="h-8 ml-auto whitespace-nowrap"
              onClick={handleAdd}
            >
              <Plus className="mr-0 h-3.5 w-3.5" />
              新增会员
            </Button>
          </div>
        </div>

        {/* 会员列表 */}
        <DataStateWrapper
          isLoading={isLoading}
          isEmpty={!isLoading && members.length === 0}
          emptyTitle="还没有会员"
          emptyDescription="系统中还没有任何会员，点击下方按钮添加第一个会员"
          emptyActionLabel="添加会员"
          onEmptyAction={handleAdd}
        >
          <CompactDataTable
            columns={columns}
            data={members}
            isLoading={false}
          />
        </DataStateWrapper>

        {/* 会员详情抽屉 */}
        <MemberDetailDrawer
          memberId={selectedMemberId}
          open={detailDrawerOpen}
          onClose={() => setDetailDrawerOpen(false)}
        />

        {/* 删除确认对话框 */}
        <ConfirmDialog
          {...createDeleteConfirmProps({
            open: !!deleteTarget,
            onOpenChange: () => setDeleteTarget(null),
            itemName: deleteTarget?.name || '',
            itemType: '会员',
            extraInfo: (
              <>
                手机号：<strong>{deleteTarget?.phone}</strong>
              </>
            ),
            onConfirm: confirmDelete,
            isLoading: deleteUserMutation.isPending,
          })}
        />

        {/* 解绑微信确认对话框 */}
        <ConfirmDialog
          open={!!unbindTarget}
          onOpenChange={() => setUnbindTarget(null)}
          title="确认解绑微信"
          description={
            <>
              确定要解绑会员 <strong>{unbindTarget?.name}</strong> 的微信账号吗？
              <br />
              解绑后该会员将无法通过微信登录。
            </>
          }
          variant="destructive"
          confirmText={unbindWechatMutation.isPending ? '解绑中...' : '确认解绑'}
          onConfirm={confirmUnbindWechat}
          isLoading={unbindWechatMutation.isPending}
        />

        {/* 更改销售对话框 */}
        <Dialog open={!!salesTarget} onOpenChange={() => setSalesTarget(null)}>
          <DialogContent className="sm:max-w-md space-y-0">
            <DialogHeader>
              <DialogTitle>更改销售顾问</DialogTitle>
              <DialogDescription>
                为会员重新分配销售顾问，请仔细阅读以下重要提示。
              </DialogDescription>
            </DialogHeader>

            {/* 警示信息区域 - 紧凑型Alert */}
            <Alert variant="warning" className="my-3">
              {/* <AlertTriangle className="h-4 w-4" /> */}
              <AlertDescription>
                <div className="font-medium text-warning mb-2">重要影响说明：</div>
                <div className="space-y-1 text-sm">
                  <div className="flex items-start gap-2">
                    <span className="text-warning font-medium">1.</span>
                    <span>所有导出会员的记录，涉及到该会员的「销售顾问」都会改成新更改后的销售</span>
                  </div>
                  <div className="flex items-start gap-2">
                    <span className="text-warning font-medium">2.</span>
                    <span>对于结算销售业绩，请机构方及时做好过渡工作</span>
                  </div>
                </div>
              </AlertDescription>
            </Alert>

            {/* 紧凑型会员信息和选择区域 */}
            <div className="space-y-2">
              <div className="grid grid-cols-4 items-center gap-4">
                <span className="text-right text-sm font-medium">会员姓名:</span>
                <div className="col-span-3 font-medium">{salesTarget?.name}</div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <span className="text-right text-sm font-medium">当前销售:</span>
                <div className="col-span-3">
                  {salesTarget && getSalesName(salesTarget.agent_id)}
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <span className="text-right text-sm font-medium">选择新销售:</span>
                <div className="col-span-3">
                  <Select
                    value={selectedSalesId}
                    onValueChange={setSelectedSalesId}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择销售顾问" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="null">无销售顾问</SelectItem>
                      {salesmen.map(salesman => (
                        <SelectItem
                          key={salesman.id}
                          value={salesman.id.toString()}
                        >
                          {salesman.username}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setSalesTarget(null)}>
                取消
              </Button>
              <Button
                onClick={confirmChangeSales}
                disabled={updateMemberMutation.isPending}
              >
                {updateMemberMutation.isPending ? '更改中...' : '确定'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  );
}
