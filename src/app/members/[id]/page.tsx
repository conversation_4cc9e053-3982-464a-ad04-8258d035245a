'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { Pencil } from 'lucide-react';

import { useMemberDetail } from '@/hooks/use-members';
import {
  useMemberCardsByMember,
  useCardOperations,
} from '@/hooks/use-member-cards';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';

import { MemberInfoCard } from '@/components/tables/member-detail/member-info-card';
import { MemberCardList } from '@/components/tables/member-detail/member-card-list';
import { CompactDataTable } from '@/components/tables/common/compact-data-table';

import { createOperationColumns } from '@/components/tables/columns/member-detail-columns';
import { Skeleton } from '@/components/ui/skeleton';

interface MemberDetailPageProps {
  params: {
    id: string;
  };
}

export default function MemberDetailPage({ params }: MemberDetailPageProps) {
  const router = useRouter();
  const { id } = params;
  const [activeTab, setActiveTab] = useState<string>('all');

  // 获取会员信息
  const {
    data: member,
    isLoading: isLoadingMember,
    error: memberError,
  } = useMemberDetail(id);

  // 添加日志查看member数据
  useEffect(() => {
    console.log('MemberDetailPage member data:', member);
    console.log('MemberDetailPage isLoadingMember:', isLoadingMember);
  }, [member, isLoadingMember]);

  // 获取会员卡列表
  const {
    data: memberCards,
    isLoading: isLoadingCards,
    error: cardsError,
  } = useMemberCardsByMember(id);

  // 添加日志查看memberCards数据
  useEffect(() => {
    console.log('MemberDetailPage memberCards data:', memberCards);
    console.log('MemberDetailPage isLoadingCards:', isLoadingCards);
  }, [memberCards, isLoadingCards]);

  // 根据选项卡设置操作类型过滤
  const getOperationTypes = (tab: string): string[] | undefined => {
    switch (tab) {
      case 'recharge':
        return ['recharge'];
      case 'consume':
        return [
          'direct_booking',
          'fixed_schedule_booking',
          'admin_booking',
          'manual_deduction',
        ];
      case 'income':
        return [
          'initial_binding',
          'recharge',
          'refund',
          'member_cancel_booking',
          'admin_cancel_booking',
        ];
      case 'booking':
        return ['direct_booking', 'fixed_schedule_booking', 'admin_booking'];
      case 'cancel':
        return ['member_cancel_booking', 'admin_cancel_booking'];
      default:
        return undefined;
    }
  };

  // 获取操作记录
  const {
    data: operations,
    isLoading: isLoadingOperations,
    error: operationsError,
  } = useCardOperations({
    member_id: id,
    operation_types: getOperationTypes(activeTab),
  });

  // 处理错误
  useEffect(() => {
    if (memberError) {
      toast.error('获取会员信息失败');
    }
    if (cardsError) {
      toast.error('获取会员卡信息失败');
    }
    if (operationsError) {
      toast.error('获取操作记录失败');
    }
  }, [memberError, cardsError, operationsError]);

  // 处理编辑
  const handleEdit = () => {
    router.push(`/members/${id}/edit`);
  };

  // 处理Tab切换
  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  return (
    <div className="p-4 md:p-6 space-y-6">
      {/* 1. 基本信息卡片 */}
      <div className="flex justify-between items-start mb-6">
        <h1 className="text-2xl font-bold">会员详情</h1>
        <Button
          onClick={handleEdit}
          size="sm"
          variant="outline"
          className="flex items-center gap-1"
        >
          <Pencil className="h-4 w-4" />
          编辑
        </Button>
      </div>

      <MemberInfoCard member={member} isLoading={isLoadingMember} />

      {/* 2. 会员卡区域 */}
      <Card className="border shadow-sm">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">会员卡</CardTitle>
        </CardHeader>
        <CardContent>
          {/* 只有在会员数据加载完成后才渲染会员卡列表 */}
          {!isLoadingMember && (
            <MemberCardList
              cards={memberCards || []}
              isLoading={isLoadingCards}
              member={member}
            />
          )}
          {isLoadingMember && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Array(3)
                .fill(0)
                .map((_, i) => (
                  <Skeleton key={i} className="h-40 w-full" />
                ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 3. 操作记录 */}
      <Card className="border shadow-sm">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">操作记录</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs
            defaultValue="all"
            value={activeTab}
            onValueChange={handleTabChange}
            className="w-full"
          >
            <TabsList className="mb-4 w-full sm:w-auto">
              <TabsTrigger value="all">全部</TabsTrigger>
              <TabsTrigger value="recharge">充值</TabsTrigger>
              <TabsTrigger value="consume">消费</TabsTrigger>
              <TabsTrigger value="income">收入</TabsTrigger>
              <TabsTrigger value="booking">约课</TabsTrigger>
              <TabsTrigger value="cancel">取消预约</TabsTrigger>
            </TabsList>

            <TabsContent value={activeTab}>
              <CompactDataTable
                columns={createOperationColumns(memberCards || [])}
                data={operations || []}
                isLoading={isLoadingOperations}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
