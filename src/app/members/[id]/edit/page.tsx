'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { Trash2 } from 'lucide-react';

import { MainLayout } from '@/components/layout/main-layout';
import { MemberForm } from '@/components/forms/member-form';
import {
  useMemberDetail,
  useUpdateMember,
  useDeleteMember,
} from '@/hooks/use-members';
import { MESSAGES } from '@/constants/messages';
import { Button } from '@/components/ui/button';
import { ConfirmDialog, createDeleteConfirmProps } from '@/components/ui/confirm-dialog';
import type { MemberUpdate } from '@/types/api';

interface MemberEditPageProps {
  params: {
    id: string;
  };
}

export default function MemberEditPage({ params }: MemberEditPageProps) {
  const { id } = params;
  const router = useRouter();

  // 删除确认对话框状态
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // 获取会员详情
  const {
    data: member,
    isLoading: isLoadingMember,
    error,
  } = useMemberDetail(id);

  // 更新和删除会员
  const updateMemberMutation = useUpdateMember();
  const deleteMemberMutation = useDeleteMember();

  // 处理错误
  useEffect(() => {
    if (error) {
      toast.error('获取会员信息失败');
      router.push('/members');
    }
  }, [error, router]);

  const handleUpdateMember = async (data: MemberUpdate) => {
    await updateMemberMutation.mutateAsync({ id, data });
    router.push(`/members/${id}`);
    // 成功和错误消息都由 useUpdateMember Hook 统一处理，避免重复提示
  };

  const handleDeleteMember = async () => {
    await deleteMemberMutation.mutateAsync(id);
    setShowDeleteDialog(false);
    router.push('/members');
    // 成功和错误消息都由 useDeleteMember Hook 统一处理，避免重复提示
  };

  return (
    <MainLayout>
      <div className="space-y-6 p-4 md:p-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold tracking-tight">编辑会员</h1>
          <Button
            variant="destructive"
            onClick={() => setShowDeleteDialog(true)}
            disabled={isLoadingMember || !member}
            size="sm"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            删除会员
          </Button>
        </div>

        {isLoadingMember ? (
          <div className="flex items-center justify-center h-64">
            <p>加载中...</p>
          </div>
        ) : member ? (
          <MemberForm
            member={member}
            onSubmit={handleUpdateMember}
            isLoading={updateMemberMutation.isPending}
            mode="edit"
          />
        ) : (
          <div className="flex items-center justify-center h-64">
            <p>未找到会员信息</p>
          </div>
        )}
      </div>

      {/* 删除确认对话框 */}
      <ConfirmDialog
        {...createDeleteConfirmProps({
          open: showDeleteDialog,
          onOpenChange: setShowDeleteDialog,
          itemName: member?.name || '',
          itemType: '会员',
          extraInfo: (
            <>
              手机号：<strong>{member?.phone}</strong>
            </>
          ),
          onConfirm: handleDeleteMember,
          isLoading: deleteMemberMutation.isPending,
        })}
      />
    </MainLayout>
  );
}
