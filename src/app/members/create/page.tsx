'use client';

import { useRouter } from 'next/navigation';

import { MainLayout } from '@/components/layout/main-layout';
import { MemberForm } from '@/components/forms/member-form';
import { useCreateMember } from '@/hooks/use-members';
import { ROUTES } from '@/constants/routes';
import type { MemberCreate, MemberUpdate } from '@/types/api';

export default function CreateMemberPage() {
  const router = useRouter();
  const createMemberMutation = useCreateMember();

  const handleCreateMember = async (data: MemberCreate | MemberUpdate) => {
    // 由于是创建会员，我们可以安全地断言为MemberCreate类型
    await createMemberMutation.mutateAsync(data as MemberCreate);
    router.push(ROUTES.MEMBERS);
    // 成功和错误消息都由 useCreateMember Hook 统一处理，避免重复提示
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold tracking-tight">新增会员</h1>
        </div>
        <MemberForm
          onSubmit={handleCreateMember}
          isLoading={createMemberMutation.isPending}
          mode="create"
        />
      </div>
    </MainLayout>
  );
}
