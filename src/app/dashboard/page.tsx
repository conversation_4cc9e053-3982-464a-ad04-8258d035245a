'use client';

import { useAuth } from '@/hooks/use-auth';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { MainLayout } from '@/components/layout/main-layout';
import { LogOut, User, TrendingUp, Users, Activity, CheckCircle } from 'lucide-react';
import { LineChart, BarChart } from '@/components/charts';
import { Badge } from '@/components/ui/badge';

// 示例数据
const chartData = [
  { name: '周一', 访问量: 400, 用户数: 240 },
  { name: '周二', 访问量: 300, 用户数: 139 },
  { name: '周三', 访问量: 200, 用户数: 980 },
  { name: '周四', 访问量: 278, 用户数: 390 },
  { name: '周五', 访问量: 189, 用户数: 480 },
  { name: '周六', 访问量: 239, 用户数: 380 },
  { name: '周日', 访问量: 349, 用户数: 430 },
];

const barData = [
  { name: '一月', 收入: 4000, 支出: 2400 },
  { name: '二月', 收入: 3000, 支出: 1398 },
  { name: '三月', 收入: 2000, 支出: 9800 },
  { name: '四月', 收入: 2780, 支出: 3908 },
  { name: '五月', 收入: 1890, 支出: 4800 },
  { name: '六月', 收入: 2390, 支出: 3800 },
];

export default function DashboardPage() {
  const { user, logout } = useAuth();

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="mb-6">
          <h2 className="text-2xl font-bold">
            欢迎回来，{user?.username || '用户'}！
          </h2>
          <p className="text-muted-foreground">
            这里是您的管理仪表盘，您可以查看系统概况和进行快速操作。
          </p>
        </div>

        {/* 统计卡片 */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总用户数</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">1,234</div>
              <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                <TrendingUp className="h-3 w-3 text-success" />
                <span>+12% 较上月</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">活跃会员</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">856</div>
              <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                <TrendingUp className="h-3 w-3 text-success" />
                <span>+8% 较上月</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">本月收入</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">¥45,231</div>
              <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                <TrendingUp className="h-3 w-3 text-success" />
                <span>+15% 较上月</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">系统状态</CardTitle>
              <CheckCircle className="h-4 w-4 text-success" />
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <Badge variant="success" className="text-xs">正常运行</Badge>
              </div>
              <div className="text-xs text-muted-foreground mt-2">
                运行时间: 99.9%
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 图表区域 */}
        <div className="grid gap-6 md:grid-cols-2">
          <LineChart
            title="访问统计"
            description="近7天访问量和用户数统计"
            data={chartData}
            categories={[
              { key: '访问量', name: '访问量', color: 'hsl(var(--chart-1))' },
              { key: '用户数', name: '用户数', color: 'hsl(var(--chart-2))' },
            ]}
          />

          <BarChart
            title="收支统计"
            description="近6个月收支情况"
            data={barData}
            categories={[
              { key: '收入', name: '收入', color: 'hsl(var(--chart-2))' },
              { key: '支出', name: '支出', color: 'hsl(var(--chart-3))' },
            ]}
          />
        </div>

        <div className="grid gap-6 md:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">用户信息</CardTitle>
              <User className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p className="text-sm">
                  <span className="font-medium">用户名：</span>
                  {user?.username}
                </p>
                <p className="text-sm">
                  <span className="font-medium">邮箱：</span>
                  {user?.email || '未设置'}
                </p>
                <p className="text-sm">
                  <span className="font-medium">角色：</span>
                  {user?.role}
                </p>
                <p className="text-sm">
                  <span className="font-medium">状态：</span>
                  <span
                    className={
                      user?.is_active ? 'text-success' : 'text-destructive'
                    }
                  >
                    {user?.is_active ? '活跃' : '禁用'}
                  </span>
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">快速操作</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Button variant="outline" className="w-full justify-start">
                  用户管理
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  会员管理
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  系统设置
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">系统操作</CardTitle>
            </CardHeader>
            <CardContent>
              <Button
                variant="destructive"
                className="w-full"
                onClick={handleLogout}
              >
                <LogOut className="h-4 w-4 mr-2" />
                退出登录
              </Button>
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>系统状态</CardTitle>
              <CardDescription>
                当前系统运行正常，所有服务可用。
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex items-center space-x-2">
                    <Badge variant="success" className="text-xs">API服务</Badge>
                    <span className="text-sm text-muted-foreground">正常</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="success" className="text-xs">数据库</Badge>
                    <span className="text-sm text-muted-foreground">正常</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="info" className="text-xs">缓存</Badge>
                    <span className="text-sm text-muted-foreground">正常</span>
                  </div>
                </div>

                <div className="text-sm text-muted-foreground">
                  <p>认证系统已成功实现并正常工作！</p>
                  <p className="mt-2">功能包括：</p>
                  <ul className="list-disc list-inside mt-2 space-y-1">
                    <li>JWT 认证和状态管理</li>
                    <li>登录页面和表单验证</li>
                    <li>路由保护中间件</li>
                    <li>自动令牌刷新</li>
                    <li>安全的登出处理</li>
                    <li>响应式侧边栏和导航</li>
                    <li>主题切换功能</li>
                    <li>面包屑导航</li>
                    <li>数据可视化图表</li>
                    <li>完整的UI设计系统</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
}
