export const MESSAGES = {
  // 通用消息
  COMMON: {
    SUCCESS: '操作成功',
    FAILED: '操作失败',
    LOADING: '加载中...',
    EMPTY: '暂无数据',
    ERROR: '请求错误',
    REQUIRED: '此项必填',
    INVALID_FORMAT: '格式不正确',
    NETWORK_ERROR: '网络错误，请检查网络连接',
    SERVER_ERROR: '服务器错误，请稍后再试',
    UNKNOWN_ERROR: '未知错误',
    EXPIRED: '登录已过期，请重新登录',
    UNAUTHORIZED: '无权限访问',
  },

  // 认证相关消息
  AUTH: {
    LOGIN_SUCCESS: '登录成功',
    LOGIN_FAILED: '登录失败',
    LOGOUT_SUCCESS: '登出成功',
    INVALID_CREDENTIALS: '用户名或密码不正确',
    SESSION_EXPIRED: '会话已过期，请重新登录',
  },

  // 用户管理消息
  USERS: {
    CREATE_SUCCESS: '用户创建成功',
    CREATE_FAILED: '用户创建失败',
    UPDATE_SUCCESS: '用户信息更新成功',
    UPDATE_FAILED: '用户信息更新失败',
    DELETE_SUCCESS: '用户删除成功',
    DELETE_FAILED: '用户删除失败',
    DELETE_CONFIRM: '确认删除该用户？',
    USERNAME_EXISTS: '用户名已存在',
    EMAIL_EXISTS: '邮箱已存在',
    PHONE_EXISTS: '手机号已存在',
  },

  // 会员管理消息
  MEMBERS: {
    CREATE_SUCCESS: '会员创建成功',
    CREATE_FAILED: '会员创建失败',
    UPDATE_SUCCESS: '会员信息更新成功',
    UPDATE_FAILED: '会员信息更新失败',
    DELETE_SUCCESS: '会员删除成功',
    DELETE_FAILED: '会员删除失败',
    DELETE_CONFIRM: '确认删除该会员？',
    PHONE_EXISTS: '手机号已存在',
    EMAIL_EXISTS: '邮箱已存在',
    STATUS_UPDATE_SUCCESS: '会员状态更新成功',
    STATUS_UPDATE_FAILED: '会员状态更新失败',
  },

  // 会员卡模板管理
  CARD_TEMPLATES: {
    CREATE_SUCCESS: '会员卡模板创建成功',
    CREATE_FAILED: '会员卡模板创建失败',
    UPDATE_SUCCESS: '会员卡模板更新成功',
    UPDATE_FAILED: '会员卡模板更新失败',
    DELETE_SUCCESS: '会员卡模板删除成功',
    DELETE_FAILED: '会员卡模板删除失败',
    DELETE_CONFIRM: '确认删除该会员卡模板？',
    TOGGLE_STATUS_SUCCESS: '会员卡模板状态更新成功',
    TOGGLE_STATUS_FAILED: '会员卡模板状态更新失败',
  },

  // 会员卡管理
  MEMBER_CARDS: {
    CREATE_SUCCESS: '会员卡创建成功',
    CREATE_FAILED: '会员卡创建失败',
    UPDATE_SUCCESS: '会员卡更新成功',
    UPDATE_FAILED: '会员卡更新失败',
    FREEZE_SUCCESS: '会员卡冻结成功',
    FREEZE_FAILED: '会员卡冻结失败',
    UNFREEZE_SUCCESS: '会员卡解冻成功',
    UNFREEZE_FAILED: '会员卡解冻失败',
    CANCEL_SUCCESS: '会员卡注销成功',
    CANCEL_FAILED: '会员卡注销失败',
    RECHARGE_SUCCESS: '充值成功',
    RECHARGE_FAILED: '充值失败',
    DEDUCT_SUCCESS: '扣费成功',
    DEDUCT_FAILED: '扣费失败',
    FREEZE_CONFIRM: '确认冻结该会员卡？',
    UNFREEZE_CONFIRM: '确认解冻该会员卡？',
    CANCEL_CONFIRM: '确认注销该会员卡？',
  },

  // 标签管理消息
  TAGS: {
    // 标签分类
    CATEGORY_CREATE_SUCCESS: '标签分类创建成功',
    CATEGORY_CREATE_FAILED: '标签分类创建失败',
    CATEGORY_UPDATE_SUCCESS: '标签分类更新成功',
    CATEGORY_UPDATE_FAILED: '标签分类更新失败',
    CATEGORY_DELETE_SUCCESS: '标签分类删除成功',
    CATEGORY_DELETE_FAILED: '标签分类删除失败',
    CATEGORY_DELETE_CONFIRM: '确认删除该标签分类？删除后该分类下的所有标签也将被删除。',

    // 标签
    CREATE_SUCCESS: '标签创建成功',
    CREATE_FAILED: '标签创建失败',
    UPDATE_SUCCESS: '标签更新成功',
    UPDATE_FAILED: '标签更新失败',
    DELETE_SUCCESS: '标签删除成功',
    DELETE_FAILED: '标签删除失败',
    DELETE_CONFIRM: '确认删除该标签？',
    BATCH_CREATE_SUCCESS: '批量创建标签成功',
    BATCH_CREATE_FAILED: '批量创建标签失败',
    BATCH_UPDATE_SUCCESS: '批量更新标签成功',
    BATCH_UPDATE_FAILED: '批量更新标签失败',
    BATCH_DELETE_SUCCESS: '批量删除标签成功',
    BATCH_DELETE_FAILED: '批量删除标签失败',
    BATCH_DELETE_CONFIRM: '确认删除选中的标签？',

    // 教师标签关联
    ASSIGN_SUCCESS: '标签分配成功',
    ASSIGN_FAILED: '标签分配失败',
    REMOVE_SUCCESS: '标签移除成功',
    REMOVE_FAILED: '标签移除失败',
    BATCH_ASSIGN_SUCCESS: '批量分配标签成功',
    BATCH_ASSIGN_FAILED: '批量分配标签失败',
  },
};
