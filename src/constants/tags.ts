import { TagStatus } from '@/types/api/tags';

// 标签状态显示名称
export const TagStatusNames: Record<TagStatus, string> = {
  [TagStatus.ACTIVE]: '激活',
  [TagStatus.INACTIVE]: '停用',
};

// 标签状态颜色映射
export const TagStatusColors: Record<TagStatus, string> = {
  [TagStatus.ACTIVE]: 'success',
  [TagStatus.INACTIVE]: 'secondary',
};

// 默认标签颜色选项
export const DefaultTagColors = [
  '#3B82F6', // 蓝色
  '#10B981', // 绿色
  '#F59E0B', // 橙色
  '#EF4444', // 红色
  '#8B5CF6', // 紫色
  '#06B6D4', // 青色
  '#F97316', // 橙红色
  '#84CC16', // 青绿色
  '#EC4899', // 粉色
  '#6B7280', // 灰色
];

// 标签管理相关消息
export const TAG_MESSAGES = {
  // 标签分类消息
  CATEGORY: {
    CREATE_SUCCESS: '标签分类创建成功',
    UPDATE_SUCCESS: '标签分类更新成功',
    DELETE_SUCCESS: '标签分类删除成功',
    DELETE_CONFIRM: '确定要删除此标签分类吗？删除后该分类下的所有标签也将被删除。',
    FETCH_ERROR: '获取标签分类失败',
    CREATE_ERROR: '创建标签分类失败',
    UPDATE_ERROR: '更新标签分类失败',
    DELETE_ERROR: '删除标签分类失败',
    NAME_REQUIRED: '分类名称不能为空',
    NAME_EXISTS: '分类名称已存在',
  },
  
  // 标签消息
  TAG: {
    CREATE_SUCCESS: '标签创建成功',
    UPDATE_SUCCESS: '标签更新成功',
    DELETE_SUCCESS: '标签删除成功',
    BATCH_CREATE_SUCCESS: '批量创建标签成功',
    BATCH_UPDATE_SUCCESS: '批量更新标签成功',
    BATCH_DELETE_SUCCESS: '批量删除标签成功',
    DELETE_CONFIRM: '确定要删除此标签吗？',
    BATCH_DELETE_CONFIRM: '确定要删除选中的标签吗？',
    FETCH_ERROR: '获取标签失败',
    CREATE_ERROR: '创建标签失败',
    UPDATE_ERROR: '更新标签失败',
    DELETE_ERROR: '删除标签失败',
    BATCH_ERROR: '批量操作失败',
    NAME_REQUIRED: '标签名称不能为空',
    CATEGORY_REQUIRED: '请选择标签分类',
    NAME_EXISTS: '标签名称已存在',
    STATUS_UPDATE_SUCCESS: '标签状态更新成功',
  },
  
  // 教师标签关联消息
  TEACHER_TAG: {
    ASSIGN_SUCCESS: '标签分配成功',
    REMOVE_SUCCESS: '标签移除成功',
    BATCH_ASSIGN_SUCCESS: '批量分配标签成功',
    BATCH_REMOVE_SUCCESS: '批量移除标签成功',
    ASSIGN_ERROR: '标签分配失败',
    REMOVE_ERROR: '标签移除失败',
    BATCH_ERROR: '批量操作失败',
  },
};

// 表格默认配置
export const TAG_TABLE_CONFIG = {
  // 分类表格
  CATEGORY: {
    PAGE_SIZE: 10,
    DEFAULT_SORT: { id: 'created_at', desc: true },
  },
  
  // 标签表格
  TAG: {
    PAGE_SIZE: 20,
    DEFAULT_SORT: { id: 'created_at', desc: true },
  },
};

// 表单验证规则
export const TAG_VALIDATION = {
  CATEGORY: {
    NAME_MIN_LENGTH: 1,
    NAME_MAX_LENGTH: 50,
    DESCRIPTION_MAX_LENGTH: 200,
  },
  
  TAG: {
    NAME_MIN_LENGTH: 1,
    NAME_MAX_LENGTH: 30,
    DESCRIPTION_MAX_LENGTH: 100,
  },
};

// 批量操作限制
export const TAG_BATCH_LIMITS = {
  MAX_BATCH_CREATE: 50,
  MAX_BATCH_UPDATE: 100,
  MAX_BATCH_DELETE: 100,
};

// 标签搜索配置
export const TAG_SEARCH_CONFIG = {
  DEBOUNCE_DELAY: 300,
  MIN_SEARCH_LENGTH: 1,
};
