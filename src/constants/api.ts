export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: '/api/v1/auth/admin/login',
    LOGOUT: '/api/v1/auth/admin/logout',
    REFRESH: '/api/v1/auth/admin/refresh',
    REGISTER: '/api/v1/auth/admin/register',
    PROFILE: '/api/v1/auth/admin/profile',
  },

  // 用户相关
  USERS: {
    LIST: '/api/v1/admin/users',
    GET: (id: string) => `/api/v1/admin/users/${id}`,
    CREATE: '/api/v1/admin/users',
    UPDATE: (id: string) => `/api/v1/admin/users/${id}`,
    DELETE: (id: string) => `/api/v1/admin/users/${id}`,
    SALESMEN: '/api/v1/admin/users/salesman/list',
  },

  // 会员相关
  MEMBERS: {
    LIST: '/api/v1/admin/members/',
    GET: (id: string) => `/api/v1/admin/members/${id}`,
    CREATE: '/api/v1/admin/members/',
    UPDATE: (id: string) => `/api/v1/admin/members/${id}/update`,
    DELETE: (id: string) => `/api/v1/admin/members/${id}/delete`,
    UPDATE_STATUS: (id: string) => `/api/v1/admin/members/${id}/update-status`,
    COUNT: '/api/v1/admin/members/count',
  },

  // 会员卡模板管理
  CARD_TEMPLATES: {
    LIST: '/api/v1/admin/member-cards/templates',
    CREATE: '/api/v1/admin/member-cards/templates',
    GET: (id: string) => `/api/v1/admin/member-cards/templates/${id}`,
    UPDATE: (id: string) => `/api/v1/admin/member-cards/templates/${id}`,
    DELETE: (id: string) => `/api/v1/admin/member-cards/templates/${id}`,
    TOGGLE_STATUS: (id: string) =>
      `/api/v1/admin/member-cards/templates/${id}/toggle-status`,
  },

  // 会员卡管理
  MEMBER_CARDS: {
    LIST: '/api/v1/admin/member-cards/cards',
    CREATE: '/api/v1/admin/member-cards/cards',
    GET: (id: string) => `/api/v1/admin/member-cards/cards/${id}`,
    UPDATE: (id: string) => `/api/v1/admin/member-cards/cards/${id}`,
    GET_BY_MEMBER: (memberId: string) =>
      `/api/v1/admin/member-cards/members/${memberId}/cards`,
    FREEZE: (id: string) => `/api/v1/admin/member-cards/cards/${id}/freeze`,
    UNFREEZE: (id: string) => `/api/v1/admin/member-cards/cards/${id}/unfreeze`,
    CANCEL: (id: string) => `/api/v1/admin/member-cards/cards/${id}/cancel`,
    RECHARGE: (id: string) => `/api/v1/admin/member-cards/cards/${id}/recharge`,
    DEDUCT: (id: string) => `/api/v1/admin/member-cards/cards/${id}/deduct`,
    RECHARGE_HISTORY: (id: string) =>
      `/api/v1/admin/member-cards/cards/${id}/recharge-history`,
    CONSUMPTION_HISTORY: (id: string) =>
      `/api/v1/admin/member-cards/cards/${id}/consumption-history`,
    OPERATION_SUMMARY: (id: string) =>
      `/api/v1/admin/member-cards/cards/${id}/operation-summary`,
  },

  // 会员卡操作记录
  CARD_OPERATIONS: {
    LIST: '/api/v1/admin/member-cards/operations',
    GET: (id: string) => `/api/v1/admin/member-cards/operations/${id}`,
  },

  // 标签管理相关
  TAGS: {
    // 标签分类
    CATEGORIES: '/api/v1/admin/tags/categories',
    CATEGORY_DETAIL: (id: string) => `/api/v1/admin/tags/categories/${id}`,
    CATEGORY_TAGS: (id: string) => `/api/v1/admin/tags/categories/${id}/tags`,

    // 标签
    LIST: '/api/v1/admin/tags/with-teacher-count',
    CREATE: '/api/v1/admin/tags',
    DETAIL: (id: string) => `/api/v1/admin/tags/${id}`,
    BATCH_CREATE: '/api/v1/admin/tags/batch',
    BATCH_UPDATE: '/api/v1/admin/tags/batch/update',
    ACTIVE_TAGS: '/api/v1/admin/tags/active',

    // 教师标签关联
    TEACHER_TAGS: (teacherId: string) => `/api/v1/admin/teachers/${teacherId}/tags`,
    TEACHER_TAG_REMOVE: (teacherId: string) => `/api/v1/admin/teachers/${teacherId}/tags/remove`,
    TEACHER_TAG_BATCH: '/api/v1/admin/teachers/tags/batch',
  },

  // 教师管理相关
  TEACHERS: {
    LIST: '/api/v1/admin/teachers', // 目前不用这个接口，而是直接用下面这个 LISTALL，本接口留作备用
    LISTALL: '/api/v1/admin/teachers/all',
    DETAIL: (id: string) => `/api/v1/admin/teachers/${id}`,
    UPDATE: (id: string) => `/api/v1/admin/teachers/${id}/update`,
    DELETE: (id: string) => `/api/v1/admin/teachers/${id}/delete`,
    STATUS: (id: string) => `/api/v1/admin/teachers/${id}/status`,
    ACTIVATE: (id: string) => `/api/v1/admin/teachers/${id}/activate`,
    DEACTIVATE: (id: string) => `/api/v1/admin/teachers/${id}/deactivate`,
    UPDATE_STATS: (id: string) => `/api/v1/admin/teachers/${id}/update-stats`,
    AVATAR: (id: string) => `/api/v1/admin/teachers/${id}/avatar`,
    AVATAR_DELETE: (id: string) => `/api/v1/admin/teachers/${id}/avatar/delete`,
    TAGS: {
      LIST: (id: string) => `/api/v1/admin/teachers/${id}/tags`,
      ASSIGN: (id: string) => `/api/v1/admin/teachers/${id}/tags`,
      REMOVE: (id: string) => `/api/v1/admin/teachers/${id}/tags/remove`,
      BATCH: '/api/v1/admin/teachers/tags/batch',
    },
    STATISTICS: '/api/v1/admin/teachers/statistics',
    PRIORITY: '/api/v1/admin/teachers/priority',
    AVAILABLE: '/api/v1/admin/teachers/available',
    SEARCH: '/api/v1/admin/teachers/search',
  },
};
