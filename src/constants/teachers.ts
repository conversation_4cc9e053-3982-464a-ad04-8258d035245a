import { TeacherStatus, TeacherCategory, TeacherRegion } from '@/types/api/teachers';

// 教师状态显示名称
export const TeacherStatusNames: Record<TeacherStatus, string> = {
  [TeacherStatus.ACTIVE]: '激活',
  [TeacherStatus.INACTIVE]: '停用',
  [TeacherStatus.SUSPENDED]: '暂停',
};

// 教师状态颜色映射
export const TeacherStatusColors: Record<TeacherStatus, string> = {
  [TeacherStatus.ACTIVE]: 'success',
  [TeacherStatus.INACTIVE]: 'secondary',
  [TeacherStatus.SUSPENDED]: 'warning',
};

// 教师分类显示名称
export const TeacherCategoryNames: Record<TeacherCategory, string> = {
  [TeacherCategory.CHINESE]: '语文',
  [TeacherCategory.ENGLISH]: '英语',
  [TeacherCategory.MATH]: '数学',
  [TeacherCategory.SCIENCE]: '科学',
  [TeacherCategory.ART]: '美术',
  [TeacherCategory.MUSIC]: '音乐',
  [TeacherCategory.SPORTS]: '体育',
  [TeacherCategory.OTHER]: '其他',
};

// 教师区域显示名称
export const TeacherRegionNames: Record<TeacherRegion, string> = {
  [TeacherRegion.NORTH]: '北区',
  [TeacherRegion.SOUTH]: '南区',
  [TeacherRegion.EAST]: '东区',
  [TeacherRegion.WEST]: '西区',
  [TeacherRegion.CENTRAL]: '中心区',
  [TeacherRegion.ONLINE]: '线上',
};

// 教师管理相关消息
export const TEACHER_MESSAGES = {
  // 基础操作
  CREATE_SUCCESS: '教师创建成功',
  UPDATE_SUCCESS: '教师信息更新成功',
  DELETE_SUCCESS: '教师删除成功',
  DELETE_CONFIRM: '确定要删除此教师吗？删除后无法恢复。',
  
  // 状态操作
  ACTIVATE_SUCCESS: '教师激活成功',
  DEACTIVATE_SUCCESS: '教师停用成功',
  SUSPEND_SUCCESS: '教师暂停成功',
  STATUS_UPDATE_SUCCESS: '教师状态更新成功',
  
  // 标签操作
  TAG_ASSIGN_SUCCESS: '标签分配成功',
  TAG_REMOVE_SUCCESS: '标签移除成功',
  TAG_BATCH_SUCCESS: '批量标签操作成功',
  
  // 头像操作
  AVATAR_UPLOAD_SUCCESS: '头像上传成功',
  AVATAR_DELETE_SUCCESS: '头像删除成功',
  
  // 错误消息
  CREATE_FAILED: '教师创建失败',
  UPDATE_FAILED: '教师信息更新失败',
  DELETE_FAILED: '教师删除失败',
  STATUS_UPDATE_FAILED: '状态更新失败',
  TAG_OPERATION_FAILED: '标签操作失败',
  AVATAR_UPLOAD_FAILED: '头像上传失败',
  FETCH_FAILED: '获取教师信息失败',
  
  // 验证消息
  NAME_REQUIRED: '教师姓名不能为空',
  EMAIL_INVALID: '邮箱格式不正确',
  PHONE_INVALID: '手机号格式不正确',
  HOURLY_RATE_REQUIRED: '课时费不能为空',
  HOURLY_RATE_MIN: '课时费不能小于0',
  CATEGORY_REQUIRED: '请选择教师分类',
  REGION_REQUIRED: '请选择教师区域',
  PRIORITY_MIN: '优先级不能小于0',
  PRIORITY_MAX: '优先级不能大于100',
};

// 表格默认配置
export const TEACHER_TABLE_CONFIG = {
  PAGE_SIZE: 20,
  DEFAULT_SORT: { id: 'created_at', desc: true },
  MAX_PAGE_SIZE: 100,
};

// 表单验证规则
export const TEACHER_VALIDATION = {
  NAME_MIN_LENGTH: 1,
  NAME_MAX_LENGTH: 50,
  EMAIL_MAX_LENGTH: 100,
  PHONE_LENGTH: 11,
  BIO_MAX_LENGTH: 500,
  EDUCATION_MAX_LENGTH: 200,
  SPECIALTIES_MAX_LENGTH: 300,
  EXPERIENCE_YEARS_MIN: 0,
  EXPERIENCE_YEARS_MAX: 50,
  HOURLY_RATE_MIN: 0,
  HOURLY_RATE_MAX: 10000,
  PRIORITY_MIN: 0,
  PRIORITY_MAX: 100,
};

// 搜索配置
export const TEACHER_SEARCH_CONFIG = {
  DEBOUNCE_DELAY: 300,
  MIN_SEARCH_LENGTH: 1,
  DEFAULT_LIMIT: 20,
  MAX_LIMIT: 100,
};

// 头像配置
export const TEACHER_AVATAR_CONFIG = {
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
  DEFAULT_AVATAR: '/images/default-teacher-avatar.png',
};

// 批量操作限制
export const TEACHER_BATCH_LIMITS = {
  MAX_BATCH_ASSIGN: 100,
  MAX_BATCH_REMOVE: 100,
  MAX_BATCH_STATUS_UPDATE: 50,
};

// 统计图表配置
export const TEACHER_CHART_CONFIG = {
  COLORS: {
    ACTIVE: '#10B981',
    INACTIVE: '#6B7280',
    SUSPENDED: '#F59E0B',
  },
  CATEGORY_COLORS: [
    '#3B82F6', // 蓝色
    '#10B981', // 绿色
    '#F59E0B', // 橙色
    '#EF4444', // 红色
    '#8B5CF6', // 紫色
    '#06B6D4', // 青色
    '#F97316', // 橙红色
    '#84CC16', // 青绿色
  ],
};

// 默认查询参数
export const DEFAULT_TEACHER_QUERY = {
  page: 1,
  size: TEACHER_TABLE_CONFIG.PAGE_SIZE,
  sort_by: 'created_at',
  sort_order: 'desc' as const,
};
