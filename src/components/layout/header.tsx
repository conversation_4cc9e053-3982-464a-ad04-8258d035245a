'use client';

import { Search, User, LogOut, Settings, <PERSON>u, <PERSON>, Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ThemeToggle } from './theme-toggle';
import { Breadcrumb } from './breadcrumb';
import { useAuth } from '@/hooks/use-auth';
import { useUIStore } from '@/lib/stores/ui-store';

export function Header() {
  const { user, logout } = useAuth();

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const { sidebarCollapsed, setSidebarCollapsed } = useUIStore();

  return (
    <header className="sticky top-0 z-30 flex h-16 items-center justify-between border-b bg-background px-4 transition-all duration-300">
      {/* 左侧：折叠按钮和面包屑 */}
      <div className="flex items-center pl-2">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
          className="hidden md:flex h-10 w-10"
        >
          <Menu className="h-4 w-4" />
          <span className="sr-only">切换侧边栏</span>
        </Button>
        <div className="md:hidden w-10" />

        {/* 面包屑导航 */}
        <div className="ml-2">
          <Breadcrumb />
        </div>
      </div>

      {/* 右侧：搜索、用户菜单、主题切换 */}
      <div className="flex items-center space-x-4 pr-4">
        {/* 搜索框 */}
        <div className="relative hidden sm:block">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input placeholder="搜索..." className="w-64 pl-10" />
        </div>

        {/* 搜索按钮（移动端） */}
        <Button variant="outline" size="icon" className="sm:hidden">
          <Search className="h-4 w-4" />
          <span className="sr-only">搜索</span>
        </Button>

        {/* 通知中心 */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="icon" className="relative">
              <Bell className="h-4 w-4" />
              {/* 通知徽章 */}
              <Badge
                variant="destructive"
                className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs flex items-center justify-center"
              >
                3
              </Badge>
              <span className="sr-only">通知</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-80" align="end">
            <DropdownMenuLabel>通知中心</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <div className="space-y-2 p-2">
              <div className="flex items-start space-x-3 p-2 rounded-md hover:bg-muted">
                <div className="w-2 h-2 bg-destructive rounded-full mt-2 flex-shrink-0"></div>
                <div className="flex-1 space-y-1">
                  <p className="text-sm font-medium">系统维护通知</p>
                  <p className="text-xs text-muted-foreground">
                    系统将在今晚23:00-01:00进行维护
                  </p>
                  <p className="text-xs text-muted-foreground">2分钟前</p>
                </div>
              </div>
              <div className="flex items-start space-x-3 p-2 rounded-md hover:bg-muted">
                <div className="w-2 h-2 bg-warning rounded-full mt-2 flex-shrink-0"></div>
                <div className="flex-1 space-y-1">
                  <p className="text-sm font-medium">新用户注册</p>
                  <p className="text-xs text-muted-foreground">
                    用户 "张三" 已成功注册
                  </p>
                  <p className="text-xs text-muted-foreground">10分钟前</p>
                </div>
              </div>
              <div className="flex items-start space-x-3 p-2 rounded-md hover:bg-muted">
                <div className="w-2 h-2 bg-info rounded-full mt-2 flex-shrink-0"></div>
                <div className="flex-1 space-y-1">
                  <p className="text-sm font-medium">数据备份完成</p>
                  <p className="text-xs text-muted-foreground">
                    今日数据备份已完成
                  </p>
                  <p className="text-xs text-muted-foreground">1小时前</p>
                </div>
              </div>
            </div>
            <DropdownMenuSeparator />
            <div className="p-2">
              <Button variant="ghost" className="w-full text-xs">
                查看全部通知
              </Button>
            </div>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* 主题切换 */}
        <ThemeToggle />

        {/* 用户菜单 */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="relative h-10 w-10 rounded-full">
              <Avatar className="h-10 w-10">
                <AvatarImage src={''} alt={user?.username || ''} />
                <AvatarFallback>
                  {user?.username?.charAt(0).toUpperCase() || 'U'}
                </AvatarFallback>
              </Avatar>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56" align="end" forceMount>
            <DropdownMenuLabel className="font-normal">
              <div className="flex flex-col space-y-2">
                <div className="flex items-center space-x-2">
                  <p className="text-sm font-medium leading-none">
                    {user?.username || '用户'}
                  </p>
                  <Badge
                    variant={user?.is_active ? 'success' : 'secondary'}
                    className="text-xs px-1.5 py-0.5"
                  >
                    {user?.is_active ? '在线' : '离线'}
                  </Badge>
                </div>
                <p className="text-xs leading-none text-muted-foreground">
                  {user?.email || '未设置邮箱'}
                </p>
                <div className="flex items-center space-x-2">
                  <Shield className="h-3 w-3 text-muted-foreground" />
                  <span className="text-xs text-muted-foreground">
                    {user?.role === 'super_admin' ? '超级管理员' :
                     user?.role === 'admin' ? '管理员' :
                     user?.role === 'agent' ? '代理商' : '销售'}
                  </span>
                </div>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <User className="mr-2 h-4 w-4" />
              <span>个人资料</span>
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Settings className="mr-2 h-4 w-4" />
              <span>设置</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleLogout}>
              <LogOut className="mr-2 h-4 w-4" />
              <span>退出登录</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
}
