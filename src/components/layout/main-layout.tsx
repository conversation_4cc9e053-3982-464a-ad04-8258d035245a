'use client';

import { useAuth } from '@/hooks/use-auth';
import { useUIStore } from '@/lib/stores/ui-store';
import { cn } from '@/lib/utils';
import { Sidebar } from './sidebar';
import { Header } from './header';

interface MainLayoutProps {
  children: React.ReactNode;
}

export function MainLayout({ children }: MainLayoutProps) {
  const { isAuthenticated } = useAuth();
  const { sidebarCollapsed } = useUIStore();

  // 如果未认证，不显示布局
  if (!isAuthenticated) {
    return <>{children}</>;
  }

  return (
    <div className="min-h-screen bg-background">
      {/* 侧边栏 */}
      <Sidebar />

      {/* 主内容区域 */}
      <div
        className={cn(
          'flex flex-col transition-all duration-300',
          sidebarCollapsed
            ? 'md:ml-[var(--sidebar-collapsed-width)]'
            : 'md:ml-[var(--sidebar-width)]'
        )}
      >
        {/* 顶部栏 */}
        <Header />

        {/* 内容区域 */}
        <main className="flex-1 md:ml-4">
          {/* 页面内容 */}
          <div className="p-4">{children}</div>
        </main>
      </div>
    </div>
  );
}
