'use client';

import { <PERSON>, Sun } from 'lucide-react';
import { useTheme } from 'next-themes';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from '@/components/ui/dropdown-menu';
import { useUIStore } from '@/lib/stores/ui-store';
import { useEffect } from 'react';

export function ThemeToggle() {
  const { setTheme, resolvedTheme } = useTheme();
  const { colorScheme, setColorScheme } = useUIStore();

  // 应用配色方案
  useEffect(() => {
    // 移除之前的配色类
    document.documentElement.classList.remove(
      'sidebar-blue',
      'sidebar-purple',
      'sidebar-teal',
      'sidebar-gray',
      'theme-blue',
      'theme-purple',
      'theme-teal',
      'theme-gray'
    );

    // 添加新的配色类 - 同时应用到侧边栏和主题色系统
    document.documentElement.classList.add('sidebar-theme'); // 添加基础侧边栏主题类
    document.documentElement.classList.add(`sidebar-${colorScheme}`);
    document.documentElement.classList.add(`theme-${colorScheme}`);

    // 确保暗色模式类名正确应用
    if (resolvedTheme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [colorScheme, resolvedTheme]);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon">
          <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
          <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
          <span className="sr-only">切换主题</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>外观</DropdownMenuLabel>
        <DropdownMenuItem onClick={() => setTheme('light')}>
          浅色
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme('dark')}>
          深色
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme('system')}>
          系统
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        <DropdownMenuLabel>侧边栏配色</DropdownMenuLabel>
        <DropdownMenuItem onClick={() => setColorScheme('blue')}>
          <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
          深蓝色系
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setColorScheme('purple')}>
          <div className="w-3 h-3 rounded-full bg-purple-500 mr-2"></div>
          深紫色系
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setColorScheme('teal')}>
          <div className="w-3 h-3 rounded-full bg-teal-500 mr-2"></div>
          青绿色系
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setColorScheme('gray')}>
          <div className="w-3 h-3 rounded-full bg-gray-500 mr-2"></div>
          灰色系
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
