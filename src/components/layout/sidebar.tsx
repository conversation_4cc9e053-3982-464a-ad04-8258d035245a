'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { ChevronDown, Menu } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { navigation, type NavItem } from '@/config/navigation';
import { siteConfig } from '@/config/site';
import { useUIStore } from '@/lib/stores/ui-store';

interface SidebarProps {
  className?: string;
}

export function Sidebar({ className }: SidebarProps) {
  const pathname = usePathname();
  const { sidebarCollapsed } = useUIStore();

  return (
    <>
      {/* 桌面端侧边栏 */}
      <div
        className={cn(
          'hidden md:flex h-screen flex-col fixed left-0 top-0 z-40 p-4',
          className
        )}
      >
        <div
          className={cn(
            'sidebar-container flex h-full flex-col transition-all duration-300 overflow-hidden',
            sidebarCollapsed
              ? 'w-[var(--sidebar-collapsed-width)]'
              : 'w-[var(--sidebar-width)]'
          )}
        >
          <SidebarContent pathname={pathname} collapsed={sidebarCollapsed} />
        </div>
      </div>

      {/* 移动端侧边栏 */}
      <Sheet>
        <SheetTrigger asChild>
          <Button
            variant="outline"
            size="icon"
            className="md:hidden fixed left-4 top-4 z-50"
          >
            <Menu className="h-4 w-4" />
            <span className="sr-only">打开菜单</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="p-0">
          <div className="sidebar-container h-full">
            <SidebarContent pathname={pathname} collapsed={false} />
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
}

interface SidebarContentProps {
  pathname: string;
  collapsed: boolean;
}

function SidebarContent({ pathname, collapsed }: SidebarContentProps) {
  return (
    <div className="flex h-full flex-col">
      {/* 头部 */}
      <div className="flex items-center px-5 py-4">
        <Link href="/dashboard" className="flex items-center gap-3">
          <div className="flex-shrink-0 w-8 h-8">
            <div className="w-8 h-8 bg-gradient-to-r from-[#3375FF] to-[#649BFF] rounded-md flex items-center justify-center">
              <div className="w-4 h-4 bg-white rounded-sm transform rotate-45"></div>
            </div>
          </div>
          {!collapsed && (
            <span className="text-lg font-semibold">{siteConfig.name}</span>
          )}
        </Link>
        {/* 移除侧边栏折叠按钮 */}
      </div>

      {/* 导航菜单 */}
      <ScrollArea className="flex-1 px-3 py-4">
        <nav className="space-y-1">
          {navigation.map(item => (
            <NavItemComponent
              key={item.href}
              item={item}
              pathname={pathname}
              collapsed={collapsed}
            />
          ))}
        </nav>
      </ScrollArea>
    </div>
  );
}

interface NavItemComponentProps {
  item: NavItem;
  pathname: string;
  collapsed: boolean;
}

function NavItemComponent({
  item,
  pathname,
  collapsed,
}: NavItemComponentProps) {
  const [isOpen, setIsOpen] = useState(
    item.children?.some(child => pathname.startsWith(child.href)) || false
  );

  // 检查是否有子菜单项与当前路径匹配
  const hasActiveChild = item.children?.some(child => pathname === child.href);

  // 只有当前路径完全匹配且没有子菜单被激活时才激活一级菜单
  // 如果当前路径与父菜单路径相同，但是存在子菜单也有相同路径，则不激活父菜单
  const isActive =
    pathname === item.href &&
    !hasActiveChild &&
    !item.children?.some(child => child.href === item.href);

  const hasChildren = item.children && item.children.length > 0;

  // 检查是否有通知标记
  const hasNotification = item.hasNotification;
  const hasChildWithNotification = item.children?.some(
    child => child.hasNotification
  );

  if (hasChildren) {
    return (
      <div className="relative">
        {(hasNotification || hasChildWithNotification) && (
          <div className="sidebar-notification" />
        )}

        <Button
          variant="ghost"
          className={cn(
            'sidebar-item w-full justify-start font-normal relative',
            collapsed ? 'px-2' : 'px-5',
            isActive ? 'active' : ''
          )}
          onClick={() => !collapsed && setIsOpen(!isOpen)}
        >
          {/* 活跃状态指示器 */}
          {isActive && <div className="sidebar-item-indicator" />}

          <item.icon
            className={cn(
              'sidebar-icon flex-shrink-0',
              collapsed ? '' : 'mr-3'
            )}
          />
          {!collapsed && (
            <>
              <span className="flex-1 text-left">{item.title}</span>
              <ChevronDown
                className={cn('sidebar-chevron', isOpen && 'rotate-180')}
              />
            </>
          )}

          {/* 折叠状态下的工具提示 */}
          {collapsed && (
            <div className="sidebar-tooltip">
              {item.title}
            </div>
          )}
        </Button>

        {!collapsed && isOpen && (
          <div className="mt-1 space-y-1">
            {item.children?.map(child => {
              const isChildActive = pathname === child.href;

              return (
                <div key={child.href} className="relative">
                  {child.hasNotification && (
                    <div className="sidebar-notification" />
                  )}
                  <Link href={child.href}>
                    <Button
                      variant="ghost"
                      className={cn(
                        'sidebar-item w-full justify-start font-normal relative',
                        'pl-14 pr-3', // 调整左内边距，使其与一级菜单文字左对齐
                        isChildActive ? 'active' : ''
                      )}
                    >
                      {/* 子菜单活跃状态指示器 */}
                      {isChildActive && <div className="sidebar-item-indicator" />}
                      <span className="flex-1 text-left">{child.title}</span>
                    </Button>
                  </Link>
                </div>
              );
            })}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="relative">
      {hasNotification && <div className="sidebar-notification" />}
      <Link href={item.href}>
        <Button
          variant="ghost"
          className={cn(
            'sidebar-item w-full justify-start font-normal relative',
            collapsed ? 'px-2' : 'px-5',
            isActive ? 'active' : ''
          )}
        >
          {/* 活跃状态指示器 */}
          {isActive && <div className="sidebar-item-indicator" />}

          <item.icon
            className={cn(
              'sidebar-icon flex-shrink-0',
              collapsed ? '' : 'mr-3'
            )}
          />
          {!collapsed && <span className="flex-1 text-left">{item.title}</span>}

          {/* 折叠状态下的工具提示 */}
          {collapsed && (
            <div className="sidebar-tooltip">
              {item.title}
            </div>
          )}
        </Button>
      </Link>
    </div>
  );
}
