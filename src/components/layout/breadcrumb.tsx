'use client';

import { ChevronRight, Home } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';

import { navigation } from '@/config/navigation';

interface BreadcrumbItem {
  title: string;
  href: string;
  icon?: React.ComponentType<{ className?: string }>;
}

export function Breadcrumb() {
  const pathname = usePathname();
  const breadcrumbs = generateBreadcrumbs(pathname);

  if (breadcrumbs.length <= 1) {
    return null;
  }

  return (
    <nav className="flex items-center space-x-1 text-sm">
      {breadcrumbs.map((item, index) => {
        const isLast = index === breadcrumbs.length - 1;
        const isFirst = index === 0;

        return (
          <div key={item.href} className="flex items-center">
            {index > 0 && (
              <ChevronRight className="mx-2 h-3.5 w-3.5 text-muted-foreground" />
            )}

            {isLast ? (
              <span className={cn(
                "flex items-center font-medium text-foreground px-2 py-1 rounded-md",
                "bg-muted/50"
              )}>
                {isFirst && <Home className="mr-1.5 h-3.5 w-3.5" />}
                {item.title}
              </span>
            ) : (
              <Link
                href={item.href}
                className={cn(
                  "flex items-center px-2 py-1 rounded-md transition-colors",
                  "text-muted-foreground hover:text-foreground hover:bg-muted/50"
                )}
              >
                {isFirst && <Home className="mr-1.5 h-3.5 w-3.5" />}
                {item.title}
              </Link>
            )}
          </div>
        );
      })}
    </nav>
  );
}

function generateBreadcrumbs(pathname: string): BreadcrumbItem[] {
  const breadcrumbs: BreadcrumbItem[] = [
    {
      title: '首页',
      href: '/dashboard',
    },
  ];

  // 如果就是首页，直接返回
  if (pathname === '/dashboard') {
    return breadcrumbs;
  }

  // 查找匹配的导航项
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const findNavItem = (items: typeof navigation, path: string): any => {
    for (const item of items) {
      if (item.href === path) {
        return item;
      }
      if (item.children) {
        const found = findNavItem(item.children, path);
        if (found) {
          return { parent: item, child: found };
        }
      }
    }
    return null;
  };

  const navItem = findNavItem(navigation, pathname);

  if (navItem) {
    if (navItem.parent && navItem.child) {
      // 有父级菜单
      breadcrumbs.push({
        title: navItem.parent.title,
        href: navItem.parent.href,
      });
      breadcrumbs.push({
        title: navItem.child.title,
        href: navItem.child.href,
      });
    } else {
      // 顶级菜单
      breadcrumbs.push({
        title: navItem.title,
        href: navItem.href,
      });
    }
  } else {
    // 动态路由处理
    const segments = pathname.split('/').filter(Boolean);

    for (let i = 1; i < segments.length; i++) {
      const path = '/' + segments.slice(0, i + 1).join('/');
      const segment = segments[i];

      // 尝试从导航中找到匹配项
      const found = findNavItem(navigation, path);
      if (found && !found.parent) {
        breadcrumbs.push({
          title: found.title,
          href: found.href,
        });
      } else {
        // 生成默认标题
        let title = segment;

        // 特殊路径处理
        const titleMap: Record<string, string> = {
          'create': '新增',
          'edit': '编辑',
          'users': '用户管理',
          'members': '会员管理',
          'dashboard': '仪表盘',
          'examples': '示例',
          'ui-design-system': 'UI设计系统',
        };

        if (titleMap[segment]) {
          title = titleMap[segment];
        } else if (segment.match(/^[0-9a-f-]+$/)) {
          // 看起来像 ID
          title = '详情';
        } else {
          // 首字母大写
          title = segment.charAt(0).toUpperCase() + segment.slice(1);
        }

        breadcrumbs.push({
          title,
          href: path,
        });
      }
    }
  }

  return breadcrumbs;
}
