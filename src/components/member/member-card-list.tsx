'use client';

import { useState } from 'react';
import { MoreHorizontal, CreditCard, Clock, DollarSign, Freeze, Play, Ban, Plus } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { CompactDataTable } from '@/components/tables';
import { DataStateWrapper } from '@/components/ui/loading-states';
import { useMemberCards } from '@/hooks/use-member-cards';
import { useCardTemplates } from '@/hooks/use-card-templates';
import type { MemberCardRead } from '@/types/api';
import { CardTypeNames, CardStatusNames } from '@/types/api';
import { formatDate, cn } from '@/lib/utils';

interface MemberCardListProps {
  memberId: number;
  onCreateCard: () => void;
  onRecharge: (card: MemberCardRead) => void;
  onDeduct: (card: MemberCardRead) => void;
  onFreeze: (card: MemberCardRead) => void;
  onUnfreeze: (card: MemberCardRead) => void;
  onCancel: (card: MemberCardRead) => void;
}

export function MemberCardList({
  memberId,
  onCreateCard,
  onRecharge,
  onDeduct,
  onFreeze,
  onUnfreeze,
  onCancel,
}: MemberCardListProps) {
  const [viewMode, setViewMode] = useState<'cards' | 'table'>('cards');
  
  const { data: memberCards, isLoading, error } = useMemberCards(memberId);
  const { data: templates } = useCardTemplates();

  const cards = memberCards?.data || [];

  // 获取状态变体
  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'active':
        return 'default';
      case 'frozen':
        return 'secondary';
      case 'expired':
        return 'destructive';
      case 'cancelled':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  // 获取卡片类型图标
  const getCardTypeIcon = (cardType: string) => {
    switch (cardType) {
      case 'times_limited':
      case 'times_unlimited':
        return <Clock className="h-4 w-4" />;
      case 'value_limited':
      case 'value_unlimited':
        return <DollarSign className="h-4 w-4" />;
      default:
        return <CreditCard className="h-4 w-4" />;
    }
  };

  // 格式化余额显示
  const formatBalance = (balance: number, cardType: string) => {
    const isTimesCard = cardType === 'times_limited' || cardType === 'times_unlimited';
    return isTimesCard ? `${balance} 次` : `¥${balance.toFixed(2)}`;
  };

  // 操作按钮组件
  const ActionButtons = ({ card }: { card: MemberCardRead }) => (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>操作</DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {card.status === 'active' && (
          <>
            <DropdownMenuItem onClick={() => onRecharge(card)} className="cursor-pointer">
              <Plus className="mr-2 h-4 w-4" />
              充值
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onDeduct(card)} className="cursor-pointer">
              <DollarSign className="mr-2 h-4 w-4" />
              扣费
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onFreeze(card)} className="cursor-pointer">
              <Freeze className="mr-2 h-4 w-4" />
              冻结
            </DropdownMenuItem>
          </>
        )}
        
        {card.status === 'frozen' && (
          <DropdownMenuItem onClick={() => onUnfreeze(card)} className="cursor-pointer">
            <Play className="mr-2 h-4 w-4" />
            解冻
          </DropdownMenuItem>
        )}
        
        {(card.status === 'active' || card.status === 'frozen') && (
          <DropdownMenuItem 
            onClick={() => onCancel(card)} 
            className="cursor-pointer text-destructive focus:text-destructive"
          >
            <Ban className="mr-2 h-4 w-4" />
            注销
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );

  // 卡片视图
  const CardsView = () => (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {cards.map((card) => (
        <Card key={card.id} className="relative">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {getCardTypeIcon(card.card_type)}
                <CardTitle className="text-sm font-medium">
                  {CardTypeNames[card.card_type]}
                </CardTitle>
              </div>
              <ActionButtons card={card} />
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={getStatusVariant(card.status)} className="text-xs">
                {CardStatusNames[card.status]}
              </Badge>
              {card.card_number && (
                <span className="text-xs text-muted-foreground">
                  {card.card_number}
                </span>
              )}
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-3">
              <div>
                <p className="text-xs text-muted-foreground">当前余额</p>
                <p className="text-lg font-semibold">
                  {formatBalance(card.balance, card.card_type)}
                </p>
              </div>
              
              <div className="grid grid-cols-2 gap-3 text-xs">
                <div>
                  <p className="text-muted-foreground">总充值</p>
                  <p className="font-medium">¥{card.total_recharged.toFixed(2)}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">总消费</p>
                  <p className="font-medium">¥{card.total_consumed.toFixed(2)}</p>
                </div>
              </div>
              
              {card.expires_at && (
                <div>
                  <p className="text-xs text-muted-foreground">有效期至</p>
                  <p className="text-xs font-medium">
                    {formatDate(card.expires_at)}
                  </p>
                </div>
              )}
              
              {card.last_used_at && (
                <div>
                  <p className="text-xs text-muted-foreground">最后使用</p>
                  <p className="text-xs">
                    {formatDate(card.last_used_at)}
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );

  // 表格列定义
  const columns = [
    {
      accessorKey: 'card_type',
      header: '卡片类型',
      cell: ({ row }: { row: any }) => (
        <div className="flex items-center gap-2">
          {getCardTypeIcon(row.original.card_type)}
          <span className="text-sm">{CardTypeNames[row.original.card_type]}</span>
        </div>
      ),
    },
    {
      accessorKey: 'balance',
      header: '当前余额',
      cell: ({ row }: { row: any }) => (
        <span className="font-medium">
          {formatBalance(row.original.balance, row.original.card_type)}
        </span>
      ),
    },
    {
      accessorKey: 'status',
      header: '状态',
      cell: ({ row }: { row: any }) => (
        <Badge variant={getStatusVariant(row.original.status)} className="text-xs">
          {CardStatusNames[row.original.status]}
        </Badge>
      ),
    },
    {
      accessorKey: 'total_recharged',
      header: '总充值',
      cell: ({ row }: { row: any }) => (
        <span>¥{row.original.total_recharged.toFixed(2)}</span>
      ),
      meta: { className: 'hidden md:table-cell' },
    },
    {
      accessorKey: 'expires_at',
      header: '有效期',
      cell: ({ row }: { row: any }) => (
        <span className="text-sm">
          {row.original.expires_at ? formatDate(row.original.expires_at) : '无限期'}
        </span>
      ),
      meta: { className: 'hidden lg:table-cell' },
    },
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }: { row: any }) => <ActionButtons card={row.original} />,
    },
  ];

  return (
    <div className="space-y-4">
      {/* 头部操作栏 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h3 className="text-lg font-medium">会员卡管理</h3>
          <Badge variant="outline" className="text-xs">
            {cards.length} 张
          </Badge>
        </div>
        
        <div className="flex items-center gap-2">
          {/* 视图切换按钮 */}
          <div className="hidden sm:flex items-center gap-1 border rounded-md p-1">
            <Button
              variant={viewMode === 'cards' ? 'default' : 'ghost'}
              size="sm"
              className="h-7 px-2 text-xs"
              onClick={() => setViewMode('cards')}
            >
              卡片
            </Button>
            <Button
              variant={viewMode === 'table' ? 'default' : 'ghost'}
              size="sm"
              className="h-7 px-2 text-xs"
              onClick={() => setViewMode('table')}
            >
              表格
            </Button>
          </div>
          
          <Button size="sm" onClick={onCreateCard}>
            <Plus className="mr-1.5 h-3.5 w-3.5" />
            新增会员卡
          </Button>
        </div>
      </div>

      {/* 会员卡列表 */}
      <DataStateWrapper
        isLoading={isLoading}
        isEmpty={cards.length === 0}
        isError={!!error}
        emptyTitle="还没有会员卡"
        emptyDescription="该会员还没有任何会员卡，点击下方按钮创建第一张卡片"
        emptyActionLabel="创建会员卡"
        onEmptyAction={onCreateCard}
      >
        {viewMode === 'cards' ? (
          <CardsView />
        ) : (
          <div className="hidden sm:block">
            <CompactDataTable
              columns={columns}
              data={cards}
              isLoading={false}
            />
          </div>
        )}
        
        {/* 移动端始终显示卡片视图 */}
        <div className="block sm:hidden">
          <CardsView />
        </div>
      </DataStateWrapper>
    </div>
  );
}
