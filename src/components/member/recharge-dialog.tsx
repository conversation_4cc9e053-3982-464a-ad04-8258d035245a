'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import { DollarSign, CreditCard } from 'lucide-react';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { useRechargeMemberCard } from '@/hooks/use-member-cards';
import { CardType } from '@/types/api/card-template';

// 充值表单验证
const rechargeSchema = z.object({
  amount: z.string().min(1, '请输入充值金额').refine(
    (val) => {
      const num = parseFloat(val);
      return !isNaN(num) && num > 0;
    },
    '请输入有效的充值金额'
  ),
  remark: z.string().optional(),
});

type RechargeFormData = z.infer<typeof rechargeSchema>;

interface RechargeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  card: any;
}

export function RechargeDialog({
  open,
  onOpenChange,
  card,
}: RechargeDialogProps) {
  const rechargeMutation = useRechargeMemberCard();

  const form = useForm<RechargeFormData>({
    resolver: zodResolver(rechargeSchema),
    defaultValues: {
      amount: '',
      remark: '',
    },
  });

  // 处理充值
  const handleRecharge = async (data: RechargeFormData) => {
    if (!card) return;

    try {
      await rechargeMutation.mutateAsync({
        id: card.id.toString(),
        data: {
          amount: parseFloat(data.amount),
          remark: data.remark,
        },
      });

      onOpenChange(false);
      form.reset();
    } catch (error) {
      // 错误处理已在hook中处理
    }
  };

  // 处理取消
  const handleCancel = () => {
    onOpenChange(false);
    form.reset();
  };

  if (!card) return null;

  const isTimesCard = card.card_type === CardType.TIMES_LIMITED || card.card_type === CardType.TIMES_UNLIMITED;
  const unit = isTimesCard ? '次' : '元';

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            会员卡充值
          </DialogTitle>
          <DialogDescription>
            为会员卡充值余额或次数
          </DialogDescription>
        </DialogHeader>

        {/* 会员卡信息 */}
        <Card className="border-dashed">
          <CardContent className="p-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <CreditCard className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">{card.template_name}</span>
              </div>
              <div className="text-sm text-muted-foreground">
                当前余额：
                <span className="font-mono font-medium text-foreground ml-1">
                  {card.available_balance === null ? '不限' : card.available_balance} {unit}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleRecharge)} className="space-y-4">
            {/* 充值金额 */}
            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>充值{unit}数</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        {...field}
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder={`请输入充值${unit}数`}
                        className="pr-12"
                      />
                      <div className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                        {unit}
                      </div>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 备注 */}
            <FormField
              control={form.control}
              name="remark"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>备注（可选）</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="请输入充值备注..."
                      rows={3}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type="button" variant="outline" onClick={handleCancel}>
                取消
              </Button>
              <Button 
                type="submit"
                disabled={rechargeMutation.isPending}
              >
                {rechargeMutation.isPending ? '充值中...' : '确认充值'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
