'use client';

import { useState } from 'react';
import { toast } from 'sonner';
import { CreditCard, Plus } from 'lucide-react';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { DataStateWrapper } from '@/components/ui/loading-states';
import { useCardTemplateList } from '@/hooks/use-card-templates';
import { useCreateMemberCard } from '@/hooks/use-member-cards';
import { CardType, CardTypeNames } from '@/types/api/card-template';

interface CreateMemberCardDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  memberId: number;
}

export function CreateMemberCardDialog({
  open,
  onOpenChange,
  memberId,
}: CreateMemberCardDialogProps) {
  const [selectedTemplateId, setSelectedTemplateId] = useState<string>('');

  // 获取会员卡模板列表
  const {
    data: templates = [],
    isLoading: isLoadingTemplates,
  } = useCardTemplateList();

  // 创建会员卡
  const createMemberCardMutation = useCreateMemberCard();

  // 获取选中的模板
  const selectedTemplate = templates.find(t => t.id.toString() === selectedTemplateId);

  // 处理创建
  const handleCreate = async () => {
    if (!selectedTemplateId) {
      toast.error('请选择会员卡模板');
      return;
    }

    try {
      await createMemberCardMutation.mutateAsync({
        member_id: memberId,
        template_id: parseInt(selectedTemplateId),
      });
      
      toast.success('会员卡创建成功');
      onOpenChange(false);
      setSelectedTemplateId('');
    } catch (error) {
      toast.error('创建失败');
    }
  };

  // 处理取消
  const handleCancel = () => {
    onOpenChange(false);
    setSelectedTemplateId('');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            新增会员卡
          </DialogTitle>
          <DialogDescription>
            为该会员选择并创建新的会员卡
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* 模板选择 */}
          <div className="space-y-2">
            <Label>选择会员卡模板</Label>
            <DataStateWrapper
              isLoading={isLoadingTemplates}
              isEmpty={templates.length === 0}
              emptyTitle="暂无可用模板"
              emptyDescription="请先创建会员卡模板"
            >
              <Select value={selectedTemplateId} onValueChange={setSelectedTemplateId}>
                <SelectTrigger>
                  <SelectValue placeholder="请选择会员卡模板" />
                </SelectTrigger>
                <SelectContent>
                  {templates
                    .filter(template => template.is_active) // 只显示启用的模板
                    .map(template => (
                      <SelectItem key={template.id} value={template.id.toString()}>
                        <div className="flex items-center gap-2">
                          <span>{template.name}</span>
                          <Badge variant="outline" className="text-xs">
                            {CardTypeNames[template.card_type as CardType]}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </DataStateWrapper>
          </div>

          {/* 模板预览 */}
          {selectedTemplate && (
            <Card className="border-dashed">
              <CardContent className="p-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-sm">模板预览</h4>
                    <Badge variant="outline">
                      {CardTypeNames[selectedTemplate.card_type as CardType]}
                    </Badge>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-3 text-sm">
                    <div>
                      <Label className="text-muted-foreground">售价</Label>
                      <p className="font-mono">¥{selectedTemplate.sale_price.toFixed(2)}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">可用余额/次数</Label>
                      <p className="font-mono">
                        {selectedTemplate.available_balance === null ? '不限' : selectedTemplate.available_balance}
                        {(selectedTemplate.card_type === CardType.TIMES_LIMITED || 
                          selectedTemplate.card_type === CardType.TIMES_UNLIMITED) ? ' 次' : ' 元'}
                      </p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">有效期</Label>
                      <p className="font-mono">
                        {selectedTemplate.validity_days ? `${selectedTemplate.validity_days}天` : '永久有效'}
                      </p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">代理专售</Label>
                      <p>{selectedTemplate.is_agent_exclusive ? '是' : '否'}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleCancel}>
            取消
          </Button>
          <Button 
            onClick={handleCreate}
            disabled={!selectedTemplateId || createMemberCardMutation.isPending}
          >
            {createMemberCardMutation.isPending ? (
              <>创建中...</>
            ) : (
              <>
                <Plus className="h-4 w-4 mr-2" />
                创建会员卡
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
