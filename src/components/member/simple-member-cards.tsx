'use client';

import { useState } from 'react';
import { CreditCard, Clock, DollarSign, Plus } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// 模拟会员卡数据类型
interface SimpleMemberCard {
  id: number;
  card_type: string;
  balance: number;
  status: string;
  card_number?: string;
  total_recharged: number;
  total_consumed: number;
  expires_at?: string;
  last_used_at?: string;
}

interface SimpleMemberCardsProps {
  memberId: number;
}

// 模拟会员卡数据
const mockMemberCards: SimpleMemberCard[] = [
  {
    id: 1,
    card_type: 'times_limited',
    balance: 15,
    status: 'active',
    card_number: 'TC001',
    total_recharged: 2000,
    total_consumed: 500,
    expires_at: '2024-12-31',
    last_used_at: '2024-01-15',
  },
  {
    id: 2,
    card_type: 'value_limited',
    balance: 1250.50,
    status: 'active',
    card_number: 'VC002',
    total_recharged: 3000,
    total_consumed: 1749.50,
    expires_at: '2024-06-30',
    last_used_at: '2024-01-10',
  },
  {
    id: 3,
    card_type: 'times_unlimited',
    balance: 999,
    status: 'frozen',
    card_number: 'TU003',
    total_recharged: 5000,
    total_consumed: 2000,
    last_used_at: '2023-12-20',
  },
];

export function SimpleMemberCards({ memberId }: SimpleMemberCardsProps) {
  const [cards] = useState<SimpleMemberCard[]>(mockMemberCards);

  // 卡片类型名称映射
  const cardTypeNames: Record<string, string> = {
    times_limited: '限次卡',
    times_unlimited: '无限次卡',
    value_limited: '储值卡',
    value_unlimited: '无限额卡',
  };

  // 状态名称映射
  const statusNames: Record<string, string> = {
    active: '正常',
    frozen: '冻结',
    expired: '过期',
    cancelled: '注销',
  };

  // 获取状态变体
  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'active':
        return 'default';
      case 'frozen':
        return 'secondary';
      case 'expired':
        return 'destructive';
      case 'cancelled':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  // 获取卡片类型图标
  const getCardTypeIcon = (cardType: string) => {
    switch (cardType) {
      case 'times_limited':
      case 'times_unlimited':
        return <Clock className="h-4 w-4" />;
      case 'value_limited':
      case 'value_unlimited':
        return <DollarSign className="h-4 w-4" />;
      default:
        return <CreditCard className="h-4 w-4" />;
    }
  };

  // 格式化余额显示
  const formatBalance = (balance: number, cardType: string) => {
    const isTimesCard = cardType === 'times_limited' || cardType === 'times_unlimited';
    return isTimesCard ? `${balance} 次` : `¥${balance.toFixed(2)}`;
  };

  // 格式化日期
  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  return (
    <div className="space-y-4">
      {/* 头部操作栏 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h3 className="text-lg font-medium">会员卡管理</h3>
          <Badge variant="outline" className="text-xs">
            {cards.length} 张
          </Badge>
        </div>
        
        <Button size="sm" onClick={() => alert('创建会员卡功能开发中...')}>
          <Plus className="mr-1.5 h-3.5 w-3.5" />
          新增会员卡
        </Button>
      </div>

      {/* 会员卡列表 */}
      {cards.length === 0 ? (
        <div className="text-center py-12">
          <CreditCard className="mx-auto h-12 w-12 text-muted-foreground" />
          <h3 className="mt-4 text-lg font-medium">还没有会员卡</h3>
          <p className="mt-2 text-muted-foreground">
            该会员还没有任何会员卡，点击上方按钮创建第一张卡片
          </p>
        </div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {cards.map((card) => (
            <Card key={card.id} className="relative">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getCardTypeIcon(card.card_type)}
                    <CardTitle className="text-sm font-medium">
                      {cardTypeNames[card.card_type] || card.card_type}
                    </CardTitle>
                  </div>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="h-8 px-2 text-xs"
                    onClick={() => alert('会员卡操作功能开发中...')}
                  >
                    操作
                  </Button>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={getStatusVariant(card.status)} className="text-xs">
                    {statusNames[card.status] || card.status}
                  </Badge>
                  {card.card_number && (
                    <span className="text-xs text-muted-foreground">
                      {card.card_number}
                    </span>
                  )}
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-3">
                  <div>
                    <p className="text-xs text-muted-foreground">当前余额</p>
                    <p className="text-lg font-semibold">
                      {formatBalance(card.balance, card.card_type)}
                    </p>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-3 text-xs">
                    <div>
                      <p className="text-muted-foreground">总充值</p>
                      <p className="font-medium">¥{card.total_recharged.toFixed(2)}</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">总消费</p>
                      <p className="font-medium">¥{card.total_consumed.toFixed(2)}</p>
                    </div>
                  </div>
                  
                  {card.expires_at && (
                    <div>
                      <p className="text-xs text-muted-foreground">有效期至</p>
                      <p className="text-xs font-medium">
                        {formatDate(card.expires_at)}
                      </p>
                    </div>
                  )}
                  
                  {card.last_used_at && (
                    <div>
                      <p className="text-xs text-muted-foreground">最后使用</p>
                      <p className="text-xs">
                        {formatDate(card.last_used_at)}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* 操作记录预览 */}
      <div className="mt-8">
        <h4 className="text-md font-medium mb-4">最近操作记录</h4>
        <div className="space-y-2">
          {[
            { type: '充值', amount: '+¥500.00', time: '2024-01-15 14:30', card: '储值卡 VC002' },
            { type: '消费', amount: '-5次', time: '2024-01-14 10:15', card: '限次卡 TC001' },
            { type: '冻结', amount: '', time: '2024-01-13 16:45', card: '无限次卡 TU003' },
          ].map((record, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
              <div className="flex items-center gap-3">
                <Badge variant="outline" className="text-xs">
                  {record.type}
                </Badge>
                <span className="text-sm">{record.card}</span>
              </div>
              <div className="text-right">
                {record.amount && (
                  <p className={`text-sm font-medium ${
                    record.amount.startsWith('+') ? 'text-green-600' : 
                    record.amount.startsWith('-') ? 'text-red-600' : ''
                  }`}>
                    {record.amount}
                  </p>
                )}
                <p className="text-xs text-muted-foreground">{record.time}</p>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-4 text-center">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => alert('查看更多记录功能开发中...')}
          >
            查看更多记录
          </Button>
        </div>
      </div>
    </div>
  );
}
