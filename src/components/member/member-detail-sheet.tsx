'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Edit, User, Phone, Mail, Calendar, MapPin, Tag } from 'lucide-react';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { MemberCardList } from './member-card-list';
import { MemberCardOperations } from './member-card-operations';
import { MemberCardHistoryTabs } from './member-card-history-tabs';
import { useMemberDetail } from '@/hooks/use-members';
import { useMemberCards } from '@/hooks/use-member-cards';
import type { MemberRead, MemberCardRead } from '@/types/api';
import { formatDate } from '@/lib/utils';
import { ROUTES } from '@/constants/routes';

interface MemberDetailSheetProps {
  memberId: number | null;
  onClose: () => void;
}

export function MemberDetailSheet({ memberId, onClose }: MemberDetailSheetProps) {
  const router = useRouter();
  const [selectedCard, setSelectedCard] = useState<MemberCardRead | undefined>();
  const [operationType, setOperationType] = useState<
    'create' | 'recharge' | 'deduct' | 'freeze' | 'unfreeze' | 'cancel' | null
  >(null);

  // 获取会员详情
  const { data: memberData, isLoading: isLoadingMember } = useMemberDetail(
    memberId?.toString() || ''
  );

  // 获取会员卡列表
  const { data: memberCardsData } = useMemberCards(memberId || 0);

  const member = memberData?.data;
  const memberCards = memberCardsData?.data || [];

  // 处理编辑会员信息
  const handleEditMember = () => {
    if (member) {
      router.push(`${ROUTES.MEMBERS}/${member.id}/edit`);
      onClose();
    }
  };

  // 处理会员卡操作
  const handleCardOperation = (
    type: 'create' | 'recharge' | 'deduct' | 'freeze' | 'unfreeze' | 'cancel',
    card?: MemberCardRead
  ) => {
    setSelectedCard(card);
    setOperationType(type);
  };

  // 关闭操作对话框
  const handleCloseOperation = () => {
    setSelectedCard(undefined);
    setOperationType(null);
  };

  // 获取会员状态颜色
  const getMemberStatusVariant = (status: string) => {
    switch (status) {
      case 'active':
        return 'default';
      case 'silent':
        return 'secondary';
      case 'frozen':
        return 'destructive';
      case 'cancelled':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  // 获取会员类型颜色
  const getMemberTypeVariant = (type: string) => {
    switch (type) {
      case 'vip':
        return 'default';
      case 'formal':
        return 'secondary';
      case 'trial':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  if (!memberId) return null;

  return (
    <>
      <Sheet open={!!memberId} onOpenChange={() => onClose()}>
        <SheetContent className="w-full sm:max-w-2xl lg:max-w-4xl overflow-y-auto">
          <SheetHeader className="space-y-3">
            <div className="flex items-center justify-between">
              <SheetTitle className="text-xl">会员详情</SheetTitle>
              <Button variant="outline" size="sm" onClick={handleEditMember}>
                <Edit className="mr-1.5 h-3.5 w-3.5" />
                编辑会员信息
              </Button>
            </div>
            <SheetDescription>
              查看和管理会员的详细信息及会员卡
            </SheetDescription>
          </SheetHeader>

          {isLoadingMember ? (
            <div className="flex items-center justify-center h-64">
              <p className="text-muted-foreground">加载中...</p>
            </div>
          ) : member ? (
            <div className="space-y-6 mt-6">
              {/* 会员基本信息 */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">基本信息</h3>
                
                <div className="flex items-start gap-4 p-4 bg-muted/30 rounded-lg">
                  <Avatar className="h-16 w-16">
                    <AvatarImage src={member.avatar_url || member.wechat_avatar || ''} />
                    <AvatarFallback>
                      <User className="h-8 w-8" />
                    </AvatarFallback>
                  </Avatar>
                  
                  <div className="flex-1 space-y-3">
                    <div className="flex items-center gap-3">
                      <h4 className="text-lg font-semibold">{member.name}</h4>
                      <Badge variant={getMemberStatusVariant(member.member_status)}>
                        {member.member_status === 'active' ? '正常' : 
                         member.member_status === 'silent' ? '静默' :
                         member.member_status === 'frozen' ? '冻结' : '注销'}
                      </Badge>
                      <Badge variant={getMemberTypeVariant(member.member_type)}>
                        {member.member_type === 'vip' ? 'VIP会员' :
                         member.member_type === 'formal' ? '正式会员' : '试用会员'}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <span>{member.phone}</span>
                      </div>
                      
                      {member.email && (
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4 text-muted-foreground" />
                          <span>{member.email}</span>
                        </div>
                      )}
                      
                      {member.birthday && (
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span>生日：{formatDate(member.birthday)}</span>
                        </div>
                      )}
                      
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span>注册：{formatDate(member.registered_at)}</span>
                      </div>
                      
                      {(member.city || member.province) && (
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4 text-muted-foreground" />
                          <span>{[member.province, member.city].filter(Boolean).join(' ')}</span>
                        </div>
                      )}
                      
                      {member.agent_name && (
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-muted-foreground" />
                          <span>代理：{member.agent_name}</span>
                        </div>
                      )}
                    </div>
                    
                    {member.tags && member.tags.length > 0 && (
                      <div className="flex items-center gap-2">
                        <Tag className="h-4 w-4 text-muted-foreground" />
                        <div className="flex flex-wrap gap-1">
                          {member.tags.map((tag, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* 统计信息 */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-3 bg-muted/30 rounded-lg">
                    <p className="text-2xl font-bold text-primary">{member.total_classes}</p>
                    <p className="text-xs text-muted-foreground">总课程数</p>
                  </div>
                  <div className="text-center p-3 bg-muted/30 rounded-lg">
                    <p className="text-2xl font-bold text-green-600">{member.completed_classes}</p>
                    <p className="text-xs text-muted-foreground">已完成</p>
                  </div>
                  <div className="text-center p-3 bg-muted/30 rounded-lg">
                    <p className="text-2xl font-bold text-blue-600">¥{member.total_spent.toFixed(2)}</p>
                    <p className="text-xs text-muted-foreground">总消费</p>
                  </div>
                  <div className="text-center p-3 bg-muted/30 rounded-lg">
                    <p className="text-2xl font-bold text-purple-600">¥{member.total_recharged.toFixed(2)}</p>
                    <p className="text-xs text-muted-foreground">总充值</p>
                  </div>
                </div>
              </div>

              <Separator />

              {/* 会员卡管理 */}
              <MemberCardList
                memberId={member.id}
                onCreateCard={() => handleCardOperation('create')}
                onRecharge={(card) => handleCardOperation('recharge', card)}
                onDeduct={(card) => handleCardOperation('deduct', card)}
                onFreeze={(card) => handleCardOperation('freeze', card)}
                onUnfreeze={(card) => handleCardOperation('unfreeze', card)}
                onCancel={(card) => handleCardOperation('cancel', card)}
              />

              <Separator />

              {/* 历史记录 */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">历史记录</h3>
                <MemberCardHistoryTabs
                  memberId={member.id}
                  memberCards={memberCards.map(card => ({
                    id: card.id,
                    card_type: card.card_type,
                  }))}
                />
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center h-64">
              <p className="text-muted-foreground">未找到会员信息</p>
            </div>
          )}
        </SheetContent>
      </Sheet>

      {/* 会员卡操作对话框 */}
      <MemberCardOperations
        memberId={memberId}
        selectedCard={selectedCard}
        operationType={operationType}
        onClose={handleCloseOperation}
      />
    </>
  );
}
