'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import {
  useCreateMemberCard,
  useRechargeMemberCard,
  useDeductMemberCard,
  useFreezeMemberCard,
  useUnfreezeMemberCard,
  useCancelMemberCard,
} from '@/hooks/use-member-cards';
import { useCardTemplates } from '@/hooks/use-card-templates';
import type { MemberCardRead, PaymentMethod } from '@/types/api';
import { CardTypeNames, CardStatusNames } from '@/types/api';

// 创建会员卡表单验证
const createCardSchema = z.object({
  template_id: z.number().min(1, '请选择会员卡模板'),
});

// 充值表单验证
const rechargeSchema = z.object({
  amount: z.number().min(0.01, '充值金额必须大于0'),
  bonus_amount: z.number().min(0, '赠送金额不能为负数').optional(),
  payment_method: z.enum(['cash', 'wechat', 'alipay', 'bank_card', 'other'] as const),
  notes: z.string().optional(),
});

// 扣费表单验证
const deductSchema = z.object({
  amount: z.number().min(0.01, '扣费金额必须大于0'),
  reason: z.string().min(1, '请填写扣费原因'),
  notes: z.string().optional(),
});

// 冻结表单验证
const freezeSchema = z.object({
  reason: z.string().min(1, '请填写冻结原因'),
});

// 注销表单验证
const cancelSchema = z.object({
  reason: z.string().min(1, '请填写注销原因'),
});

type CreateCardFormData = z.infer<typeof createCardSchema>;
type RechargeFormData = z.infer<typeof rechargeSchema>;
type DeductFormData = z.infer<typeof deductSchema>;
type FreezeFormData = z.infer<typeof freezeSchema>;
type CancelFormData = z.infer<typeof cancelSchema>;

interface MemberCardOperationsProps {
  memberId: number;
  selectedCard?: MemberCardRead;
  operationType: 'create' | 'recharge' | 'deduct' | 'freeze' | 'unfreeze' | 'cancel' | null;
  onClose: () => void;
}

export function MemberCardOperations({
  memberId,
  selectedCard,
  operationType,
  onClose,
}: MemberCardOperationsProps) {
  const { data: templates } = useCardTemplates();
  
  const createCardMutation = useCreateMemberCard();
  const rechargeMutation = useRechargeMemberCard();
  const deductMutation = useDeductMemberCard();
  const freezeMutation = useFreezeMemberCard();
  const unfreezeMutation = useUnfreezeMemberCard();
  const cancelMutation = useCancelMemberCard();

  // 支付方式选项
  const paymentMethods = [
    { value: 'cash', label: '现金' },
    { value: 'wechat', label: '微信支付' },
    { value: 'alipay', label: '支付宝' },
    { value: 'bank_card', label: '银行卡' },
    { value: 'other', label: '其他' },
  ];

  // 创建会员卡表单
  const createForm = useForm<CreateCardFormData>({
    resolver: zodResolver(createCardSchema),
  });

  // 充值表单
  const rechargeForm = useForm<RechargeFormData>({
    resolver: zodResolver(rechargeSchema),
    defaultValues: {
      bonus_amount: 0,
      payment_method: 'cash',
    },
  });

  // 扣费表单
  const deductForm = useForm<DeductFormData>({
    resolver: zodResolver(deductSchema),
  });

  // 冻结表单
  const freezeForm = useForm<FreezeFormData>({
    resolver: zodResolver(freezeSchema),
  });

  // 注销表单
  const cancelForm = useForm<CancelFormData>({
    resolver: zodResolver(cancelSchema),
  });

  // 处理创建会员卡
  const handleCreateCard = async (data: CreateCardFormData) => {
    await createCardMutation.mutateAsync({
      member_id: memberId,
      template_id: data.template_id,
    });
    onClose();
  };

  // 处理充值
  const handleRecharge = async (data: RechargeFormData) => {
    if (!selectedCard) return;
    await rechargeMutation.mutateAsync({
      cardId: selectedCard.id,
      data: {
        amount: data.amount,
        bonus_amount: data.bonus_amount || 0,
        payment_method: data.payment_method as PaymentMethod,
        notes: data.notes,
      },
    });
    onClose();
  };

  // 处理扣费
  const handleDeduct = async (data: DeductFormData) => {
    if (!selectedCard) return;
    await deductMutation.mutateAsync({
      cardId: selectedCard.id,
      data: {
        amount: data.amount,
        reason: data.reason,
        notes: data.notes,
      },
    });
    onClose();
  };

  // 处理冻结
  const handleFreeze = async (data: FreezeFormData) => {
    if (!selectedCard) return;
    await freezeMutation.mutateAsync({
      cardId: selectedCard.id,
      reason: data.reason,
    });
    onClose();
  };

  // 处理解冻
  const handleUnfreeze = async () => {
    if (!selectedCard) return;
    await unfreezeMutation.mutateAsync(selectedCard.id);
    onClose();
  };

  // 处理注销
  const handleCancel = async (data: CancelFormData) => {
    if (!selectedCard) return;
    await cancelMutation.mutateAsync({
      cardId: selectedCard.id,
      reason: data.reason,
    });
    onClose();
  };

  // 获取对话框标题和描述
  const getDialogInfo = () => {
    switch (operationType) {
      case 'create':
        return {
          title: '创建会员卡',
          description: '为会员创建新的会员卡',
        };
      case 'recharge':
        return {
          title: '会员卡充值',
          description: `为 ${CardTypeNames[selectedCard?.card_type || 'value_limited']} 充值`,
        };
      case 'deduct':
        return {
          title: '会员卡扣费',
          description: `从 ${CardTypeNames[selectedCard?.card_type || 'value_limited']} 扣费`,
        };
      case 'freeze':
        return {
          title: '冻结会员卡',
          description: `冻结 ${CardTypeNames[selectedCard?.card_type || 'value_limited']}`,
        };
      case 'unfreeze':
        return {
          title: '解冻会员卡',
          description: `解冻 ${CardTypeNames[selectedCard?.card_type || 'value_limited']}`,
        };
      case 'cancel':
        return {
          title: '注销会员卡',
          description: `注销 ${CardTypeNames[selectedCard?.card_type || 'value_limited']}`,
        };
      default:
        return { title: '', description: '' };
    }
  };

  const { title, description } = getDialogInfo();

  // 渲染表单内容
  const renderFormContent = () => {
    switch (operationType) {
      case 'create':
        return (
          <Form {...createForm}>
            <form onSubmit={createForm.handleSubmit(handleCreateCard)} className="space-y-4">
              <FormField
                control={createForm.control}
                name="template_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>选择会员卡模板</FormLabel>
                    <Select onValueChange={(value) => field.onChange(Number(value))}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="请选择会员卡模板" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {templates?.data?.map((template) => (
                          <SelectItem key={template.id} value={template.id.toString()}>
                            <div className="flex items-center gap-2">
                              <span>{template.name}</span>
                              <Badge variant="outline" className="text-xs">
                                {CardTypeNames[template.card_type]}
                              </Badge>
                              <span className="text-xs text-muted-foreground">
                                ¥{template.sale_price.toFixed(2)}
                              </span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <DialogFooter>
                <Button type="button" variant="outline" onClick={onClose}>
                  取消
                </Button>
                <Button type="submit" disabled={createCardMutation.isPending}>
                  {createCardMutation.isPending ? '创建中...' : '创建'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        );

      case 'recharge':
        return (
          <Form {...rechargeForm}>
            <form onSubmit={rechargeForm.handleSubmit(handleRecharge)} className="space-y-4">
              {selectedCard && (
                <div className="p-3 bg-muted rounded-md">
                  <div className="flex items-center justify-between text-sm">
                    <span>当前余额：</span>
                    <span className="font-medium">
                      {selectedCard.card_type.includes('times') 
                        ? `${selectedCard.balance} 次`
                        : `¥${selectedCard.balance.toFixed(2)}`
                      }
                    </span>
                  </div>
                </div>
              )}
              
              <FormField
                control={rechargeForm.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>充值金额</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="请输入充值金额"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={rechargeForm.control}
                name="bonus_amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>赠送金额（可选）</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="请输入赠送金额"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={rechargeForm.control}
                name="payment_method"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>支付方式</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="请选择支付方式" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {paymentMethods.map((method) => (
                          <SelectItem key={method.value} value={method.value}>
                            {method.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={rechargeForm.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>备注（可选）</FormLabel>
                    <FormControl>
                      <Textarea placeholder="请输入备注信息" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button type="button" variant="outline" onClick={onClose}>
                  取消
                </Button>
                <Button type="submit" disabled={rechargeMutation.isPending}>
                  {rechargeMutation.isPending ? '充值中...' : '确认充值'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        );

      case 'deduct':
        return (
          <Form {...deductForm}>
            <form onSubmit={deductForm.handleSubmit(handleDeduct)} className="space-y-4">
              {selectedCard && (
                <div className="p-3 bg-muted rounded-md">
                  <div className="flex items-center justify-between text-sm">
                    <span>当前余额：</span>
                    <span className="font-medium">
                      {selectedCard.card_type.includes('times') 
                        ? `${selectedCard.balance} 次`
                        : `¥${selectedCard.balance.toFixed(2)}`
                      }
                    </span>
                  </div>
                </div>
              )}
              
              <FormField
                control={deductForm.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>扣费金额</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="请输入扣费金额"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={deductForm.control}
                name="reason"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>扣费原因</FormLabel>
                    <FormControl>
                      <Input placeholder="请输入扣费原因" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={deductForm.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>备注（可选）</FormLabel>
                    <FormControl>
                      <Textarea placeholder="请输入备注信息" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button type="button" variant="outline" onClick={onClose}>
                  取消
                </Button>
                <Button type="submit" variant="destructive" disabled={deductMutation.isPending}>
                  {deductMutation.isPending ? '扣费中...' : '确认扣费'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        );

      case 'freeze':
        return (
          <Form {...freezeForm}>
            <form onSubmit={freezeForm.handleSubmit(handleFreeze)} className="space-y-4">
              <FormField
                control={freezeForm.control}
                name="reason"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>冻结原因</FormLabel>
                    <FormControl>
                      <Textarea placeholder="请输入冻结原因" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button type="button" variant="outline" onClick={onClose}>
                  取消
                </Button>
                <Button type="submit" variant="destructive" disabled={freezeMutation.isPending}>
                  {freezeMutation.isPending ? '冻结中...' : '确认冻结'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        );

      case 'unfreeze':
        return (
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              确认要解冻这张会员卡吗？解冻后会员卡将恢复正常使用。
            </p>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                取消
              </Button>
              <Button onClick={handleUnfreeze} disabled={unfreezeMutation.isPending}>
                {unfreezeMutation.isPending ? '解冻中...' : '确认解冻'}
              </Button>
            </DialogFooter>
          </div>
        );

      case 'cancel':
        return (
          <Form {...cancelForm}>
            <form onSubmit={cancelForm.handleSubmit(handleCancel)} className="space-y-4">
              <p className="text-sm text-muted-foreground">
                注销后的会员卡将无法恢复，请谨慎操作。
              </p>
              
              <FormField
                control={cancelForm.control}
                name="reason"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>注销原因</FormLabel>
                    <FormControl>
                      <Textarea placeholder="请输入注销原因" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button type="button" variant="outline" onClick={onClose}>
                  取消
                </Button>
                <Button type="submit" variant="destructive" disabled={cancelMutation.isPending}>
                  {cancelMutation.isPending ? '注销中...' : '确认注销'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={!!operationType} onOpenChange={() => onClose()}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        {renderFormContent()}
      </DialogContent>
    </Dialog>
  );
}
