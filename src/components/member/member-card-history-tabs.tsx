'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { CompactDataTable } from '@/components/tables';
import { DataStateWrapper } from '@/components/ui/loading-states';
import { useMemberCardOperations } from '@/hooks/use-member-cards';
import type { MemberCardOperationRead, MemberCardOperationType } from '@/types/api';
import { MemberCardOperationTypeNames } from '@/types/api';
import { formatDate, cn } from '@/lib/utils';

interface MemberCardHistoryTabsProps {
  memberId: number;
  memberCards: Array<{ id: number; card_type: string }>;
}

export function MemberCardHistoryTabs({ memberId, memberCards }: MemberCardHistoryTabsProps) {
  const [activeTab, setActiveTab] = useState('operations');

  // 获取会员卡操作记录
  const {
    data: operationsData,
    isLoading: isLoadingOperations,
    error: operationsError,
  } = useMemberCardOperations({
    member_id: memberId,
    page: 1,
    size: 50,
    sort_by: 'created_at',
    sort_order: 'desc',
  });

  // 获取充值记录（筛选充值类型的操作）
  const {
    data: rechargeData,
    isLoading: isLoadingRecharge,
    error: rechargeError,
  } = useMemberCardOperations({
    member_id: memberId,
    operation_type: 'recharge' as MemberCardOperationType,
    page: 1,
    size: 50,
    sort_by: 'created_at',
    sort_order: 'desc',
  });

  const operations = operationsData?.data || [];
  const rechargeOperations = rechargeData?.data || [];

  // 获取操作类型的颜色变体
  const getOperationVariant = (operationType: MemberCardOperationType) => {
    switch (operationType) {
      case 'recharge':
        return 'success';
      case 'manual_deduction':
      case 'direct_booking':
      case 'fixed_schedule_booking':
      case 'admin_booking':
        return 'warning';
      case 'freeze_card':
      case 'cancel_card':
        return 'destructive';
      case 'unfreeze_card':
      case 'create_card':
        return 'default';
      default:
        return 'secondary';
    }
  };

  // 格式化金额变化
  const formatAmountChange = (operation: MemberCardOperationRead) => {
    if (operation.amount_change === null || operation.amount_change === undefined) {
      return '-';
    }
    
    const amount = operation.amount_change;
    const isTimesCard = operation.member_card_name?.includes('次') || false;
    const unit = isTimesCard ? '次' : '元';
    
    if (amount > 0) {
      return <span className="text-green-600">+{amount}{unit}</span>;
    } else if (amount < 0) {
      return <span className="text-red-600">{amount}{unit}</span>;
    }
    return `${amount}${unit}`;
  };

  // 操作记录表格列定义
  const operationColumns = [
    {
      accessorKey: 'operation_type',
      header: '操作类型',
      cell: ({ row }: { row: any }) => (
        <Badge variant={getOperationVariant(row.original.operation_type)} className="text-xs">
          {MemberCardOperationTypeNames[row.original.operation_type] || row.original.operation_type}
        </Badge>
      ),
    },
    {
      accessorKey: 'operation_description',
      header: '操作描述',
      cell: ({ row }: { row: any }) => (
        <span className="text-sm">{row.original.operation_description}</span>
      ),
    },
    {
      accessorKey: 'amount_change',
      header: '金额变化',
      cell: ({ row }: { row: any }) => formatAmountChange(row.original),
    },
    {
      accessorKey: 'balance_after',
      header: '操作后余额',
      cell: ({ row }: { row: any }) => {
        const balance = row.original.balance_after;
        if (balance === null || balance === undefined) return '-';
        const isTimesCard = row.original.member_card_name?.includes('次') || false;
        return `${balance}${isTimesCard ? '次' : '元'}`;
      },
      meta: { className: 'hidden md:table-cell' },
    },
    {
      accessorKey: 'operator_name',
      header: '操作人',
      cell: ({ row }: { row: any }) => (
        <span className="text-sm">
          {row.original.operator_name || '系统'}
        </span>
      ),
      meta: { className: 'hidden lg:table-cell' },
    },
    {
      accessorKey: 'created_at',
      header: '操作时间',
      cell: ({ row }: { row: any }) => (
        <span className="text-sm text-muted-foreground">
          {formatDate(row.original.created_at)}
        </span>
      ),
    },
  ];

  // 充值记录表格列定义
  const rechargeColumns = [
    {
      accessorKey: 'member_card_name',
      header: '会员卡',
      cell: ({ row }: { row: any }) => (
        <span className="text-sm font-medium">
          {row.original.member_card_name || '未知卡片'}
        </span>
      ),
    },
    {
      accessorKey: 'actual_amount',
      header: '实收金额',
      cell: ({ row }: { row: any }) => {
        const amount = row.original.actual_amount || row.original.amount_change || 0;
        return <span className="font-medium">¥{amount.toFixed(2)}</span>;
      },
    },
    {
      accessorKey: 'bonus_amount',
      header: '赠送金额',
      cell: ({ row }: { row: any }) => {
        const bonus = row.original.bonus_amount || 0;
        return bonus > 0 ? (
          <span className="text-green-600">¥{bonus.toFixed(2)}</span>
        ) : (
          <span className="text-muted-foreground">-</span>
        );
      },
    },
    {
      accessorKey: 'payment_method',
      header: '支付方式',
      cell: ({ row }: { row: any }) => {
        const paymentMethods: Record<string, string> = {
          cash: '现金',
          wechat: '微信支付',
          alipay: '支付宝',
          bank_card: '银行卡',
          other: '其他',
        };
        return (
          <span className="text-sm">
            {paymentMethods[row.original.payment_method] || row.original.payment_method || '-'}
          </span>
        );
      },
      meta: { className: 'hidden md:table-cell' },
    },
    {
      accessorKey: 'operator_name',
      header: '操作人',
      cell: ({ row }: { row: any }) => (
        <span className="text-sm">
          {row.original.operator_name || '系统'}
        </span>
      ),
      meta: { className: 'hidden lg:table-cell' },
    },
    {
      accessorKey: 'created_at',
      header: '充值时间',
      cell: ({ row }: { row: any }) => (
        <span className="text-sm text-muted-foreground">
          {formatDate(row.original.created_at)}
        </span>
      ),
    },
  ];

  // 移动端卡片视图
  const MobileOperationCard = ({ operation }: { operation: MemberCardOperationRead }) => (
    <Card className="mb-3">
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-2">
          <Badge variant={getOperationVariant(operation.operation_type)} className="text-xs">
            {MemberCardOperationTypeNames[operation.operation_type] || operation.operation_type}
          </Badge>
          <span className="text-xs text-muted-foreground">
            {formatDate(operation.created_at)}
          </span>
        </div>
        
        <p className="text-sm font-medium mb-2">{operation.operation_description}</p>
        
        <div className="grid grid-cols-2 gap-3 text-xs">
          <div>
            <span className="text-muted-foreground">金额变化</span>
            <div className="font-medium">{formatAmountChange(operation)}</div>
          </div>
          
          {operation.balance_after !== null && operation.balance_after !== undefined && (
            <div>
              <span className="text-muted-foreground">操作后余额</span>
              <div className="font-medium">
                {operation.balance_after}
                {operation.member_card_name?.includes('次') ? '次' : '元'}
              </div>
            </div>
          )}
        </div>
        
        {operation.operator_name && (
          <div className="mt-2 text-xs text-muted-foreground">
            操作人：{operation.operator_name}
          </div>
        )}
        
        {operation.notes && (
          <div className="mt-2 text-xs text-muted-foreground">
            备注：{operation.notes}
          </div>
        )}
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-4">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="operations" className="text-sm">
            操作记录
            {operations.length > 0 && (
              <Badge variant="secondary" className="ml-2 text-xs">
                {operations.length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="recharge" className="text-sm">
            充值记录
            {rechargeOperations.length > 0 && (
              <Badge variant="secondary" className="ml-2 text-xs">
                {rechargeOperations.length}
              </Badge>
            )}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="operations" className="space-y-4">
          <DataStateWrapper
            isLoading={isLoadingOperations}
            isEmpty={operations.length === 0}
            isError={!!operationsError}
            emptyTitle="暂无操作记录"
            emptyDescription="该会员还没有任何会员卡操作记录"
          >
            {/* 桌面端表格视图 */}
            <div className="hidden sm:block">
              <CompactDataTable
                columns={operationColumns}
                data={operations}
                isLoading={false}
              />
            </div>
            
            {/* 移动端卡片视图 */}
            <div className="block sm:hidden">
              {operations.map((operation) => (
                <MobileOperationCard key={operation.id} operation={operation} />
              ))}
            </div>
          </DataStateWrapper>
        </TabsContent>

        <TabsContent value="recharge" className="space-y-4">
          <DataStateWrapper
            isLoading={isLoadingRecharge}
            isEmpty={rechargeOperations.length === 0}
            isError={!!rechargeError}
            emptyTitle="暂无充值记录"
            emptyDescription="该会员还没有任何充值记录"
          >
            {/* 桌面端表格视图 */}
            <div className="hidden sm:block">
              <CompactDataTable
                columns={rechargeColumns}
                data={rechargeOperations}
                isLoading={false}
              />
            </div>
            
            {/* 移动端卡片视图 */}
            <div className="block sm:hidden">
              {rechargeOperations.map((operation) => (
                <MobileOperationCard key={operation.id} operation={operation} />
              ))}
            </div>
          </DataStateWrapper>
        </TabsContent>
      </Tabs>
    </div>
  );
}
