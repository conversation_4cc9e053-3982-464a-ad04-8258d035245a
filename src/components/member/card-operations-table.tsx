'use client';

import { useState } from 'react';
import { Calendar, Filter } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { CompactDataTable } from '@/components/tables/common/compact-data-table';
import { DataStateWrapper } from '@/components/ui/loading-states';
import { useCardOperations } from '@/hooks/use-member-cards';
import { formatDate } from '@/lib/utils';
import { MemberCardOperationType } from '@/types/api';

interface CardOperationsTableProps {
  memberId: number;
}

export function CardOperationsTable({ memberId }: CardOperationsTableProps) {
  const [filters, setFilters] = useState({
    operation_type: 'all',
    date_range: '',
  });

  // 获取操作记录
  const {
    data: operations = [],
    isLoading,
    error,
  } = useMemberCardOperations(memberId);

  // 操作类型选项
  const operationTypeOptions = [
    { value: 'all', label: '全部操作' },
    { value: MemberCardOperationType.RECHARGE, label: '充值' },
    { value: MemberCardOperationType.DEDUCT, label: '扣费' },
    { value: MemberCardOperationType.FREEZE, label: '冻结' },
    { value: MemberCardOperationType.UNFREEZE, label: '解冻' },
    { value: MemberCardOperationType.CANCEL, label: '注销' },
  ];

  // 操作类型标签映射
  const getOperationTypeBadge = (type: MemberCardOperationType) => {
    const typeMap = {
      [MemberCardOperationType.RECHARGE]: { variant: 'default' as const, label: '充值' },
      [MemberCardOperationType.DEDUCT]: { variant: 'destructive' as const, label: '扣费' },
      [MemberCardOperationType.FREEZE]: { variant: 'secondary' as const, label: '冻结' },
      [MemberCardOperationType.UNFREEZE]: { variant: 'default' as const, label: '解冻' },
      [MemberCardOperationType.CANCEL]: { variant: 'outline' as const, label: '注销' },
    };
    return typeMap[type] || { variant: 'secondary' as const, label: '未知' };
  };

  // 筛选处理
  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  // 过滤数据
  const filteredOperations = operations.filter(operation => {
    if (filters.operation_type !== 'all' && operation.operation_type !== filters.operation_type) {
      return false;
    }
    return true;
  });

  // 表格列定义
  const columns = [
    {
      accessorKey: 'card_name',
      header: '会员卡',
      cell: ({ row }: any) => (
        <div className="font-medium">{row.original.card_name}</div>
      ),
    },
    {
      accessorKey: 'operation_type',
      header: '操作类型',
      cell: ({ row }: any) => {
        const typeInfo = getOperationTypeBadge(row.original.operation_type);
        return (
          <Badge variant={typeInfo.variant} className="text-xs">
            {typeInfo.label}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'amount',
      header: '金额/次数',
      cell: ({ row }: any) => {
        const operation = row.original;
        if (!operation.amount) return '-';
        
        const isPositive = operation.operation_type === MemberCardOperationType.RECHARGE;
        return (
          <span className={`font-mono ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
            {isPositive ? '+' : '-'}{Math.abs(operation.amount)}
          </span>
        );
      },
    },
    {
      accessorKey: 'balance_after',
      header: '操作后余额',
      cell: ({ row }: any) => (
        <span className="font-mono">
          {row.original.balance_after === null ? '不限' : row.original.balance_after}
        </span>
      ),
    },
    {
      accessorKey: 'operator_name',
      header: '操作人',
      cell: ({ row }: any) => (
        <div className="text-sm">{row.original.operator_name || '系统'}</div>
      ),
    },
    {
      accessorKey: 'created_at',
      header: '操作时间',
      cell: ({ row }: any) => (
        <div className="text-xs font-mono">{formatDate(row.original.created_at)}</div>
      ),
    },
    {
      accessorKey: 'remark',
      header: '备注',
      cell: ({ row }: any) => (
        <div className="text-xs text-muted-foreground max-w-32 truncate">
          {row.original.remark || '-'}
        </div>
      ),
    },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base flex items-center gap-2">
          <Calendar className="h-4 w-4" />
          会员卡操作记录
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 筛选区域 */}
        <div className="flex flex-col sm:flex-row gap-3">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-muted-foreground" />
            <Select
              value={filters.operation_type}
              onValueChange={value => handleFilterChange('operation_type', value)}
            >
              <SelectTrigger className="w-40">
                <SelectValue placeholder="操作类型" />
              </SelectTrigger>
              <SelectContent>
                {operationTypeOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* 数据表格 */}
        <DataStateWrapper
          isLoading={isLoading}
          isEmpty={filteredOperations.length === 0}
          emptyTitle="暂无操作记录"
          emptyDescription="该会员还没有任何会员卡操作记录"
        >
          <CompactDataTable
            columns={columns}
            data={filteredOperations}
            isLoading={isLoading}
          />
        </DataStateWrapper>
      </CardContent>
    </Card>
  );
}
