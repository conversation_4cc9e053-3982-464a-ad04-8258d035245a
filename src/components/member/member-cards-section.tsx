'use client';

import { useState } from 'react';
import { Plus, CreditCard, Calendar, DollarSign, Snowflake, Zap } from 'lucide-react';
import { toast } from 'sonner';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { DataStateWrapper } from '@/components/ui/loading-states';
import { CompactDataTable } from '@/components/tables/common/compact-data-table';
import {
  useMemberCardsByMember,
  useFreezeMemberCard,
  useUnfreezeMemberCard,
  useRechargeMemberCard
} from '@/hooks/use-member-cards';
import { formatDate } from '@/lib/utils';
import { CardStatus, CardType, CardTypeNames } from '@/types/api/card-template';

// 导入操作对话框组件（稍后创建）
import { CreateMemberCardDialog } from './create-member-card-dialog';
import { RechargeDialog } from './recharge-dialog';

interface MemberCardsSectionProps {
  memberId: number;
}

export function MemberCardsSection({ memberId }: MemberCardsSectionProps) {
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showRechargeDialog, setShowRechargeDialog] = useState(false);
  const [selectedCard, setSelectedCard] = useState<any>(null);

  // 获取会员卡列表
  const {
    data: memberCards = [],
    isLoading,
    error,
  } = useMemberCardsByMember(memberId.toString());

  // 状态更新
  const freezeMutation = useFreezeMemberCard();
  const unfreezeMutation = useUnfreezeMemberCard();
  const rechargeMutation = useRechargeMemberCard();

  // 状态标签映射
  const getStatusBadge = (status: CardStatus) => {
    const statusMap = {
      [CardStatus.ACTIVE]: { variant: 'default' as const, label: '正常', icon: Zap },
      [CardStatus.FROZEN]: { variant: 'secondary' as const, label: '冻结', icon: Snowflake },
      [CardStatus.EXPIRED]: { variant: 'destructive' as const, label: '过期', icon: Calendar },
      [CardStatus.CANCELLED]: { variant: 'outline' as const, label: '注销', icon: CreditCard },
    };
    return statusMap[status] || { variant: 'secondary' as const, label: '未知', icon: CreditCard };
  };

  // 处理状态切换
  const handleToggleStatus = async (cardId: number, currentStatus: CardStatus) => {
    try {
      if (currentStatus === CardStatus.ACTIVE) {
        await freezeMutation.mutateAsync({ id: cardId.toString() });
      } else if (currentStatus === CardStatus.FROZEN) {
        await unfreezeMutation.mutateAsync(cardId.toString());
      }
    } catch (error) {
      // 错误处理已在hooks中处理
    }
  };

  // 处理充值
  const handleRecharge = (card: any) => {
    setSelectedCard(card);
    setShowRechargeDialog(true);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="font-semibold text-base">会员卡信息</h3>
        <Button size="sm" onClick={() => setShowCreateDialog(true)}>
          <Plus className="h-4 w-4 mr-2" />
          新增会员卡
        </Button>
      </div>

      <DataStateWrapper
        isLoading={isLoading}
        isEmpty={memberCards.length === 0}
        emptyTitle="还没有会员卡"
        emptyDescription="点击上方按钮为该会员创建第一张会员卡"
        emptyActionLabel="新增会员卡"
        onEmptyAction={() => setShowCreateDialog(true)}
      >
        {/* 卡片布局展示 */}
        <div className="grid md:grid-cols-3 gap-4 mb-6">
          {memberCards.map((card) => {
            const statusInfo = getStatusBadge(card.status);
            const StatusIcon = statusInfo.icon;
            
            return (
              <Card key={card.id} className="relative">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-sm flex items-center gap-2">
                      <CreditCard className="h-4 w-4" />
                      {card.template_name}
                    </CardTitle>
                    <Badge variant={statusInfo.variant} className="text-xs">
                      <StatusIcon className="h-3 w-3 mr-1" />
                      {statusInfo.label}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <Label className="text-muted-foreground">余额/次数</Label>
                      <span className="font-mono font-medium">
                        {card.available_balance === null ? '不限' : card.available_balance}
                        {card.card_type === CardType.TIMES_LIMITED || card.card_type === CardType.TIMES_UNLIMITED ? ' 次' : ' 元'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <Label className="text-muted-foreground">有效期</Label>
                      <span className="font-mono text-xs">
                        {card.expiry_date ? formatDate(card.expiry_date) : '永久有效'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <Label className="text-muted-foreground">创建时间</Label>
                      <span className="font-mono text-xs">
                        {formatDate(card.created_at)}
                      </span>
                    </div>
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex gap-2 pt-2">
                    <Button
                      size="xs"
                      variant="outline"
                      onClick={() => handleRecharge(card)}
                      disabled={card.status !== CardStatus.ACTIVE}
                    >
                      <DollarSign className="h-3 w-3 mr-1" />
                      充值
                    </Button>
                    <Button
                      size="xs"
                      variant="outline"
                      onClick={() => handleToggleStatus(card.id, card.status)}
                      disabled={card.status === CardStatus.CANCELLED}
                    >
                      <Freeze className="h-3 w-3 mr-1" />
                      {card.status === CardStatus.ACTIVE ? '冻结' : '解冻'}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* 表格布局展示（可选，用于对比效果） */}
        {memberCards.length > 0 && (
          <div className="border rounded-lg">
            <div className="p-4 border-b">
              <h4 className="font-medium text-sm">会员卡列表</h4>
            </div>
            <CompactDataTable
              columns={[
                {
                  accessorKey: 'template_name',
                  header: '卡片名称',
                  cell: ({ row }) => (
                    <div className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">{row.original.template_name}</span>
                    </div>
                  ),
                },
                {
                  accessorKey: 'available_balance',
                  header: '余额/次数',
                  cell: ({ row }) => {
                    const card = row.original;
                    return (
                      <span className="font-mono">
                        {card.available_balance === null ? '不限' : card.available_balance}
                        {card.card_type === CardType.TIMES_LIMITED || card.card_type === CardType.TIMES_UNLIMITED ? ' 次' : ' 元'}
                      </span>
                    );
                  },
                },
                {
                  accessorKey: 'expiry_date',
                  header: '有效期',
                  cell: ({ row }) => (
                    <span className="font-mono text-xs">
                      {row.original.expiry_date ? formatDate(row.original.expiry_date) : '永久有效'}
                    </span>
                  ),
                },
                {
                  accessorKey: 'status',
                  header: '状态',
                  cell: ({ row }) => {
                    const statusInfo = getStatusBadge(row.original.status);
                    const StatusIcon = statusInfo.icon;
                    return (
                      <Badge variant={statusInfo.variant} className="text-xs">
                        <StatusIcon className="h-3 w-3 mr-1" />
                        {statusInfo.label}
                      </Badge>
                    );
                  },
                },
                {
                  id: 'actions',
                  header: '操作',
                  cell: ({ row }) => {
                    const card = row.original;
                    return (
                      <div className="flex gap-1">
                        <Button
                          size="xs"
                          variant="ghost"
                          onClick={() => handleRecharge(card)}
                          disabled={card.status !== CardStatus.ACTIVE}
                        >
                          充值
                        </Button>
                        <Button
                          size="xs"
                          variant="ghost"
                          onClick={() => handleToggleStatus(card.id, card.status)}
                          disabled={card.status === CardStatus.CANCELLED}
                        >
                          {card.status === CardStatus.ACTIVE ? '冻结' : '解冻'}
                        </Button>
                      </div>
                    );
                  },
                },
              ]}
              data={memberCards}
              isLoading={false}
            />
          </div>
        )}
      </DataStateWrapper>

      {/* 对话框组件 */}
      <CreateMemberCardDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        memberId={memberId}
      />

      <RechargeDialog
        open={showRechargeDialog}
        onOpenChange={setShowRechargeDialog}
        card={selectedCard}
      />
    </div>
  );
}
