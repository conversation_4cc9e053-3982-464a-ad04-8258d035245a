'use client';

import { useState } from 'react';
import {
  CreditCard,
  Calendar,
  MoreHorizontal,
  AlertCircle,
} from 'lucide-react';
import { formatDate } from '@/lib/utils';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

import {
  CardType,
  CardTypeNames,
  CardStatus,
  MemberCardRead,
} from '@/types/api';
import { MESSAGES } from '@/constants/messages';

interface MemberCardItemProps {
  card: MemberCardRead;
  onRecharge: (card: MemberCardRead) => void;
  onFreeze: (card: MemberCardRead, reason?: string) => void;
  onUnfreeze: (card: MemberCardRead) => void;
  onCancel: (card: MemberCardRead, reason?: string) => void;
  onShowHistory: (card: MemberCardRead) => void;
}

export function MemberCardItem({
  card,
  onRecharge,
  onFreeze,
  onUnfreeze,
  onCancel,
  onShowHistory,
}: MemberCardItemProps) {
  const [showFreezeDialog, setShowFreezeDialog] = useState(false);
  const [freezeReason, setFreezeReason] = useState('');
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [cancelReason, setCancelReason] = useState('');

  const isTimesCard =
    card.card_type === CardType.TIMES_LIMITED ||
    card.card_type === CardType.TIMES_UNLIMITED;

  // 获取会员卡状态样式
  const getCardStatusStyle = (status: CardStatus) => {
    switch (status) {
      case CardStatus.ACTIVE:
        return { variant: 'default', label: '正常' };
      case CardStatus.FROZEN:
        return { variant: 'secondary', label: '已冻结' };
      case CardStatus.EXPIRED:
        return { variant: 'outline', label: '已过期' };
      case CardStatus.CANCELLED:
        return { variant: 'destructive', label: '已注销' };
      default:
        return { variant: 'outline', label: status };
    }
  };

  const cardStatusStyle = getCardStatusStyle(card.status);

  // 确认冻结
  const handleFreezeConfirm = () => {
    onFreeze(card, freezeReason);
    setShowFreezeDialog(false);
    setFreezeReason('');
  };

  // 确认注销
  const handleCancelConfirm = () => {
    onCancel(card, cancelReason);
    setShowCancelDialog(false);
    setCancelReason('');
  };

  return (
    <Card className="overflow-hidden border-2 hover:border-primary/50 transition-all">
      <CardHeader className="p-4 bg-muted/30">
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-2">
            <CreditCard className="h-5 w-5 text-primary" />
            <CardTitle className="text-lg">
              {CardTypeNames[card.card_type] || card.card_type}
            </CardTitle>
            <Badge
              variant={
                cardStatusStyle.variant as
                  | 'default'
                  | 'secondary'
                  | 'outline'
                  | 'destructive'
              }
            >
              {cardStatusStyle.label}
            </Badge>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">操作菜单</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>卡片操作</DropdownMenuLabel>
              <DropdownMenuSeparator />

              <DropdownMenuItem onClick={() => onRecharge(card)}>
                充值
              </DropdownMenuItem>

              <DropdownMenuSeparator />

              {card.status === CardStatus.ACTIVE && (
                <DropdownMenuItem onClick={() => setShowFreezeDialog(true)}>
                  冻结卡片
                </DropdownMenuItem>
              )}

              {card.status === CardStatus.FROZEN && (
                <DropdownMenuItem onClick={() => onUnfreeze(card)}>
                  解冻卡片
                </DropdownMenuItem>
              )}

              {card.status !== CardStatus.CANCELLED && (
                <DropdownMenuItem
                  onClick={() => setShowCancelDialog(true)}
                  className="text-destructive focus:text-destructive"
                >
                  注销卡片
                </DropdownMenuItem>
              )}

              <DropdownMenuSeparator />

              <DropdownMenuItem onClick={() => onShowHistory(card)}>
                操作记录
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        {card.card_number && (
          <CardDescription className="mt-1">
            卡号: {card.card_number}
          </CardDescription>
        )}
      </CardHeader>
      <CardContent className="p-4">
        <div className="flex flex-col gap-2">
          <div className="flex justify-between items-center">
            <span className="text-sm text-muted-foreground">
              余额{isTimesCard ? '(次)' : '(元)'}
            </span>
            <span className="text-xl font-semibold">{card.balance}</span>
          </div>

          {(card.total_recharged > 0 || card.total_consumed > 0) && (
            <div className="flex justify-between text-xs text-muted-foreground pt-1">
              <span>
                累计充值: {card.total_recharged}
                {isTimesCard ? '次' : '元'}
              </span>
              <span>
                累计消费: {card.total_consumed}
                {isTimesCard ? '次' : '元'}
              </span>
            </div>
          )}

          {card.status === CardStatus.FROZEN && card.freeze_reason && (
            <div className="flex items-start gap-1 mt-2 text-xs text-amber-600 bg-amber-50 dark:bg-amber-950/30 p-2 rounded">
              <AlertCircle className="h-3 w-3 mt-0.5" />
              <span>冻结原因: {card.freeze_reason}</span>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="p-4 pt-0 flex justify-between items-center text-xs text-muted-foreground">
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3" />
          <span>
            {card.expires_at
              ? `有效期至 ${formatDate(card.expires_at).split(' ')[0]}`
              : '永久有效'}
          </span>
        </div>
        <div>创建于 {formatDate(card.created_at).split(' ')[0]}</div>
      </CardFooter>

      {/* 冻结卡片对话框 */}
      <Dialog open={showFreezeDialog} onOpenChange={setShowFreezeDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>冻结会员卡</DialogTitle>
            <DialogDescription>
              请输入冻结原因，冻结后该卡将无法使用，可以后续解冻。
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <textarea
              className="w-full p-2 border rounded resize-none h-20"
              placeholder="请输入冻结原因..."
              value={freezeReason}
              onChange={e => setFreezeReason(e.target.value)}
            />
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowFreezeDialog(false)}
            >
              取消
            </Button>
            <Button onClick={handleFreezeConfirm}>确认冻结</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 注销卡片确认对话框 */}
      <AlertDialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认注销会员卡</AlertDialogTitle>
            <AlertDialogDescription>
              {MESSAGES.MEMBER_CARDS.CANCEL_CONFIRM}
              <br />
              <span className="text-destructive font-medium">
                注销后将无法恢复，请慎重操作！
              </span>
              <div className="mt-4">
                <label
                  htmlFor="cancelReason"
                  className="block text-sm font-medium mb-1"
                >
                  注销原因:
                </label>
                <textarea
                  id="cancelReason"
                  className="w-full p-2 border rounded resize-none h-20"
                  placeholder="请输入注销原因..."
                  value={cancelReason}
                  onChange={e => setCancelReason(e.target.value)}
                />
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleCancelConfirm}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              确认注销
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
}
