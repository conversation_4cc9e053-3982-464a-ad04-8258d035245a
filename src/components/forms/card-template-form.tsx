'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { CardType, CardTypeNames } from '@/types/api';
import type {
  CardTemplateCreate,
  CardTemplateUpdate,
  CardTemplateRead,
} from '@/types/api';

// 会员卡模板表单验证 schema
const cardTemplateFormSchema = z.object({
  name: z.string().min(1, '请输入模板名称'),
  card_type: z.nativeEnum(CardType),
  sale_price: z.coerce.number().min(0, '售价不能为负数'),
  available_balance: z.coerce
    .number()
    .min(0, '可用余额/次数不能为负数')
    .optional()
    .nullable(),
  validity_days: z.coerce
    .number()
    .min(1, '有效期不能小于1天')
    .optional()
    .nullable(),
  is_agent_exclusive: z.boolean().default(false),
  allow_repeat_purchase: z.boolean().default(true),
  allow_renewal: z.boolean().default(true),
  description: z.string().optional().nullable(),
  is_active: z.boolean().default(true),
});

type CardTemplateFormData = z.infer<typeof cardTemplateFormSchema>;

interface CardTemplateFormProps {
  template?: CardTemplateRead;
  onSubmit: (data: CardTemplateCreate | CardTemplateUpdate) => void;
  isLoading?: boolean;
  mode: 'create' | 'edit';
}

export function CardTemplateForm({
  template,
  onSubmit,
  isLoading = false,
  mode,
}: CardTemplateFormProps) {
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<CardTemplateFormData>({
    resolver: zodResolver(cardTemplateFormSchema),
    defaultValues: {
      name: template?.name || '',
      card_type: template?.card_type || CardType.VALUE_LIMITED,
      sale_price: template?.sale_price || 0,
      available_balance: template?.available_balance || null,
      validity_days: template?.validity_days || null,
      is_agent_exclusive: template?.is_agent_exclusive || false,
      allow_repeat_purchase: template?.allow_repeat_purchase || true,
      allow_renewal: template?.allow_renewal || true,
      description: template?.description || '',
      is_active: template?.is_active !== undefined ? template.is_active : true,
    },
  });

  const watchedCardType = watch('card_type');

  const handleFormSubmit = (data: CardTemplateFormData) => {
    // 清理空字符串，转换为 null
    const cleanedData = {
      ...data,
      description: data.description || null,
    };

    onSubmit(cleanedData);
  };

  return (
    <Card>
      <CardHeader>
        {/* <CardTitle>
          {mode === 'create' ? '新增会员卡模板' : '编辑会员卡模板'}
        </CardTitle>
        <CardDescription>
          {mode === 'create'
            ? '填写以下信息创建新会员卡模板'
            : '修改会员卡模板信息'}
        </CardDescription> */}
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 名称 */}
            <div className="space-y-2">
              <Label htmlFor="name">模板名称 *</Label>
              <Input
                id="name"
                {...register('name')}
                placeholder="请输入模板名称"
                className={errors.name ? 'border-destructive' : ''}
              />
              {errors.name && (
                <p className="text-sm text-destructive">
                  {errors.name.message}
                </p>
              )}
            </div>

            {/* 卡片类型 */}
            <div className="space-y-2">
              <Label>卡片类型 *</Label>
              <Select
                value={watchedCardType}
                onValueChange={(value: CardType) =>
                  setValue('card_type', value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="请选择卡片类型" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(CardTypeNames).map(([value, label]) => (
                    <SelectItem key={value} value={value}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.card_type && (
                <p className="text-sm text-destructive">
                  {errors.card_type.message}
                </p>
              )}
            </div>

            {/* 售价 */}
            <div className="space-y-2">
              <Label htmlFor="sale_price">售价(元) *</Label>
              <Input
                id="sale_price"
                type="number"
                min="0"
                step="0.01"
                {...register('sale_price')}
                placeholder="请输入售价"
                className={errors.sale_price ? 'border-destructive' : ''}
              />
              {errors.sale_price && (
                <p className="text-sm text-destructive">
                  {errors.sale_price.message}
                </p>
              )}
            </div>

            {/* 可用余额/次数 */}
            <div className="space-y-2">
              <Label htmlFor="available_balance">
                可用{watchedCardType?.includes('times') ? '次数' : '余额(元)'}
              </Label>
              <Input
                id="available_balance"
                type="number"
                min="0"
                step={watchedCardType?.includes('times') ? '1' : '0.01'}
                {...register('available_balance')}
                placeholder={`请输入可用${watchedCardType?.includes('times') ? '次数' : '余额'}`}
                className={errors.available_balance ? 'border-destructive' : ''}
              />
              <p className="text-xs text-muted-foreground">
                不填表示不限
                {watchedCardType?.includes('times') ? '次数' : '余额'}
              </p>
              {errors.available_balance && (
                <p className="text-sm text-destructive">
                  {errors.available_balance.message}
                </p>
              )}
            </div>

            {/* 有效期 */}
            <div className="space-y-2">
              <Label htmlFor="validity_days">有效期(天)</Label>
              <Input
                id="validity_days"
                type="number"
                min="1"
                {...register('validity_days')}
                placeholder="请输入有效期天数"
                className={errors.validity_days ? 'border-destructive' : ''}
              />
              <p className="text-xs text-muted-foreground">不填表示永久有效</p>
              {errors.validity_days && (
                <p className="text-sm text-destructive">
                  {errors.validity_days.message}
                </p>
              )}
            </div>
          </div>

          {/* 选项设置 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">选项设置</h3>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="is_agent_exclusive"
                checked={watch('is_agent_exclusive')}
                onCheckedChange={(checked: boolean) =>
                  setValue('is_agent_exclusive', checked)
                }
              />
              <Label htmlFor="is_agent_exclusive" className="cursor-pointer">
                代理专售
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="allow_repeat_purchase"
                checked={watch('allow_repeat_purchase')}
                onCheckedChange={(checked: boolean) =>
                  setValue('allow_repeat_purchase', checked)
                }
              />
              <Label htmlFor="allow_repeat_purchase" className="cursor-pointer">
                允许重复购买
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="allow_renewal"
                checked={watch('allow_renewal')}
                onCheckedChange={(checked: boolean) =>
                  setValue('allow_renewal', checked)
                }
              />
              <Label htmlFor="allow_renewal" className="cursor-pointer">
                支持线上续费
              </Label>
            </div>

            {mode === 'edit' && (
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="is_active"
                  checked={watch('is_active')}
                  onCheckedChange={(checked: boolean) =>
                    setValue('is_active', checked)
                  }
                />
                <Label htmlFor="is_active" className="cursor-pointer">
                  启用模板
                </Label>
              </div>
            )}
          </div>

          {/* 描述 */}
          <div className="space-y-2">
            <Label htmlFor="description">描述</Label>
            <Textarea
              id="description"
              {...register('description')}
              placeholder="请输入模板描述信息"
              rows={4}
            />
          </div>

          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => window.history.back()}
            >
              取消
            </Button>
            <Button type="submit" disabled={isLoading} variant="default">
              {isLoading
                ? '保存中...'
                : mode === 'create'
                  ? '创建模板'
                  : '保存修改'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
