'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { CreditCard, Coins } from 'lucide-react';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  CardType,
  MemberCardRead,
  PaymentMethod,
  PaymentMethodNames,
  RechargeRequest,
} from '@/types/api';

// 表单验证Schema
const rechargeFormSchema = z.object({
  amount: z.coerce.number().positive('充值金额必须大于0'),
  bonus_amount: z.coerce.number().min(0, '赠送金额不能为负数').optional(),
  payment_method: z.nativeEnum(PaymentMethod, {
    required_error: '请选择支付方式',
  }),
  notes: z.string().optional(),
});

type RechargeFormData = z.infer<typeof rechargeFormSchema>;

interface CardRechargeFormProps {
  card: MemberCardRead;
  onSubmit: (rechargeData: RechargeRequest) => void;
  onCancel: () => void;
  isSubmitting?: boolean;
}

export function CardRechargeForm({
  card,
  onSubmit,
  onCancel,
  isSubmitting = false,
}: CardRechargeFormProps) {
  const form = useForm<RechargeFormData>({
    resolver: zodResolver(rechargeFormSchema),
    defaultValues: {
      amount: 0,
      bonus_amount: 0,
      payment_method: PaymentMethod.MANUAL,
      notes: '',
    },
  });

  // 是否为次数卡
  const isTimesCard =
    card.card_type === CardType.TIMES_LIMITED ||
    card.card_type === CardType.TIMES_UNLIMITED;

  // 处理提交
  const handleSubmit = (data: RechargeFormData) => {
    const rechargeData: RechargeRequest = {
      member_card_id: card.id,
      amount: Number(data.amount),
      bonus_amount: data.bonus_amount ? Number(data.bonus_amount) : 0,
      payment_method: data.payment_method,
      notes: data.notes || undefined,
    };

    onSubmit(rechargeData);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        {/* 卡片信息 */}
        <div className="flex items-center gap-2 mb-4 bg-muted/40 p-3 rounded">
          <CreditCard className="h-5 w-5 text-primary" />
          <span className="font-medium">会员卡ID: {card.id}</span>
          <span className="text-muted-foreground ml-auto">
            当前余额: {card.balance} {isTimesCard ? '次' : '元'}
          </span>
        </div>

        {/* 充值金额 */}
        <FormField
          control={form.control}
          name="amount"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                充值{isTimesCard ? '次数' : '金额'}{' '}
                <span className="text-destructive">*</span>
              </FormLabel>
              <FormControl>
                <div className="flex items-center">
                  <Coins className="h-4 w-4 mr-2 text-muted-foreground" />
                  <Input
                    type="number"
                    placeholder={`请输入充值${isTimesCard ? '次数' : '金额'}`}
                    {...field}
                    step={isTimesCard ? '1' : '0.01'}
                    min="0"
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 赠送金额 */}
        <FormField
          control={form.control}
          name="bonus_amount"
          render={({ field }) => (
            <FormItem>
              <FormLabel>赠送{isTimesCard ? '次数' : '金额'}</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  placeholder={`请输入赠送${isTimesCard ? '次数' : '金额'}`}
                  {...field}
                  step={isTimesCard ? '1' : '0.01'}
                  min="0"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 支付方式 */}
        <FormField
          control={form.control}
          name="payment_method"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                支付方式 <span className="text-destructive">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择支付方式" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {Object.entries(PaymentMethodNames).map(([value, label]) => (
                    <SelectItem key={value} value={value}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 备注 */}
        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>备注</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="请输入备注信息"
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 操作按钮 */}
        <div className="flex justify-end gap-3">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            取消
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? '充值中...' : '确认充值'}
          </Button>
        </div>
      </form>
    </Form>
  );
}
