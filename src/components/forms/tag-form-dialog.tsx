'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { FormDialog, FormField } from '@/components/ui/form-dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useCreateTag, useUpdateTag, useTagCategories } from '@/hooks/use-tags';
import { TAG_VALIDATION, DefaultTagColors } from '@/constants/tags';
import { TagStatus } from '@/types/api';
import type { TagWithCategory, TagList } from '@/types/api';

// 表单验证schema
const tagSchema = z.object({
  name: z
    .string()
    .min(TAG_VALIDATION.TAG.NAME_MIN_LENGTH, '标签名称不能为空')
    .max(TAG_VALIDATION.TAG.NAME_MAX_LENGTH, `标签名称不能超过${TAG_VALIDATION.TAG.NAME_MAX_LENGTH}个字符`),
  category_id: z.number().min(1, '请选择标签分类'),
  description: z
    .string()
    .max(TAG_VALIDATION.TAG.DESCRIPTION_MAX_LENGTH, `描述不能超过${TAG_VALIDATION.TAG.DESCRIPTION_MAX_LENGTH}个字符`)
    .optional(),
  color: z.string().optional(),
  status: z.nativeEnum(TagStatus).optional(),
});

type TagFormData = z.infer<typeof tagSchema>;

interface TagFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  tag?: TagWithCategory | TagList | null;
  defaultCategoryId?: number;
}

export function TagFormDialog({
  open,
  onOpenChange,
  tag,
  defaultCategoryId,
}: TagFormDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedColor, setSelectedColor] = useState<string>(DefaultTagColors[0]);
  const isEdit = !!tag;

  const createMutation = useCreateTag();
  const updateMutation = useUpdateTag();
  const { data: categoriesData } = useTagCategories();

  const form = useForm<TagFormData>({
    resolver: zodResolver(tagSchema),
    defaultValues: {
      name: '',
      category_id: defaultCategoryId || 0,
      description: '',
      color: DefaultTagColors[0],
      status: TagStatus.ACTIVE,
    },
  });

  // 当tag变化时重置表单
  useEffect(() => {
    if (open) {
      if (tag) {
        const color = tag.color || DefaultTagColors[0];
        setSelectedColor(color);
        form.reset({
          name: tag.name,
          category_id: tag.category_id,
          description: tag.description || '',
          color,
          status: tag.status as TagStatus,
        });
      } else {
        const color = DefaultTagColors[0];
        setSelectedColor(color);
        form.reset({
          name: '',
          category_id: defaultCategoryId || 0,
          description: '',
          color,
          status: TagStatus.ACTIVE,
        });
      }
    }
  }, [open, tag, defaultCategoryId, form]);

  const handleSubmit = async (data: TagFormData) => {
    try {
      setIsSubmitting(true);
      
      if (isEdit && tag) {
        await updateMutation.mutateAsync({
          id: tag.id.toString(),
          data,
        });
      } else {
        await createMutation.mutateAsync(data);
      }
      
      onOpenChange(false);
      form.reset();
    } catch (error) {
      console.error('提交失败:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
    form.reset();
  };

  const handleColorSelect = (color: string) => {
    setSelectedColor(color);
    form.setValue('color', color);
  };

  return (
    <FormDialog
      open={open}
      onOpenChange={onOpenChange}
      title={isEdit ? '编辑标签' : '新增标签'}
      description={isEdit ? '修改标签信息' : '创建新的标签'}
      onConfirm={form.handleSubmit(handleSubmit)}
      confirmText={isEdit ? '保存' : '创建'}
      cancelText="取消"
      isLoading={isSubmitting}
      disabled={!form.formState.isValid}
    >
      <div className="space-y-4">
        <FormField label="标签名称" required>
          <Input
            {...form.register('name')}
            placeholder="请输入标签名称"
            className={form.formState.errors.name ? 'border-destructive' : ''}
          />
          {form.formState.errors.name && (
            <p className="text-sm text-destructive mt-1">
              {form.formState.errors.name.message}
            </p>
          )}
        </FormField>

        <FormField label="所属分类" required>
          <Select
            value={form.watch('category_id')?.toString()}
            onValueChange={(value) => form.setValue('category_id', parseInt(value))}
          >
            <SelectTrigger>
              <SelectValue placeholder="请选择标签分类" />
            </SelectTrigger>
            <SelectContent>
              {categoriesData?.data?.map((category) => (
                <SelectItem key={category.id} value={category.id.toString()}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {form.formState.errors.category_id && (
            <p className="text-sm text-destructive mt-1">
              {form.formState.errors.category_id.message}
            </p>
          )}
        </FormField>

        <FormField label="标签颜色">
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <div
                className="w-6 h-6 rounded-full border-2 border-gray-300"
                style={{ backgroundColor: selectedColor }}
              />
              <Badge variant="outline" style={{ color: selectedColor, borderColor: selectedColor }}>
                {form.watch('name') || '预览'}
              </Badge>
            </div>
            <div className="grid grid-cols-5 gap-2">
              {DefaultTagColors.map((color) => (
                <button
                  key={color}
                  type="button"
                  className={`w-8 h-8 rounded-full border-2 ${
                    selectedColor === color ? 'border-gray-800' : 'border-gray-300'
                  }`}
                  style={{ backgroundColor: color }}
                  onClick={() => handleColorSelect(color)}
                />
              ))}
            </div>
          </div>
        </FormField>

        <FormField label="状态">
          <Select
            value={form.watch('status')}
            onValueChange={(value) => form.setValue('status', value as TagStatus)}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={TagStatus.ACTIVE}>激活</SelectItem>
              <SelectItem value={TagStatus.INACTIVE}>停用</SelectItem>
            </SelectContent>
          </Select>
        </FormField>

        <FormField label="描述">
          <Textarea
            {...form.register('description')}
            placeholder="请输入标签描述（可选）"
            rows={3}
            error={form.formState.errors.description?.message}
          />
        </FormField>
      </div>
    </FormDialog>
  );
}
