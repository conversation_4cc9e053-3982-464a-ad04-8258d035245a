'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { FormDialog, FormField } from '@/components/ui/form-dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useCreateTagCategory, useUpdateTagCategory } from '@/hooks/use-tags';
import { TAG_VALIDATION } from '@/constants/tags';
import type { TagCategoryList } from '@/types/api';

// 表单验证schema
const tagCategorySchema = z.object({
  name: z
    .string()
    .min(TAG_VALIDATION.CATEGORY.NAME_MIN_LENGTH, '分类名称不能为空')
    .max(TAG_VALIDATION.CATEGORY.NAME_MAX_LENGTH, `分类名称不能超过${TAG_VALIDATION.CATEGORY.NAME_MAX_LENGTH}个字符`),
  description: z
    .string()
    .max(TAG_VALIDATION.CATEGORY.DESCRIPTION_MAX_LENGTH, `描述不能超过${TAG_VALIDATION.CATEGORY.DESCRIPTION_MAX_LENGTH}个字符`)
    .optional(),
});

type TagCategoryFormData = z.infer<typeof tagCategorySchema>;

interface TagCategoryFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  category?: TagCategoryList | null;
}

export function TagCategoryFormDialog({
  open,
  onOpenChange,
  category,
}: TagCategoryFormDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const isEdit = !!category;

  const createMutation = useCreateTagCategory();
  const updateMutation = useUpdateTagCategory();

  const form = useForm<TagCategoryFormData>({
    resolver: zodResolver(tagCategorySchema),
    defaultValues: {
      name: '',
      description: '',
    },
  });

  // 当category变化时重置表单
  useEffect(() => {
    if (open) {
      if (category) {
        form.reset({
          name: category.name,
          description: category.description || '',
        });
      } else {
        form.reset({
          name: '',
          description: '',
        });
      }
    }
  }, [open, category, form]);

  const handleSubmit = async (data: TagCategoryFormData) => {
    try {
      setIsSubmitting(true);
      
      if (isEdit && category) {
        await updateMutation.mutateAsync({
          id: category.id.toString(),
          data,
        });
      } else {
        await createMutation.mutateAsync(data);
      }
      
      onOpenChange(false);
      form.reset();
    } catch (error) {
      console.error('提交失败:', error);
    } finally {
      setIsSubmitting(false);
    }
  };



  return (
    <FormDialog
      open={open}
      onOpenChange={onOpenChange}
      title={isEdit ? '编辑标签分类' : '新增标签分类'}
      description={isEdit ? '修改标签分类信息' : '创建新的标签分类'}
      onConfirm={form.handleSubmit(handleSubmit)}
      confirmText={isEdit ? '保存' : '创建'}
      cancelText="取消"
      isLoading={isSubmitting}
      disabled={!form.formState.isValid}
    >
      <div className="space-y-4">
        <FormField label="分类名称" required>
          <Input
            {...form.register('name')}
            placeholder="请输入分类名称"
            className={form.formState.errors.name ? 'border-destructive' : ''}
          />
          {form.formState.errors.name && (
            <p className="text-sm text-destructive mt-1">
              {form.formState.errors.name.message}
            </p>
          )}
        </FormField>

        <FormField label="描述">
          <Textarea
            {...form.register('description')}
            placeholder="请输入分类描述（可选）"
            rows={3}
            className={form.formState.errors.description ? 'border-destructive' : ''}
          />
          {form.formState.errors.description && (
            <p className="text-sm text-destructive mt-1">
              {form.formState.errors.description.message}
            </p>
          )}
        </FormField>
      </div>
    </FormDialog>
  );
}
