'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { FormRow, FormGroup, FormActions } from '@/components/forms/form-row';
import { commonValidation } from '@/lib/utils/validation';
import type {
  UserCreate,
  UserUpdate,
  UserRead,
  UserRole,
  Gender,
} from '@/types/api';

// 用户表单验证 schema
const userFormSchema = z.object({
  username: commonValidation.username(),
  email: z.string().email('请输入有效的邮箱地址').optional().or(z.literal('')),
  phone: z.string().optional().or(z.literal('')),
  password: z.string().min(6, '密码至少需要6位').optional().or(z.literal('')),
  real_name: z.string().optional().or(z.literal('')),
  role: z.enum(['super_admin', 'admin', 'agent', 'sale'] as const),
  gender: z.enum(['male', 'female', 'other'] as const).optional(),
  birthday: z.string().optional().or(z.literal('')),
});

type UserFormData = z.infer<typeof userFormSchema>;

interface UserFormProps {
  user?: UserRead;
  onSubmit: (data: UserCreate | UserUpdate) => void;
  isLoading?: boolean;
  mode: 'create' | 'edit';
}

export function UserForm({
  user,
  onSubmit,
  isLoading = false,
  mode,
}: UserFormProps) {
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<UserFormData>({
    resolver: zodResolver(userFormSchema),
    defaultValues: {
      username: user?.username || '',
      email: user?.email || '',
      phone: user?.phone || '',
      password: '',
      real_name: user?.real_name || '',
      role: user?.role || 'admin',
      gender: user?.gender || undefined,
      birthday: user?.birthday || '',
    },
  });

  const watchedRole = watch('role');
  const watchedGender = watch('gender');

  const handleFormSubmit = (data: UserFormData) => {
    // 清理空字符串，转换为 null
    const cleanedData = {
      ...data,
      email: data.email || null,
      phone: data.phone || null,
      real_name: data.real_name || null,
      gender: data.gender || null,
      birthday: data.birthday || null,
    };

    // 如果是编辑模式且密码为空，则不包含密码字段
    if (mode === 'edit' && !cleanedData.password) {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { password, ...updateData } = cleanedData;
      onSubmit(updateData);
    } else {
      onSubmit(cleanedData);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{mode === 'create' ? '新增用户' : '编辑用户'}</CardTitle>
        <CardDescription>
          {mode === 'create'
            ? '填写以下信息创建新用户'
            : '修改用户信息，密码留空则不修改'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)}>
          <FormGroup>
            {/* 用户名 */}
            <FormRow label="用户名" required error={errors.username?.message}>
              <Input
                id="username"
                {...register('username')}
                placeholder="请输入用户名"
                className={errors.username ? 'border-destructive' : ''}
              />
            </FormRow>

            {/* 邮箱 */}
            <FormRow label="邮箱" error={errors.email?.message}>
              <Input
                id="email"
                type="email"
                {...register('email')}
                placeholder="请输入邮箱"
                className={errors.email ? 'border-destructive' : ''}
              />
            </FormRow>

            {/* 手机号 */}
            <FormRow label="手机号" error={errors.phone?.message}>
              <Input
                id="phone"
                {...register('phone')}
                placeholder="请输入手机号"
                className={errors.phone ? 'border-destructive' : ''}
              />
            </FormRow>

            {/* 真实姓名 */}
            <FormRow label="真实姓名">
              <Input
                id="real_name"
                {...register('real_name')}
                placeholder="请输入真实姓名"
              />
            </FormRow>

            {/* 密码 */}
            <FormRow
              label="密码"
              required={mode === 'create'}
              error={errors.password?.message}
            >
              <Input
                id="password"
                type="password"
                {...register('password')}
                placeholder={
                  mode === 'create' ? '请输入密码' : '留空不修改密码'
                }
                className={errors.password ? 'border-destructive' : ''}
              />
              {mode === 'edit' && (
                <div className="text-xs text-muted-foreground mt-1">
                  留空表示不修改密码
                </div>
              )}
            </FormRow>

            {/* 角色 */}
            <FormRow label="角色" required error={errors.role?.message}>
              <Select
                value={watchedRole}
                onValueChange={(value: UserRole) => setValue('role', value)}
              >
                <SelectTrigger className="w-52">
                  <SelectValue placeholder="请选择角色" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="user">普通用户</SelectItem>
                  <SelectItem value="admin">管理员</SelectItem>
                  <SelectItem value="super_admin">超级管理员</SelectItem>
                </SelectContent>
              </Select>
            </FormRow>

            {/* 性别 */}
            <FormRow label="性别">
              <Select
                value={watchedGender || ''}
                onValueChange={(value: Gender) => setValue('gender', value)}
              >
                <SelectTrigger className="w-52">
                  <SelectValue placeholder="请选择性别" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="male">男</SelectItem>
                  <SelectItem value="female">女</SelectItem>
                  <SelectItem value="other">其他</SelectItem>
                </SelectContent>
              </Select>
            </FormRow>

            {/* 生日 */}
            <FormRow label="生日">
              <Input
                id="birthday"
                type="date"
                {...register('birthday')}
                className="w-52"
              />
            </FormRow>

            <FormActions>
              <Button type="button" variant="outline">
                取消
              </Button>
              <Button type="submit" disabled={isLoading} variant="default">
                {isLoading ? '保存中...' : mode === 'create' ? '创建用户' : '保存修改'}
              </Button>
            </FormActions>
          </FormGroup>
        </form>
      </CardContent>
    </Card>
  );
}
