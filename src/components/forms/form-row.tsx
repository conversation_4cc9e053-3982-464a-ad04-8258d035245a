'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';

interface FormRowProps {
  label: string;
  required?: boolean;
  error?: string;
  labelWidth?: string | number;
  className?: string;
  children?: React.ReactNode;
}

/**
 * 紧凑型水平表单行组件
 *
 * 用于创建更符合中国用户习惯的表单布局，标签右对齐，内容左对齐
 */
export function FormRow({
  label,
  required = false,
  error,
  labelWidth = '120px',
  className,
  children,
  ...props
}: FormRowProps &
  Omit<React.HTMLAttributes<HTMLDivElement>, keyof FormRowProps>) {
  return (
    <div className={cn('flex items-start mb-3 text-sm', className)} {...props}>
      <div
        className="flex-shrink-0 pt-2 text-right text-muted-foreground pr-3"
        style={{
          width:
            typeof labelWidth === 'number' ? `${labelWidth}px` : labelWidth,
        }}
      >
        {required && <span className="text-destructive mr-1">*</span>}
        {label}：
      </div>
      <div className="flex-1">
        {children}
        {error && <p className="text-destructive text-xs mt-1">{error}</p>}
      </div>
    </div>
  );
}

/**
 * 紧凑型表单组
 *
 * 用于组织多个表单行，提供统一的间距和样式
 */
export function FormGroup({
  className,
  children,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div className={cn('space-y-2', className)} {...props}>
      {children}
    </div>
  );
}

/**
 * 紧凑型表单操作区
 *
 * 用于放置表单底部的按钮等操作元素
 */
export function FormActions({
  className,
  children,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn('flex items-center justify-end gap-3 mt-6', className)}
      {...props}
    >
      {children}
    </div>
  );
}
