// 教师表单弹框组件

'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { FormDialog, FormField } from '@/components/ui/form-dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { X, Plus } from 'lucide-react';
import { useCreateTeacher, useUpdateTeacher } from '@/hooks/use-teachers';
import {
  TEACHER_REGION_OPTIONS,
  TEACHER_CATEGORY_OPTIONS,
  GENDER_OPTIONS,
  TEACHER_VALIDATION,
  TEACHER_DEFAULTS,
} from '@/constants/teachers';
import type { TeacherList, TeacherCreate, TeacherUpdate, Gender, TeacherCategory, TeacherRegion } from '@/types/api';

// 表单验证schema
const teacherSchema = z.object({
  name: z.string()
    .min(TEACHER_VALIDATION.NAME_MIN_LENGTH, '教师姓名不能为空')
    .max(TEACHER_VALIDATION.NAME_MAX_LENGTH, `教师姓名不能超过${TEACHER_VALIDATION.NAME_MAX_LENGTH}个字符`),
  display_code: z.string()
    .max(TEACHER_VALIDATION.DISPLAY_CODE_MAX_LENGTH, `显示编号不能超过${TEACHER_VALIDATION.DISPLAY_CODE_MAX_LENGTH}个字符`)
    .optional(),
  gender: z.enum(['male', 'female', 'other']).optional(),
  phone: z.string()
    .min(TEACHER_VALIDATION.PHONE_MIN_LENGTH, `手机号不能少于${TEACHER_VALIDATION.PHONE_MIN_LENGTH}位`)
    .max(TEACHER_VALIDATION.PHONE_MAX_LENGTH, `手机号不能超过${TEACHER_VALIDATION.PHONE_MAX_LENGTH}位`)
    .optional(),
  email: z.string()
    .email('请输入有效的邮箱地址')
    .max(TEACHER_VALIDATION.EMAIL_MAX_LENGTH, `邮箱不能超过${TEACHER_VALIDATION.EMAIL_MAX_LENGTH}个字符`)
    .optional(),
  price_per_class: z.number()
    .min(TEACHER_VALIDATION.PRICE_MIN, `课时费不能少于${TEACHER_VALIDATION.PRICE_MIN}元`)
    .max(TEACHER_VALIDATION.PRICE_MAX, `课时费不能超过${TEACHER_VALIDATION.PRICE_MAX}元`),
  teacher_category: z.enum(['european', 'south_african', 'filipino', 'chinese', 'other']),
  region: z.enum(['europe', 'north_america', 'south_africa', 'philippines', 'china', 'other']),
  wechat_bound: z.boolean(),
  show_to_members: z.boolean(),
  introduction: z.string()
    .max(TEACHER_VALIDATION.INTRODUCTION_MAX_LENGTH, `教师介绍不能超过${TEACHER_VALIDATION.INTRODUCTION_MAX_LENGTH}个字符`)
    .optional(),
  teaching_experience: z.number()
    .min(TEACHER_VALIDATION.TEACHING_EXPERIENCE_MIN, `教学经验不能少于${TEACHER_VALIDATION.TEACHING_EXPERIENCE_MIN}年`)
    .max(TEACHER_VALIDATION.TEACHING_EXPERIENCE_MAX, `教学经验不能超过${TEACHER_VALIDATION.TEACHING_EXPERIENCE_MAX}年`)
    .optional(),
  priority_level: z.number()
    .min(TEACHER_VALIDATION.PRIORITY_LEVEL_MIN, `优先级不能少于${TEACHER_VALIDATION.PRIORITY_LEVEL_MIN}`)
    .max(TEACHER_VALIDATION.PRIORITY_LEVEL_MAX, `优先级不能超过${TEACHER_VALIDATION.PRIORITY_LEVEL_MAX}`),
  notes: z.string()
    .max(TEACHER_VALIDATION.NOTES_MAX_LENGTH, `备注不能超过${TEACHER_VALIDATION.NOTES_MAX_LENGTH}个字符`)
    .optional(),
  specialties: z.array(z.string()).default([]),
  certifications: z.array(z.string()).default([]),
  tag_ids: z.array(z.number()).default([]),
});

type TeacherFormData = z.infer<typeof teacherSchema>;

interface TeacherFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  teacher?: TeacherList | null;
}

export function TeacherFormDialog({
  open,
  onOpenChange,
  teacher,
}: TeacherFormDialogProps) {
  const [specialtyInput, setSpecialtyInput] = useState('');
  const [certificationInput, setCertificationInput] = useState('');
  
  const isEdit = !!teacher;
  
  const form = useForm<TeacherFormData>({
    resolver: zodResolver(teacherSchema),
    defaultValues: {
      name: '',
      display_code: '',
      gender: undefined,
      phone: '',
      email: '',
      price_per_class: TEACHER_DEFAULTS.PRICE_PER_CLASS,
      teacher_category: 'chinese',
      region: 'china',
      wechat_bound: TEACHER_DEFAULTS.WECHAT_BOUND,
      show_to_members: TEACHER_DEFAULTS.SHOW_TO_MEMBERS,
      introduction: '',
      teaching_experience: TEACHER_DEFAULTS.TEACHING_EXPERIENCE,
      priority_level: TEACHER_DEFAULTS.PRIORITY_LEVEL,
      notes: '',
      specialties: TEACHER_DEFAULTS.SPECIALTIES,
      certifications: TEACHER_DEFAULTS.CERTIFICATIONS,
      tag_ids: TEACHER_DEFAULTS.TAG_IDS,
    },
  });

  const createMutation = useCreateTeacher();
  const updateMutation = useUpdateTeacher();


  // 重置表单数据
  useEffect(() => {
    if (open) {
      if (teacher) {
        form.reset({
          name: teacher.name,
          display_code: teacher.display_code || '',
          gender: teacher.gender,
          phone: '',
          email: '',
          price_per_class: teacher.price_per_class,
          teacher_category: teacher.teacher_category,
          region: teacher.region,
          wechat_bound: false,
          show_to_members: teacher.show_to_members,
          introduction: '',
          teaching_experience: teacher.teaching_experience || 0,
          priority_level: teacher.priority_level,
          notes: '',
          specialties: [],
          certifications: [],
          tag_ids: teacher.tags?.map(tag => tag.id) || [],
        });
      } else {
        form.reset({
          name: '',
          display_code: '',
          gender: undefined,
          phone: '',
          email: '',
          price_per_class: TEACHER_DEFAULTS.PRICE_PER_CLASS,
          teacher_category: 'chinese',
          region: 'china',
          wechat_bound: TEACHER_DEFAULTS.WECHAT_BOUND,
          show_to_members: TEACHER_DEFAULTS.SHOW_TO_MEMBERS,
          introduction: '',
          teaching_experience: TEACHER_DEFAULTS.TEACHING_EXPERIENCE,
          priority_level: TEACHER_DEFAULTS.PRIORITY_LEVEL,
          notes: '',
          specialties: TEACHER_DEFAULTS.SPECIALTIES,
          certifications: TEACHER_DEFAULTS.CERTIFICATIONS,
          tag_ids: TEACHER_DEFAULTS.TAG_IDS,
        });
      }
    }
  }, [open, teacher, form]);

  const handleSubmit = async (data: TeacherFormData) => {
    try {
      if (isEdit && teacher) {
        const updateData: TeacherUpdate = {
          name: data.name,
          display_code: data.display_code,
          gender: data.gender,
          phone: data.phone,
          email: data.email,
          price_per_class: data.price_per_class,
          teacher_category: data.teacher_category,
          region: data.region,
          wechat_bound: data.wechat_bound,
          show_to_members: data.show_to_members,
          introduction: data.introduction,
          teaching_experience: data.teaching_experience,
          priority_level: data.priority_level,
          notes: data.notes,
          specialties: data.specialties,
          certifications: data.certifications,
        };
        await updateMutation.mutateAsync({ id: teacher.id.toString(), data: updateData });
      } else {
        const createData: TeacherCreate = {
          ...data,
          avatar: '',
          wechat_openid: '',
          wechat_unionid: '',
        };
        await createMutation.mutateAsync(createData);
      }
      onOpenChange(false);
    } catch (error) {
      console.error('提交失败:', error);
    }
  };

  const addSpecialty = () => {
    if (specialtyInput.trim()) {
      const currentSpecialties = form.getValues('specialties');
      form.setValue('specialties', [...currentSpecialties, specialtyInput.trim()]);
      setSpecialtyInput('');
    }
  };

  const removeSpecialty = (index: number) => {
    const currentSpecialties = form.getValues('specialties');
    form.setValue('specialties', currentSpecialties.filter((_, i) => i !== index));
  };

  const addCertification = () => {
    if (certificationInput.trim()) {
      const currentCertifications = form.getValues('certifications');
      form.setValue('certifications', [...currentCertifications, certificationInput.trim()]);
      setCertificationInput('');
    }
  };

  const removeCertification = (index: number) => {
    const currentCertifications = form.getValues('certifications');
    form.setValue('certifications', currentCertifications.filter((_, i) => i !== index));
  };

  const isLoading = createMutation.isPending || updateMutation.isPending;

  return (
    <FormDialog
      open={open}
      onOpenChange={onOpenChange}
      title={isEdit ? '编辑教师' : '新增教师'}
      size="lg"
      onConfirm={form.handleSubmit(handleSubmit)}
      confirmText={isEdit ? '更新' : '创建'}
      isLoading={isLoading}
    >
      <div className="space-y-6">
        {/* 基本信息 */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">基本信息</h3>
          
          <div className="grid grid-cols-2 gap-4">
            <FormField label="教师姓名" required>
              <Input
                {...form.register('name')}
                placeholder="请输入教师姓名"
                className={form.formState.errors.name ? 'border-destructive' : ''}
              />
              {form.formState.errors.name && (
                <p className="text-sm text-destructive mt-1">
                  {form.formState.errors.name.message}
                </p>
              )}
            </FormField>

            <FormField label="显示编号">
              <Input
                {...form.register('display_code')}
                placeholder="请输入显示编号"
                className={form.formState.errors.display_code ? 'border-destructive' : ''}
              />
              {form.formState.errors.display_code && (
                <p className="text-sm text-destructive mt-1">
                  {form.formState.errors.display_code.message}
                </p>
              )}
            </FormField>
          </div>

          <div className="grid grid-cols-3 gap-4">
            <FormField label="性别">
              <Select
                value={form.watch('gender') || ''}
                onValueChange={(value) => form.setValue('gender', value as Gender)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择性别" />
                </SelectTrigger>
                <SelectContent>
                  {GENDER_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormField>

            <FormField label="教师分类" required>
              <Select
                value={form.watch('teacher_category')}
                onValueChange={(value) => form.setValue('teacher_category', value as TeacherCategory)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择分类" />
                </SelectTrigger>
                <SelectContent>
                  {TEACHER_CATEGORY_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormField>

            <FormField label="教师区域" required>
              <Select
                value={form.watch('region')}
                onValueChange={(value) => form.setValue('region', value as TeacherRegion)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择区域" />
                </SelectTrigger>
                <SelectContent>
                  {TEACHER_REGION_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormField>
          </div>
        </div>

        {/* 联系信息 */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">联系信息</h3>

          <div className="grid grid-cols-2 gap-4">
            <FormField label="手机号">
              <Input
                {...form.register('phone')}
                placeholder="请输入手机号"
                className={form.formState.errors.phone ? 'border-destructive' : ''}
              />
              {form.formState.errors.phone && (
                <p className="text-sm text-destructive mt-1">
                  {form.formState.errors.phone.message}
                </p>
              )}
            </FormField>

            <FormField label="邮箱">
              <Input
                {...form.register('email')}
                type="email"
                placeholder="请输入邮箱"
                className={form.formState.errors.email ? 'border-destructive' : ''}
              />
              {form.formState.errors.email && (
                <p className="text-sm text-destructive mt-1">
                  {form.formState.errors.email.message}
                </p>
              )}
            </FormField>
          </div>
        </div>

        {/* 教学信息 */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">教学信息</h3>

          <div className="grid grid-cols-3 gap-4">
            <FormField label="课时费(元)" required>
              <Input
                {...form.register('price_per_class', { valueAsNumber: true })}
                type="number"
                min="0"
                placeholder="请输入课时费"
                className={form.formState.errors.price_per_class ? 'border-destructive' : ''}
              />
              {form.formState.errors.price_per_class && (
                <p className="text-sm text-destructive mt-1">
                  {form.formState.errors.price_per_class.message}
                </p>
              )}
            </FormField>

            <FormField label="教学经验(年)">
              <Input
                {...form.register('teaching_experience', { valueAsNumber: true })}
                type="number"
                min="0"
                placeholder="请输入教学经验"
                className={form.formState.errors.teaching_experience ? 'border-destructive' : ''}
              />
              {form.formState.errors.teaching_experience && (
                <p className="text-sm text-destructive mt-1">
                  {form.formState.errors.teaching_experience.message}
                </p>
              )}
            </FormField>

            <FormField label="优先级">
              <Input
                {...form.register('priority_level', { valueAsNumber: true })}
                type="number"
                min="0"
                max="100"
                placeholder="请输入优先级"
                className={form.formState.errors.priority_level ? 'border-destructive' : ''}
              />
              {form.formState.errors.priority_level && (
                <p className="text-sm text-destructive mt-1">
                  {form.formState.errors.priority_level.message}
                </p>
              )}
            </FormField>
          </div>

          <FormField label="教师介绍">
            <Textarea
              {...form.register('introduction')}
              placeholder="请输入教师介绍"
              rows={3}
              className={form.formState.errors.introduction ? 'border-destructive' : ''}
            />
            {form.formState.errors.introduction && (
              <p className="text-sm text-destructive mt-1">
                {form.formState.errors.introduction.message}
              </p>
            )}
          </FormField>
        </div>

        {/* 专业特长 */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">专业特长</h3>

          <div className="flex space-x-2">
            <Input
              value={specialtyInput}
              onChange={(e) => setSpecialtyInput(e.target.value)}
              placeholder="输入专业特长"
              onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addSpecialty())}
            />
            <Button type="button" onClick={addSpecialty} size="sm">
              <Plus className="h-4 w-4" />
            </Button>
          </div>

          <div className="flex flex-wrap gap-2">
            {form.watch('specialties').map((specialty, index) => (
              <Badge key={index} variant="secondary" className="flex items-center gap-1">
                {specialty}
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 hover:bg-transparent"
                  onClick={() => removeSpecialty(index)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ))}
          </div>
        </div>

        {/* 资质证书 */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">资质证书</h3>

          <div className="flex space-x-2">
            <Input
              value={certificationInput}
              onChange={(e) => setCertificationInput(e.target.value)}
              placeholder="输入资质证书"
              onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addCertification())}
            />
            <Button type="button" onClick={addCertification} size="sm">
              <Plus className="h-4 w-4" />
            </Button>
          </div>

          <div className="flex flex-wrap gap-2">
            {form.watch('certifications').map((certification, index) => (
              <Badge key={index} variant="secondary" className="flex items-center gap-1">
                {certification}
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 hover:bg-transparent"
                  onClick={() => removeCertification(index)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ))}
          </div>
        </div>

        {/* 设置选项 */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">设置选项</h3>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium">对会员端展示</label>
                <p className="text-sm text-muted-foreground">是否在会员端显示该教师</p>
              </div>
              <Switch
                checked={form.watch('show_to_members')}
                onCheckedChange={(checked) => form.setValue('show_to_members', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium">微信绑定</label>
                <p className="text-sm text-muted-foreground">是否已绑定微信账号</p>
              </div>
              <Switch
                checked={form.watch('wechat_bound')}
                onCheckedChange={(checked) => form.setValue('wechat_bound', checked)}
              />
            </div>
          </div>
        </div>

        {/* 备注 */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">备注</h3>

          <FormField label="备注信息">
            <Textarea
              {...form.register('notes')}
              placeholder="请输入备注信息"
              rows={2}
              className={form.formState.errors.notes ? 'border-destructive' : ''}
            />
            {form.formState.errors.notes && (
              <p className="text-sm text-destructive mt-1">
                {form.formState.errors.notes.message}
              </p>
            )}
          </FormField>
        </div>
      </div>
    </FormDialog>
  );
}
