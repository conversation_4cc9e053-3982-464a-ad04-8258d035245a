'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { MemberType, MemberStatus } from '@/types/api';
import type {
  MemberCreate,
  MemberUpdate,
  MemberRead,
  Gender,
} from '@/types/api';

// 会员表单验证 schema
const memberFormSchema = z.object({
  name: z.string().min(1, '请输入会员姓名'),
  phone: z.string().min(1, '请输入手机号码'),
  email: z.string().email('请输入有效的邮箱地址').optional().or(z.literal('')),
  gender: z
    .enum(['male', 'female', 'other'] as const)
    .optional()
    .nullable(),
  birthday: z.string().optional().or(z.literal('')),
  member_type: z.nativeEnum(MemberType).optional(),
  member_status: z.nativeEnum(MemberStatus).optional(),
  source_channel: z.string().optional().or(z.literal('')),
  address: z.string().optional().or(z.literal('')),
  city: z.string().optional().or(z.literal('')),
  province: z.string().optional().or(z.literal('')),
  postal_code: z.string().optional().or(z.literal('')),
  notes: z.string().optional().or(z.literal('')),
});

type MemberFormData = z.infer<typeof memberFormSchema>;

interface MemberFormProps {
  member?: MemberRead;
  onSubmit: (data: MemberCreate | MemberUpdate) => void;
  isLoading?: boolean;
  mode: 'create' | 'edit';
}

export function MemberForm({
  member,
  onSubmit,
  isLoading = false,
  mode,
}: MemberFormProps) {
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<MemberFormData>({
    resolver: zodResolver(memberFormSchema),
    defaultValues: {
      name: member?.name || '',
      phone: member?.phone || '',
      email: member?.email || '',
      gender: member?.gender || null,
      birthday: member?.birthday || '',
      member_type: member?.member_type || MemberType.TRIAL,
      member_status: member?.member_status || MemberStatus.ACTIVE,
      source_channel: member?.source_channel || '',
      address: member?.address || '',
      city: member?.city || '',
      province: member?.province || '',
      postal_code: member?.postal_code || '',
      notes: member?.notes || '',
    },
  });

  const watchedGender = watch('gender');
  const watchedMemberType = watch('member_type');
  const watchedMemberStatus = watch('member_status');

  const handleFormSubmit = (data: MemberFormData) => {
    // 清理空字符串，转换为 null
    const cleanedData = {
      ...data,
      email: data.email || null,
      gender: data.gender || null,
      birthday: data.birthday || null,
      source_channel: data.source_channel || null,
      address: data.address || null,
      city: data.city || null,
      province: data.province || null,
      postal_code: data.postal_code || null,
      notes: data.notes || null,
    };

    // 如果是创建模式，只提交创建需要的字段
    if (mode === 'create') {
      // 创建会员时不需要这些字段，解构出来不使用
      const createData = {
        name: cleanedData.name,
        phone: cleanedData.phone,
        email: cleanedData.email,
        gender: cleanedData.gender,
        birthday: cleanedData.birthday,
        member_type: cleanedData.member_type,
        source_channel: cleanedData.source_channel,
        notes: cleanedData.notes,
      };
      onSubmit(createData as MemberCreate);
    } else {
      onSubmit(cleanedData as MemberUpdate);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{mode === 'create' ? '新增会员' : '编辑会员'}</CardTitle>
        <CardDescription>
          {mode === 'create' ? '填写以下信息创建新会员' : '修改会员信息'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 姓名 */}
            <div className="space-y-2">
              <Label htmlFor="name">姓名 *</Label>
              <Input
                id="name"
                {...register('name')}
                placeholder="请输入会员姓名"
                className={errors.name ? 'border-destructive' : ''}
              />
              {errors.name && (
                <p className="text-sm text-destructive">{errors.name.message}</p>
              )}
            </div>

            {/* 手机号 */}
            <div className="space-y-2">
              <Label htmlFor="phone">手机号 *</Label>
              <Input
                id="phone"
                {...register('phone')}
                placeholder="请输入手机号码"
                className={errors.phone ? 'border-destructive' : ''}
              />
              {errors.phone && (
                <p className="text-sm text-destructive">{errors.phone.message}</p>
              )}
            </div>

            {/* 邮箱 */}
            <div className="space-y-2">
              <Label htmlFor="email">邮箱</Label>
              <Input
                id="email"
                type="email"
                {...register('email')}
                placeholder="请输入邮箱"
                className={errors.email ? 'border-destructive' : ''}
              />
              {errors.email && (
                <p className="text-sm text-destructive">{errors.email.message}</p>
              )}
            </div>

            {/* 性别 */}
            <div className="space-y-2">
              <Label>性别</Label>
              <Select
                value={watchedGender || ''}
                onValueChange={(value: Gender) => setValue('gender', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="请选择性别" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="male">男</SelectItem>
                  <SelectItem value="female">女</SelectItem>
                  <SelectItem value="other">其他</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* 生日 */}
            <div className="space-y-2">
              <Label htmlFor="birthday">生日</Label>
              <Input id="birthday" type="date" {...register('birthday')} />
            </div>

            {/* 会员类型 */}
            <div className="space-y-2">
              <Label>会员类型</Label>
              <Select
                value={watchedMemberType}
                onValueChange={(value: MemberType) =>
                  setValue('member_type', value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="请选择会员类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={MemberType.TRIAL}>试听</SelectItem>
                  <SelectItem value={MemberType.FORMAL}>正式</SelectItem>
                  <SelectItem value={MemberType.VIP}>VIP</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* 来源渠道 */}
            <div className="space-y-2">
              <Label htmlFor="source_channel">来源渠道</Label>
              <Input
                id="source_channel"
                {...register('source_channel')}
                placeholder="请输入来源渠道"
              />
            </div>

            {mode === 'edit' && (
              <>
                {/* 会员状态 - 仅编辑模式显示 */}
                <div className="space-y-2">
                  <Label>会员状态</Label>
                  <Select
                    value={watchedMemberStatus}
                    onValueChange={(value: MemberStatus) =>
                      setValue('member_status', value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="请选择会员状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={MemberStatus.ACTIVE}>活跃</SelectItem>
                      <SelectItem value={MemberStatus.SILENT}>沉默</SelectItem>
                      <SelectItem value={MemberStatus.FROZEN}>冻结</SelectItem>
                      <SelectItem value={MemberStatus.CANCELLED}>
                        已取消
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* 地址 - 仅编辑模式显示 */}
                <div className="space-y-2">
                  <Label htmlFor="address">地址</Label>
                  <Input
                    id="address"
                    {...register('address')}
                    placeholder="请输入地址"
                  />
                </div>

                {/* 城市 - 仅编辑模式显示 */}
                <div className="space-y-2">
                  <Label htmlFor="city">城市</Label>
                  <Input
                    id="city"
                    {...register('city')}
                    placeholder="请输入城市"
                  />
                </div>

                {/* 省份 - 仅编辑模式显示 */}
                <div className="space-y-2">
                  <Label htmlFor="province">省份</Label>
                  <Input
                    id="province"
                    {...register('province')}
                    placeholder="请输入省份"
                  />
                </div>

                {/* 邮编 - 仅编辑模式显示 */}
                <div className="space-y-2">
                  <Label htmlFor="postal_code">邮编</Label>
                  <Input
                    id="postal_code"
                    {...register('postal_code')}
                    placeholder="请输入邮编"
                  />
                </div>
              </>
            )}
          </div>

          {/* 备注 */}
          <div className="space-y-2">
            <Label htmlFor="notes">备注</Label>
            <Textarea
              id="notes"
              {...register('notes')}
              placeholder="请输入备注信息"
              rows={4}
            />
          </div>

          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => window.history.back()}
            >
              取消
            </Button>
            <Button type="submit" disabled={isLoading} variant="default">
              {isLoading ? '保存中...' : mode === 'create' ? '创建会员' : '保存修改'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
