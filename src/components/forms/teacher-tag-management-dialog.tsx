// 教师标签管理弹框组件

'use client';

import { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { FormDialog, FormField } from '@/components/ui/form-dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, X, Plus } from 'lucide-react';
import { useTeacherTags, useAssignTeacherTags, useRemoveTeacherTags } from '@/hooks/use-teachers';
import { useTags, useTagCategories } from '@/hooks/use-tags';
import { TagStatus } from '@/types/api';
import type { TeacherList, TagWithTeacherCount } from '@/types/api';

interface TeacherTagManagementDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  teacher: TeacherList | null;
}

export function TeacherTagManagementDialog({
  open,
  onO<PERSON><PERSON><PERSON><PERSON>,
  teacher,
}: TeacherTagManagementDialogProps) {
  const [searchKeyword, setSearchKeyword] = useState('');
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>('all');
  const [selectedTagIds, setSelectedTagIds] = useState<number[]>([]);
  const [currentTeacherTagIds, setCurrentTeacherTagIds] = useState<number[]>([]);

  // API hooks
  const { refetch: refetchTeacherTags } = useTeacherTags(teacher?.id.toString() || '');
  const { data: allTagsData } = useTags({ status: TagStatus.ACTIVE });
  const { data: categoriesData } = useTagCategories({});
  const assignTagsMutation = useAssignTeacherTags();
  const removeTagsMutation = useRemoveTeacherTags();

  // 重置状态
  useEffect(() => {
    if (open && teacher) {
      setSearchKeyword('');
      setSelectedCategoryId('all');
      setSelectedTagIds([]);
      
      // 获取当前教师的标签ID列表
      const currentTagIds = teacher.tags?.map(tag => tag.id) || [];
      setCurrentTeacherTagIds(currentTagIds);
      setSelectedTagIds(currentTagIds);
    }
  }, [open, teacher]);

  // 筛选标签
  const filteredTags = allTagsData?.data?.filter((tag: TagWithTeacherCount) => {
    const matchesSearch = !searchKeyword || tag.name.toLowerCase().includes(searchKeyword.toLowerCase());
    const matchesCategory = selectedCategoryId === 'all' || tag.category_id?.toString() === selectedCategoryId;
    return matchesSearch && matchesCategory;
  }) || [];

  // 处理标签选择
  const handleTagToggle = (tagId: number, checked: boolean) => {
    if (checked) {
      setSelectedTagIds(prev => [...prev, tagId]);
    } else {
      setSelectedTagIds(prev => prev.filter(id => id !== tagId));
    }
  };

  // 全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const allFilteredTagIds = filteredTags.map(tag => tag.id);
      setSelectedTagIds(prev => {
        const newIds = [...prev];
        allFilteredTagIds.forEach(id => {
          if (!newIds.includes(id)) {
            newIds.push(id);
          }
        });
        return newIds;
      });
    } else {
      const filteredTagIds = filteredTags.map(tag => tag.id);
      setSelectedTagIds(prev => prev.filter(id => !filteredTagIds.includes(id)));
    }
  };

  // 快速添加标签
  const handleQuickAdd = (tagId: number) => {
    if (!selectedTagIds.includes(tagId)) {
      setSelectedTagIds(prev => [...prev, tagId]);
    }
  };

  // 快速移除标签
  const handleQuickRemove = (tagId: number) => {
    setSelectedTagIds(prev => prev.filter(id => id !== tagId));
  };

  // 保存标签分配
  const handleSave = async () => {
    if (!teacher) return;

    try {
      // 计算需要添加和移除的标签
      const toAdd = selectedTagIds.filter(id => !currentTeacherTagIds.includes(id));
      const toRemove = currentTeacherTagIds.filter(id => !selectedTagIds.includes(id));

      // 执行添加操作
      if (toAdd.length > 0) {
        await assignTagsMutation.mutateAsync({
          id: teacher.id.toString(),
          data: { tag_ids: toAdd }
        });
      }

      // 执行移除操作
      if (toRemove.length > 0) {
        await removeTagsMutation.mutateAsync({
          id: teacher.id.toString(),
          tagIds: toRemove
        });
      }

      // 刷新教师标签数据
      await refetchTeacherTags();
      
      onOpenChange(false);
    } catch (error) {
      console.error('标签管理失败:', error);
    }
  };

  // 获取标签显示信息
  const getTagInfo = (tagId: number) => {
    return allTagsData?.data?.find((tag: TagWithTeacherCount) => tag.id === tagId);
  };

  const isLoading = assignTagsMutation.isPending || removeTagsMutation.isPending;

  return (
    <FormDialog
      open={open}
      onOpenChange={onOpenChange}
      title={`管理教师标签 - ${teacher?.name}`}
      size="lg"
      onConfirm={handleSave}
      confirmText="保存"
      isLoading={isLoading}
    >
      <div className="space-y-6">
        {/* 当前标签 */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium">当前标签</h3>
          <div className="min-h-[60px] p-3 border rounded-lg bg-muted/20">
            {selectedTagIds.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                {selectedTagIds.map(tagId => {
                  const tagInfo = getTagInfo(tagId);
                  return tagInfo ? (
                    <Badge
                      key={tagId}
                      variant="secondary"
                      className="flex items-center gap-1"
                    >
                      {tagInfo.name}
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-4 w-4 p-0 hover:bg-transparent"
                        onClick={() => handleQuickRemove(tagId)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  ) : null;
                })}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">暂无标签</p>
            )}
          </div>
        </div>

        {/* 搜索和筛选 */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium">选择标签</h3>
          
          <div className="grid grid-cols-2 gap-3">
            <FormField label="搜索标签">
              <div className="relative">
                <Search className="absolute left-2.5 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="搜索标签名称..."
                  value={searchKeyword}
                  onChange={e => setSearchKeyword(e.target.value)}
                  className="pl-8"
                />
              </div>
            </FormField>

            <FormField label="标签分类">
              <Select
                value={selectedCategoryId}
                onValueChange={setSelectedCategoryId}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择分类" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部分类</SelectItem>
                  {categoriesData?.data?.map((category) => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormField>
          </div>
        </div>

        {/* 标签列表 */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">可选标签</span>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="select-all"
                checked={filteredTags.length > 0 && filteredTags.every(tag => selectedTagIds.includes(tag.id))}
                onCheckedChange={handleSelectAll}
              />
              <label htmlFor="select-all" className="text-sm">
                全选当前筛选结果
              </label>
            </div>
          </div>

          <div className="max-h-60 overflow-y-auto border rounded-lg">
            {filteredTags.length > 0 ? (
              <div className="p-3 space-y-2">
                {filteredTags.map((tag: TagWithTeacherCount) => (
                  <div
                    key={tag.id}
                    className="flex items-center justify-between p-2 rounded hover:bg-muted/50"
                  >
                    <div className="flex items-center space-x-3">
                      <Checkbox
                        id={`tag-${tag.id}`}
                        checked={selectedTagIds.includes(tag.id)}
                        onCheckedChange={(checked) => handleTagToggle(tag.id, checked as boolean)}
                      />
                      <div>
                        <label htmlFor={`tag-${tag.id}`} className="text-sm font-medium cursor-pointer">
                          {tag.name}
                        </label>
                        {tag.category_name && (
                          <p className="text-xs text-muted-foreground">
                            {tag.category_name}
                          </p>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline" className="text-xs">
                        {tag.teacher_count}个教师
                      </Badge>
                      {!selectedTagIds.includes(tag.id) && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => handleQuickAdd(tag.id)}
                          className="h-6 w-6 p-0"
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="p-6 text-center text-sm text-muted-foreground">
                {searchKeyword || selectedCategoryId !== 'all' 
                  ? '没有找到符合条件的标签' 
                  : '暂无可用标签'
                }
              </div>
            )}
          </div>
        </div>

        {/* 统计信息 */}
        <div className="text-xs text-muted-foreground">
          已选择 {selectedTagIds.length} 个标签，
          新增 {selectedTagIds.filter(id => !currentTeacherTagIds.includes(id)).length} 个，
          移除 {currentTeacherTagIds.filter(id => !selectedTagIds.includes(id)).length} 个
        </div>
      </div>
    </FormDialog>
  );
}
