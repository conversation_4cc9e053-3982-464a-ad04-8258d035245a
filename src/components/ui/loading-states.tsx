'use client';

import { Loader2, File<PERSON>, <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  className?: string;
}

export function LoadingSpinner({ 
  size = 'md', 
  text = '加载中...', 
  className 
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
  };

  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
  };

  return (
    <div className={cn('flex flex-col items-center justify-center space-y-2', className)}>
      <Loader2 className={cn('animate-spin text-primary', sizeClasses[size])} />
      <span className={cn('text-muted-foreground', textSizeClasses[size])}>
        {text}
      </span>
    </div>
  );
}

interface EmptyStateProps {
  icon?: React.ComponentType<{ className?: string }>;
  title?: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  className?: string;
}

export function EmptyState({
  icon: Icon = FileX,
  title = '暂无数据',
  description,
  action,
  className,
}: EmptyStateProps) {
  return (
    <div className={cn('flex flex-col items-center justify-center space-y-3 py-8', className)}>
      <Icon className="h-12 w-12 text-muted-foreground" />
      <div className="text-center space-y-1">
        <h3 className="text-sm font-medium text-foreground">{title}</h3>
        {description && (
          <p className="text-xs text-muted-foreground max-w-sm">{description}</p>
        )}
      </div>
      {action && (
        <Button variant="outline" size="sm" onClick={action.onClick}>
          {action.label}
        </Button>
      )}
    </div>
  );
}

interface ErrorStateProps {
  title?: string;
  description?: string;
  onRetry?: () => void;
  className?: string;
}

export function ErrorState({
  title = '加载失败',
  description = '数据加载时出现错误，请稍后重试',
  onRetry,
  className,
}: ErrorStateProps) {
  return (
    <div className={cn('flex flex-col items-center justify-center space-y-3 py-8', className)}>
      <AlertTriangle className="h-12 w-12 text-destructive" />
      <div className="text-center space-y-1">
        <h3 className="text-sm font-medium text-foreground">{title}</h3>
        <p className="text-xs text-muted-foreground max-w-sm">{description}</p>
      </div>
      {onRetry && (
        <Button variant="outline" size="sm" onClick={onRetry}>
          <RefreshCw className="h-3 w-3 mr-1" />
          重试
        </Button>
      )}
    </div>
  );
}

interface DataStateWrapperProps {
  isLoading?: boolean;
  isError?: boolean;
  isEmpty?: boolean;
  loadingText?: string;
  emptyTitle?: string;
  emptyDescription?: string;
  errorTitle?: string;
  errorDescription?: string;
  onRetry?: () => void;
  onEmptyAction?: () => void;
  emptyActionLabel?: string;
  children: React.ReactNode;
  className?: string;
}

export function DataStateWrapper({
  isLoading = false,
  isError = false,
  isEmpty = false,
  loadingText,
  emptyTitle,
  emptyDescription,
  errorTitle,
  errorDescription,
  onRetry,
  onEmptyAction,
  emptyActionLabel,
  children,
  className,
}: DataStateWrapperProps) {
  if (isLoading) {
    return (
      <div className={cn('min-h-[200px] flex items-center justify-center', className)}>
        <LoadingSpinner text={loadingText} />
      </div>
    );
  }

  if (isError) {
    return (
      <div className={cn('min-h-[200px] flex items-center justify-center', className)}>
        <ErrorState
          title={errorTitle}
          description={errorDescription}
          onRetry={onRetry}
        />
      </div>
    );
  }

  if (isEmpty) {
    return (
      <div className={cn('min-h-[200px] flex items-center justify-center', className)}>
        <EmptyState
          title={emptyTitle}
          description={emptyDescription}
          action={onEmptyAction ? {
            label: emptyActionLabel || '添加数据',
            onClick: onEmptyAction,
          } : undefined}
        />
      </div>
    );
  }

  return <>{children}</>;
}

interface LoadingCardProps {
  title?: string;
  description?: string;
  className?: string;
}

export function LoadingCard({ title, description, className }: LoadingCardProps) {
  return (
    <Card className={className}>
      <CardContent className="p-6">
        <div className="space-y-4">
          {title && (
            <div className="space-y-2">
              <div className="h-5 bg-muted animate-pulse rounded w-1/3" />
              {description && <div className="h-4 bg-muted animate-pulse rounded w-1/2" />}
            </div>
          )}
          <div className="space-y-2">
            <div className="h-4 bg-muted animate-pulse rounded" />
            <div className="h-4 bg-muted animate-pulse rounded w-5/6" />
            <div className="h-4 bg-muted animate-pulse rounded w-4/6" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

interface SkeletonProps {
  className?: string;
  lines?: number;
}

export function Skeleton({ className, lines = 3 }: SkeletonProps) {
  return (
    <div className={cn('space-y-2', className)}>
      {Array.from({ length: lines }).map((_, i) => (
        <div
          key={i}
          className={cn(
            'h-4 bg-muted animate-pulse rounded',
            i === lines - 1 && 'w-4/6',
            i === lines - 2 && 'w-5/6'
          )}
        />
      ))}
    </div>
  );
}
