'use client';

import * as React from 'react';
import { EnhancedFormDialog } from './enhanced-dialogs';
import { Button } from '@/components/ui/button';

export interface FormDialogProps {
  /** 是否显示弹框 */
  open: boolean;
  /** 弹框显示状态变化回调 */
  onOpenChange: (open: boolean) => void;
  /** 弹框标题 */
  title: string;
  /** 弹框描述 */
  description?: string;
  /** 表单内容 */
  children: React.ReactNode;
  /** 确认操作回调 */
  onConfirm: () => void;
  /** 取消操作回调 */
  onCancel?: () => void;
  /** 确认按钮文本 */
  confirmText?: string;
  /** 取消按钮文本 */
  cancelText?: string;
  /** 确认按钮样式变体 */
  variant?: 'default' | 'destructive' | 'success' | 'warning';
  /** 是否处于加载状态 */
  isLoading?: boolean;
  /** 是否禁用确认按钮 */
  disabled?: boolean;
  /** 弹框尺寸 */
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

/**
 * 通用表单弹框组件
 * 
 * 基于 shadcn/ui 的 Dialog 封装，提供统一的表单操作界面
 * 
 * @example
 * ```tsx
 * <FormDialog
 *   open={rechargeOpen}
 *   onOpenChange={setRechargeOpen}
 *   title="会员卡充值"
 *   description="请填写充值信息"
 *   size="md"
 *   onConfirm={handleRecharge}
 *   onCancel={() => setRechargeOpen(false)}
 *   confirmText="确认充值"
 *   isLoading={rechargeMutation.isPending}
 *   disabled={!rechargeAmount}
 * >
 *   <div className="space-y-4">
 *     <FormField label="充值金额" required>
 *       <Input ... />
 *     </FormField>
 *   </div>
 * </FormDialog>
 * ```
 */
export function FormDialog({
  open,
  onOpenChange,
  title,
  description,
  children,
  onConfirm,
  onCancel, // eslint-disable-line @typescript-eslint/no-unused-vars
  confirmText = '确认',
  cancelText = '取消',
  variant = 'default',
  isLoading = false,
  disabled = false,
  size = 'md',
}: FormDialogProps) {
  // 转换variant类型以兼容EnhancedFormDialog
  const enhancedVariant = variant === 'warning' ? 'default' : variant;
  // 转换size类型以兼容EnhancedFormDialog
  const enhancedSize = size === 'xl' ? 'lg' : size;

  return (
    <EnhancedFormDialog
      open={open}
      onOpenChange={onOpenChange}
      title={title}
      description={description}
      onConfirm={onConfirm}
      confirmText={confirmText}
      cancelText={cancelText}
      variant={enhancedVariant}
      isLoading={isLoading}
      disabled={disabled}
      size={enhancedSize}
    >
      {children}
    </EnhancedFormDialog>
  );
}

/**
 * 表单字段组件
 * 
 * 提供统一的表单字段布局
 */
export interface FormFieldProps {
  /** 字段标签 */
  label: string;
  /** 是否必填 */
  required?: boolean;
  /** 错误信息 */
  error?: string;
  /** 字段内容 */
  children: React.ReactNode;
  /** 帮助文本 */
  help?: React.ReactNode;
}

export function FormField({
  label,
  required = false,
  error,
  children,
  help,
}: FormFieldProps) {
  return (
    <div className="grid grid-cols-4 items-center gap-4">
      <span className="text-right">
        {label}
        {required && <span className="text-destructive ml-1">*</span>}
      </span>
      <div className="col-span-3">
        {children}
        {help && (
          <p className="text-xs text-muted-foreground mt-1">{help}</p>
        )}
        {error && (
          <p className="text-xs text-destructive mt-1">{error}</p>
        )}
      </div>
    </div>
  );
}

/**
 * 快捷按钮组件
 * 
 * 用于表单中的快捷操作按钮
 */
export interface QuickButtonsProps {
  /** 按钮配置 */
  buttons: Array<{
    label: string;
    onClick: () => void;
  }>;
  /** 按钮组标题 */
  title?: string;
}

export function QuickButtons({ buttons, title }: QuickButtonsProps) {
  return (
    <div className="space-y-2">
      {title && (
        <p className="text-xs text-muted-foreground">{title}</p>
      )}
      <div className="flex gap-2 flex-wrap">
        {buttons.map((button, index) => (
          <Button
            key={index}
            type="button"
            variant="outline"
            size="xs"
            onClick={button.onClick}
          >
            {button.label}
          </Button>
        ))}
      </div>
    </div>
  );
}
