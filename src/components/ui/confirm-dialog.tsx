'use client';

import * as React from 'react';
import { EnhancedConfirmDialog, StepConfirmDialog } from './enhanced-dialogs';
import type { EnhancedConfirmDialogProps, StepConfirmDialogProps } from './enhanced-dialogs';

export interface ConfirmDialogProps {
  /** 是否显示弹框 */
  open: boolean;
  /** 弹框显示状态变化回调 */
  onOpenChange: (open: boolean) => void;
  /** 弹框标题 */
  title?: string;
  /** 弹框描述内容 */
  description: React.ReactNode;
  /** 确认按钮文本 */
  confirmText?: string;
  /** 取消按钮文本 */
  cancelText?: string;
  /** 确认按钮样式变体 */
  variant?: 'destructive' | 'default';
  /** 确认操作回调 */
  onConfirm: () => void;
  /** 是否处于加载状态 */
  isLoading?: boolean;
  /** 是否禁用确认按钮 */
  disabled?: boolean;
}

/**
 * 通用确认弹框组件
 * 
 * 基于 shadcn/ui 的 AlertDialog 封装，提供统一的确认操作界面
 * 
 * @example
 * ```tsx
 * <ConfirmDialog
 *   open={showDeleteDialog}
 *   onOpenChange={setShowDeleteDialog}
 *   title="确认删除"
 *   description={
 *     <>
 *       确定要删除这个用户吗？
 *       <br />
 *       用户名：<strong>{user.username}</strong>
 *       <br />
 *       此操作无法撤销。
 *     </>
 *   }
 *   variant="destructive"
 *   onConfirm={handleDelete}
 *   isLoading={deleteUserMutation.isPending}
 * />
 * ```
 */
export function ConfirmDialog({
  open,
  onOpenChange,
  title = '确认操作',
  description,
  confirmText = '确认',
  cancelText = '取消',
  variant = 'default',
  onConfirm,
  isLoading = false,
  disabled = false,
}: ConfirmDialogProps) {
  const handleConfirm = () => {
    onConfirm();
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{title}</AlertDialogTitle>
          <AlertDialogDescription asChild>
            <div>{description}</div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={handleCancel} disabled={isLoading}>
            {cancelText}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            variant={variant}
            disabled={isLoading || disabled}
          >
            {isLoading ? '处理中...' : confirmText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

/**
 * 删除确认弹框的预设配置
 * 
 * @example
 * ```tsx
 * <ConfirmDialog
 *   {...createDeleteConfirmProps({
 *     open: showDeleteDialog,
 *     onOpenChange: setShowDeleteDialog,
 *     itemName: user.username,
 *     itemType: '用户',
 *     onConfirm: handleDelete,
 *     isLoading: deleteUserMutation.isPending,
 *   })}
 * />
 * ```
 */
export function createDeleteConfirmProps({
  open,
  onOpenChange,
  itemName,
  itemType = '项目',
  extraInfo,
  onConfirm,
  isLoading = false,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  itemName: string;
  itemType?: string;
  extraInfo?: React.ReactNode;
  onConfirm: () => void;
  isLoading?: boolean;
}): ConfirmDialogProps {
  return {
    open,
    onOpenChange,
    title: '确认删除',
    description: (
      <>
        确定要删除这个{itemType}吗？
        <br />
        {itemType}名称：<strong>{itemName}</strong>
        {extraInfo && (
          <>
            <br />
            {extraInfo}
          </>
        )}
        <br />
        此操作无法撤销。
      </>
    ),
    variant: 'destructive',
    confirmText: isLoading ? '删除中...' : '确认删除',
    onConfirm,
    isLoading,
  };
}
