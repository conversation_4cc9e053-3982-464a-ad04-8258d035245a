'use client';

import * as React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { AlertTriangle, CheckCircle, Info, X } from 'lucide-react';
import { cn } from '@/lib/utils';

// 方案A：渐变头部设计
export interface EnhancedConfirmDialogPropsA {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description: React.ReactNode;
  variant?: 'destructive' | 'warning' | 'success' | 'info';
  confirmText?: string;
  cancelText?: string;
  onConfirm: () => void;
  isLoading?: boolean;
}

export function EnhancedConfirmDialogA({
  open,
  onOpenChange,
  title,
  description,
  variant = 'info',
  confirmText = '确认',
  cancelText = '取消',
  onConfirm,
  isLoading = false,
}: EnhancedConfirmDialogPropsA) {
  const variantConfig = {
    destructive: {
      icon: AlertTriangle,
      headerBg: 'bg-gradient-to-r from-destructive/10 to-destructive/5',
      iconBg: 'bg-destructive/10',
      iconColor: 'text-destructive',
      buttonVariant: 'destructive' as const,
    },
    warning: {
      icon: AlertTriangle,
      headerBg: 'bg-gradient-to-r from-warning/10 to-warning/5',
      iconBg: 'bg-warning/10',
      iconColor: 'text-warning',
      buttonVariant: 'default' as const,
    },
    success: {
      icon: CheckCircle,
      headerBg: 'bg-gradient-to-r from-success/10 to-success/5',
      iconBg: 'bg-success/10',
      iconColor: 'text-success',
      buttonVariant: 'default' as const,
    },
    info: {
      icon: Info,
      headerBg: 'bg-gradient-to-r from-info/10 to-info/5',
      iconBg: 'bg-info/10',
      iconColor: 'text-info',
      buttonVariant: 'default' as const,
    },
  };

  const config = variantConfig[variant];
  const IconComponent = config.icon;

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="p-0 gap-0 max-w-md">
        {/* 渐变头部区域 */}
        <div className={cn('px-6 py-5 border-b', config.headerBg)}>
          <div className="flex items-start gap-4">
            <div className={cn('w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0', config.iconBg)}>
              <IconComponent className={cn('h-6 w-6', config.iconColor)} />
            </div>
            <div className="flex-1 min-w-0">
              <AlertDialogTitle className="text-lg font-semibold leading-tight">
                {title}
              </AlertDialogTitle>
              <AlertDialogDescription className="text-sm text-muted-foreground mt-1 leading-relaxed">
                {description}
              </AlertDialogDescription>
            </div>
          </div>
        </div>

        {/* 操作按钮区域 */}
        <AlertDialogFooter className="px-6 py-4 bg-muted/30">
          <AlertDialogCancel disabled={isLoading} className="mr-2">
            {cancelText}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirm}
            disabled={isLoading}
            className={cn(
              "min-w-[80px]",
              config.buttonVariant === 'destructive'
                ? "bg-destructive text-destructive-foreground hover:bg-destructive/90"
                : ""
            )}
          >
            {isLoading ? '处理中...' : confirmText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

// 方案B：卡片式确认弹框
export function EnhancedConfirmDialogB({
  open,
  onOpenChange,
  title,
  description,
  variant = 'info',
  confirmText = '确认',
  cancelText = '取消',
  onConfirm,
  isLoading = false,
}: EnhancedConfirmDialogPropsA) {
  const variantConfig = {
    destructive: {
      icon: AlertTriangle,
      cardBg: 'bg-destructive/5',
      cardBorder: 'border-destructive/20',
      iconColor: 'text-destructive',
      titleColor: 'text-destructive',
      buttonVariant: 'destructive' as const,
    },
    warning: {
      icon: AlertTriangle,
      cardBg: 'bg-warning/5',
      cardBorder: 'border-warning/20',
      iconColor: 'text-warning',
      titleColor: 'text-warning',
      buttonVariant: 'default' as const,
    },
    success: {
      icon: CheckCircle,
      cardBg: 'bg-success/5',
      cardBorder: 'border-success/20',
      iconColor: 'text-success',
      titleColor: 'text-success',
      buttonVariant: 'default' as const,
    },
    info: {
      icon: Info,
      cardBg: 'bg-info/5',
      cardBorder: 'border-info/20',
      iconColor: 'text-info',
      titleColor: 'text-info',
      buttonVariant: 'default' as const,
    },
  };

  const config = variantConfig[variant];
  const IconComponent = config.icon;

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="p-6 gap-6 max-w-md">
        {/* 卡片式内容区域 */}
        <div className={cn('p-6 rounded-lg border', config.cardBg, config.cardBorder)}>
          <div className="flex items-start gap-4">
            <div className="flex-shrink-0">
              <IconComponent className={cn('h-8 w-8', config.iconColor)} />
            </div>
            <div className="flex-1 min-w-0">
              <AlertDialogTitle className={cn('text-lg font-semibold leading-tight mb-2', config.titleColor)}>
                {title}
              </AlertDialogTitle>
              <AlertDialogDescription className="text-sm text-muted-foreground leading-relaxed">
                {description}
              </AlertDialogDescription>
            </div>
          </div>
        </div>

        {/* 操作按钮区域 */}
        <AlertDialogFooter className="flex-row justify-end gap-3 p-0">
          <AlertDialogCancel disabled={isLoading} className="m-0">
            {cancelText}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirm}
            variant={config.buttonVariant}
            disabled={isLoading}
            className="min-w-[80px] m-0"
          >
            {isLoading ? '处理中...' : confirmText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

// 方案C：极简线性确认弹框
export function EnhancedConfirmDialogC({
  open,
  onOpenChange,
  title,
  description,
  variant = 'info',
  confirmText = '确认',
  cancelText = '取消',
  onConfirm,
  isLoading = false,
}: EnhancedConfirmDialogPropsA) {
  const variantConfig = {
    destructive: {
      icon: AlertTriangle,
      iconColor: 'text-destructive',
      buttonVariant: 'destructive' as const,
    },
    warning: {
      icon: AlertTriangle,
      iconColor: 'text-warning',
      buttonVariant: 'default' as const,
    },
    success: {
      icon: CheckCircle,
      iconColor: 'text-success',
      buttonVariant: 'default' as const,
    },
    info: {
      icon: Info,
      iconColor: 'text-info',
      buttonVariant: 'default' as const,
    },
  };

  const config = variantConfig[variant];
  const IconComponent = config.icon;

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader className="pb-6">
          <div className="flex items-center gap-3 mb-3">
            <IconComponent className={cn('h-6 w-6', config.iconColor)} />
            <AlertDialogTitle className="text-xl font-semibold tracking-tight">
              {title}
            </AlertDialogTitle>
          </div>
          <AlertDialogDescription className="text-muted-foreground leading-relaxed text-base">
            {description}
          </AlertDialogDescription>
        </AlertDialogHeader>

        <AlertDialogFooter className="pt-6 border-t border-border/50">
          <AlertDialogCancel disabled={isLoading}>
            {cancelText}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirm}
            variant={config.buttonVariant}
            disabled={isLoading}
            className="min-w-[80px]"
          >
            {isLoading ? '处理中...' : confirmText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

// 方案B：卡片式分组设计
export interface EnhancedFormDialogPropsB {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description?: string;
  children: React.ReactNode;
  onConfirm: () => void;
  confirmText?: string;
  cancelText?: string;
  variant?: 'default' | 'destructive' | 'success';
  isLoading?: boolean;
  disabled?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export function EnhancedFormDialogB({
  open,
  onOpenChange,
  title,
  description,
  children,
  onConfirm,
  confirmText = '确认',
  cancelText = '取消',
  variant = 'default',
  isLoading = false,
  disabled = false,
  size = 'md',
}: EnhancedFormDialogPropsB) {
  const sizeClasses = {
    sm: 'sm:max-w-[400px]',
    md: 'sm:max-w-[500px]',
    lg: 'sm:max-w-[600px]',
  };

  const variantConfig = {
    default: {
      headerBg: 'bg-gradient-to-r from-primary/5 to-primary/10',
      buttonVariant: 'default' as const,
    },
    destructive: {
      headerBg: 'bg-gradient-to-r from-destructive/5 to-destructive/10',
      buttonVariant: 'destructive' as const,
    },
    success: {
      headerBg: 'bg-gradient-to-r from-success/5 to-success/10',
      buttonVariant: 'default' as const,
    },
  };

  const config = variantConfig[variant];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className={cn('p-0 gap-0', sizeClasses[size])}>
        {/* 头部区域 */}
        <div className={cn('px-6 py-4 border-b', config.headerBg)}>
          <DialogHeader className="space-y-1">
            <DialogTitle className="text-lg font-semibold">{title}</DialogTitle>
            {description && (
              <DialogDescription className="text-sm text-muted-foreground">
                {description}
              </DialogDescription>
            )}
          </DialogHeader>
        </div>

        {/* 内容区域 - 卡片式布局 */}
        <div className="px-6 py-4 max-h-[60vh] overflow-y-auto">
          <div className="bg-card border rounded-lg p-4 shadow-sm">
            {children}
          </div>
        </div>

        {/* 底部操作区域 */}
        <DialogFooter className="px-6 py-4 bg-muted/20 border-t">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            {cancelText}
          </Button>
          <Button
            variant={config.buttonVariant}
            onClick={onConfirm}
            disabled={isLoading || disabled}
            className="min-w-[80px]"
          >
            {isLoading ? '处理中...' : confirmText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// 方案C：极简分割线设计
export function EnhancedFormDialogC({
  open,
  onOpenChange,
  title,
  description,
  children,
  onConfirm,
  confirmText = '确认',
  cancelText = '取消',
  variant = 'default',
  isLoading = false,
  disabled = false,
  size = 'md',
}: EnhancedFormDialogPropsB) {
  const sizeClasses = {
    sm: 'sm:max-w-[400px]',
    md: 'sm:max-w-[500px]',
    lg: 'sm:max-w-[600px]',
  };

  const getButtonVariant = () => {
    switch (variant) {
      case 'destructive':
        return 'destructive';
      case 'success':
        return 'default'; // 可以后续扩展为 success 变体
      default:
        return 'default';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className={cn('gap-0', sizeClasses[size])}>
        {/* 头部区域 - 增加底部边框 */}
        <DialogHeader className="pb-4 border-b border-border/50">
          <DialogTitle className="text-xl font-semibold tracking-tight">
            {title}
          </DialogTitle>
          {description && (
            <DialogDescription className="text-muted-foreground leading-relaxed">
              {description}
            </DialogDescription>
          )}
        </DialogHeader>

        {/* 内容区域 - 增加垂直间距 */}
        <div className="py-6 max-h-[60vh] overflow-y-auto">
          {children}
        </div>

        {/* 底部操作区域 - 增加顶部边框 */}
        <DialogFooter className="pt-4 border-t border-border/50">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
            className="mr-2"
          >
            {cancelText}
          </Button>
          <Button
            variant={getButtonVariant()}
            onClick={onConfirm}
            disabled={isLoading || disabled}
            className="min-w-[80px]"
          >
            {isLoading ? '处理中...' : confirmText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
