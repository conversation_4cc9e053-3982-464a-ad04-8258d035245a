'use client';

import {
  AreaChart as Recharts<PERSON>rea<PERSON>hart,
  Area,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON>ltip,
  Responsive<PERSON>ontainer,
  Legend,
} from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

interface DataPoint {
  name: string;
  [key: string]: string | number;
}

interface AreaChartProps {
  title?: string;
  description?: string;
  data: DataPoint[];
  categories: {
    key: string;
    name: string;
    color: string;
  }[];
  xAxisKey?: string;
  showLegend?: boolean;
  showGrid?: boolean;
  className?: string;
  height?: number;
}

export function AreaChart({
  title,
  description,
  data,
  categories,
  xAxisKey = 'name',
  showLegend = true,
  showGrid = true,
  className,
  height = 300,
}: AreaChartProps) {
  return (
    <Card className={className}>
      {(title || description) && (
        <CardHeader>
          {title && <CardTitle>{title}</CardTitle>}
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
      )}
      <CardContent>
        <div style={{ width: '100%', height }}>
          <ResponsiveContainer width="100%" height="100%">
            <RechartsAreaChart
              data={data}
              margin={{
                top: 10,
                right: 30,
                left: 0,
                bottom: 0,
              }}
            >
              {showGrid && <CartesianGrid strokeDasharray="3 3" />}
              <XAxis
                dataKey={xAxisKey}
                tick={{ fontSize: 12 }}
                tickLine={{ stroke: 'hsl(var(--muted-foreground))' }}
                axisLine={{ stroke: 'hsl(var(--muted-foreground))' }}
              />
              <YAxis
                tick={{ fontSize: 12 }}
                tickLine={{ stroke: 'hsl(var(--muted-foreground))' }}
                axisLine={{ stroke: 'hsl(var(--muted-foreground))' }}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'hsl(var(--popover))',
                  borderColor: 'hsl(var(--border))',
                  borderRadius: '8px',
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                  color: 'hsl(var(--popover-foreground))',
                  fontSize: '12px',
                  padding: '8px 12px',
                }}
                labelStyle={{
                  color: 'hsl(var(--popover-foreground))',
                  fontWeight: '500',
                  marginBottom: '4px',
                }}
                itemStyle={{
                  color: 'hsl(var(--popover-foreground))',
                  fontSize: '12px',
                }}
              />
              {showLegend && (
                <Legend
                  wrapperStyle={{
                    fontSize: '12px',
                    color: 'hsl(var(--muted-foreground))',
                  }}
                />
              )}
              {categories.map(category => (
                <Area
                  key={category.key}
                  type="monotone"
                  dataKey={category.key}
                  name={category.name}
                  stroke={category.color}
                  fill={category.color}
                  fillOpacity={0.2}
                />
              ))}
            </RechartsAreaChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
