'use client';

import {
  <PERSON><PERSON><PERSON> as Recha<PERSON>Bar<PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Re<PERSON>onsive<PERSON><PERSON><PERSON>,
  Legend,
} from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

interface DataPoint {
  name: string;
  [key: string]: string | number;
}

interface BarChartProps {
  title?: string;
  description?: string;
  data: DataPoint[];
  categories: {
    key: string;
    name: string;
    color: string;
  }[];
  xAxisKey?: string;
  showLegend?: boolean;
  showGrid?: boolean;
  layout?: 'vertical' | 'horizontal';
  className?: string;
  height?: number;
}

export function BarChart({
  title,
  description,
  data,
  categories,
  xAxisKey = 'name',
  showLegend = true,
  showGrid = true,
  layout = 'horizontal',
  className,
  height = 300,
}: BarChartProps) {
  return (
    <Card className={className}>
      {(title || description) && (
        <CardHeader>
          {title && <CardTitle>{title}</CardTitle>}
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
      )}
      <CardContent>
        <div style={{ width: '100%', height }}>
          <ResponsiveContainer width="100%" height="100%">
            <RechartsBarChart
              data={data}
              layout={layout}
              margin={{
                top: 10,
                right: 30,
                left: 0,
                bottom: 0,
              }}
            >
              {showGrid && <CartesianGrid strokeDasharray="3 3" />}
              <XAxis
                dataKey={layout === 'horizontal' ? xAxisKey : undefined}
                type={layout === 'horizontal' ? 'category' : 'number'}
                tick={{ fontSize: 12 }}
                tickLine={{ stroke: 'hsl(var(--muted-foreground))' }}
                axisLine={{ stroke: 'hsl(var(--muted-foreground))' }}
              />
              <YAxis
                dataKey={layout === 'vertical' ? xAxisKey : undefined}
                type={layout === 'vertical' ? 'category' : 'number'}
                tick={{ fontSize: 12 }}
                tickLine={{ stroke: 'hsl(var(--muted-foreground))' }}
                axisLine={{ stroke: 'hsl(var(--muted-foreground))' }}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'hsl(var(--popover))',
                  borderColor: 'hsl(var(--border))',
                  borderRadius: '8px',
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                  color: 'hsl(var(--popover-foreground))',
                  fontSize: '12px',
                  padding: '8px 12px',
                }}
                labelStyle={{
                  color: 'hsl(var(--popover-foreground))',
                  fontWeight: '500',
                  marginBottom: '4px',
                }}
                itemStyle={{
                  color: 'hsl(var(--popover-foreground))',
                  fontSize: '12px',
                }}
              />
              {showLegend && (
                <Legend
                  wrapperStyle={{
                    fontSize: '12px',
                    color: 'hsl(var(--muted-foreground))',
                  }}
                />
              )}
              {categories.map(category => (
                <Bar
                  key={category.key}
                  dataKey={category.key}
                  name={category.name}
                  fill={category.color}
                  radius={[4, 4, 0, 0]}
                />
              ))}
            </RechartsBarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
