'use client';

import {
  <PERSON><PERSON><PERSON> as Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Toolt<PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  Legend,
} from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

interface DataPoint {
  name: string;
  value: number;
  color: string;
}

interface PieChartProps {
  title?: string;
  description?: string;
  data: DataPoint[];
  showLegend?: boolean;
  innerRadius?: number;
  outerRadius?: number;
  className?: string;
  height?: number;
}

export function PieChart({
  title,
  description,
  data,
  showLegend = true,
  innerRadius = 0,
  outerRadius = 80,
  className,
  height = 300,
}: PieChartProps) {
  return (
    <Card className={className}>
      {(title || description) && (
        <CardHeader>
          {title && <CardTitle>{title}</CardTitle>}
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
      )}
      <CardContent>
        <div style={{ width: '100%', height }}>
          <ResponsiveContainer width="100%" height="100%">
            <RechartsPieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                labelLine={false}
                innerRadius={innerRadius}
                outerRadius={outerRadius}
                fill="#8884d8"
                dataKey="value"
                label={({ name, percent }) =>
                  `${name}: ${percent ? (percent * 100).toFixed(0) : 0}%`
                }
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip
                formatter={value => [`${value}`, 'Value']}
                contentStyle={{
                  backgroundColor: 'hsl(var(--popover))',
                  borderColor: 'hsl(var(--border))',
                  borderRadius: '8px',
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                  color: 'hsl(var(--popover-foreground))',
                  fontSize: '12px',
                  padding: '8px 12px',
                }}
                labelStyle={{
                  color: 'hsl(var(--popover-foreground))',
                  fontWeight: '500',
                  marginBottom: '4px',
                }}
                itemStyle={{
                  color: 'hsl(var(--popover-foreground))',
                  fontSize: '12px',
                }}
              />
              {showLegend && (
                <Legend
                  wrapperStyle={{
                    fontSize: '12px',
                    color: 'hsl(var(--muted-foreground))',
                  }}
                />
              )}
            </RechartsPieChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
