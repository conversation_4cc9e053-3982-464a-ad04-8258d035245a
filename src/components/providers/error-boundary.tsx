'use client';

import React, { ErrorInfo } from 'react';
import { ErrorBoundary, FallbackProps } from 'react-error-boundary';
import { toast } from 'sonner';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { env } from '@/config/env';

/**
 * 错误回退UI组件 - 当组件渲染出错时显示
 */
function ErrorFallback({ error, resetErrorBoundary }: FallbackProps) {
  const isDevelopment = env.NODE_ENV === 'development';

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-gray-50 dark:bg-gray-900">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
            <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
          </div>
          <CardTitle className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            出现了一些问题
          </CardTitle>
          <CardDescription className="text-gray-600 dark:text-gray-400">
            页面遇到了意外错误，请尝试刷新页面或返回首页
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* 开发环境显示错误详情 */}
          {isDevelopment && (
            <div className="rounded-md bg-red-50 dark:bg-red-900/10 p-3">
              <h4 className="text-sm font-medium text-red-800 dark:text-red-400 mb-2">
                错误详情 (仅开发环境显示)
              </h4>
              <pre className="text-xs text-red-700 dark:text-red-300 whitespace-pre-wrap break-words">
                {error.message}
              </pre>
              {error.stack && (
                <details className="mt-2">
                  <summary className="text-xs text-red-600 dark:text-red-400 cursor-pointer">
                    查看堆栈信息
                  </summary>
                  <pre className="text-xs text-red-600 dark:text-red-400 mt-1 whitespace-pre-wrap break-words">
                    {error.stack}
                  </pre>
                </details>
              )}
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex flex-col sm:flex-row gap-3">
            <Button 
              onClick={resetErrorBoundary}
              className="flex-1"
              variant="default"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              重试
            </Button>
            <Button 
              onClick={() => window.location.href = '/dashboard'}
              variant="outline"
              className="flex-1"
            >
              <Home className="h-4 w-4 mr-2" />
              返回首页
            </Button>
          </div>

          {/* 联系支持 */}
          <div className="text-center text-sm text-gray-500 dark:text-gray-400">
            如果问题持续存在，请联系技术支持
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * 错误处理函数 - 记录错误并显示通知
 */
function handleError(error: Error, errorInfo: ErrorInfo) {
  // 记录错误到控制台
  console.error('🚨 Global Error Boundary caught an error:', error);
  console.error('Component Stack:', errorInfo.componentStack);

  // 显示用户友好的错误提示
  toast.error('页面出现异常，请刷新重试');

  // 在生产环境中，这里可以集成错误监控服务
  if (env.NODE_ENV === 'production') {
    // TODO: 集成 Sentry、LogRocket 等错误监控服务
    // Sentry.captureException(error, {
    //   contexts: {
    //     react: {
    //       componentStack: errorInfo.componentStack,
    //     },
    //   },
    // });
  }
}

/**
 * 全局错误边界组件
 * 捕获子组件树中的JavaScript错误，显示错误UI，防止整个应用崩溃
 */
export function GlobalErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onError={handleError}
      onReset={() => {
        // 重置时清除可能的错误状态
        window.location.reload();
      }}
    >
      {children}
    </ErrorBoundary>
  );
}

/**
 * 页面级错误边界组件
 * 用于包装单个页面或功能模块，提供更细粒度的错误处理
 */
interface PageErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<FallbackProps>;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

export function PageErrorBoundary({ 
  children, 
  fallback: CustomFallback,
  onError 
}: PageErrorBoundaryProps) {
  const FallbackComponent = CustomFallback || ErrorFallback;

  return (
    <ErrorBoundary
      FallbackComponent={FallbackComponent}
      onError={(error, errorInfo) => {
        // 执行自定义错误处理
        onError?.(error, errorInfo);
        
        // 执行默认错误处理
        handleError(error, errorInfo);
      }}
    >
      {children}
    </ErrorBoundary>
  );
}

/**
 * 简单的错误回退组件 - 用于小型组件或非关键功能
 */
export function SimpleErrorFallback({ error, resetErrorBoundary }: FallbackProps) {
  return (
    <div className="flex flex-col items-center justify-center p-6 text-center border border-red-200 dark:border-red-800 rounded-lg bg-red-50 dark:bg-red-900/10">
      <AlertTriangle className="h-8 w-8 text-red-500 mb-2" />
      <h3 className="text-sm font-medium text-red-800 dark:text-red-400 mb-2">
        组件加载失败
      </h3>
      <p className="text-xs text-red-600 dark:text-red-500 mb-3">
        {error.message}
      </p>
      <Button 
        onClick={resetErrorBoundary}
        size="sm"
        variant="outline"
        className="text-xs"
      >
        重新加载
      </Button>
    </div>
  );
}
