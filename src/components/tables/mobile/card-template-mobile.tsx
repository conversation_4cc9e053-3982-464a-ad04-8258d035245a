'use client';

import { MoreH<PERSON>zontal, CheckCircle, XCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  MobileCardList, 
  MobileCardRenderer, 
  MobileCardHeader, 
  MobileCardGrid,
  MobileCardInfo
} from '@/components/tables/common/mobile-card-list';
import { CardTemplateList, CardType, CardTypeNames } from '@/types/api';
import { formatDate } from '@/lib/utils';

interface CardTemplateMobileProps {
  data: CardTemplateList[];
  onEdit: (id: number) => void;
  onDelete: (id: number) => void;
  onToggleStatus: (id: number) => void;
  loading?: boolean;
  emptyState?: React.ReactNode;
}

export function CardTemplateMobile({
  data,
  onEdit,
  onDelete,
  onToggleStatus,
  loading = false,
  emptyState,
}: CardTemplateMobileProps) {
  // 定义卡片渲染器
  const renderer: MobileCardRenderer<CardTemplateList> = {
    renderHeader: (template) => (
      <MobileCardHeader
        title={template.name}
        badges={[
          <Badge key="type" variant="outline" className="text-xs">
            {CardTypeNames[template.card_type as CardType] || template.card_type}
          </Badge>,
          <Badge 
            key="status"
            variant={template.is_active ? 'default' : 'secondary'}
            className="text-xs"
          >
            {template.is_active ? '启用' : '停用'}
          </Badge>
        ]}
        actions={
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-9 w-9 p-0 touch-manipulation">
                <MoreHorizontal className="h-5 w-5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>操作</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => onEdit(template.id)}
                className="cursor-pointer"
              >
                编辑
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => onToggleStatus(template.id)}
                className="cursor-pointer"
              >
                {template.is_active ? '停用' : '启用'}
              </DropdownMenuItem>
              <DropdownMenuItem
                className="text-destructive focus:text-destructive cursor-pointer"
                onClick={() => onDelete(template.id)}
              >
                删除
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        }
      />
    ),

    renderContent: (template) => (
      <MobileCardGrid
        items={[
          {
            label: '售价',
            value: `¥${template.sale_price.toFixed(2)}`,
          },
          {
            label: '可用余额/次数',
            value: template.available_balance === null || template.available_balance === undefined ? (
              <span className="text-muted-foreground">不限</span>
            ) : (
              <>
                {template.available_balance}{' '}
                {(template.card_type === CardType.TIMES_LIMITED || 
                  template.card_type === CardType.TIMES_UNLIMITED) ? '次' : '元'}
              </>
            ),
          },
          {
            label: '有效期',
            value: template.validity_days ? `${template.validity_days}天` : (
              <span className="text-muted-foreground">不限</span>
            ),
          },
          {
            label: '代理专售',
            value: (
              <div className="flex items-center">
                {template.is_agent_exclusive ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="h-4 w-4 text-muted-foreground" />
                )}
              </div>
            ),
          },
        ]}
      />
    ),

    renderFooter: (template) => (
      <MobileCardInfo
        label="创建时间"
        value={formatDate(template.created_at)}
      />
    ),
  };

  return (
    <MobileCardList
      data={data}
      renderer={renderer}
      loading={loading}
      emptyState={emptyState}
    />
  );
}
