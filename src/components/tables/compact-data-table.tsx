'use client';

import * as React from 'react';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
} from '@tanstack/react-table';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useState } from 'react';
import { cn } from '@/lib/utils';
import { ArrowUpDown, ArrowUp, ArrowDown, Loader2, FileX } from 'lucide-react';

interface CompactDataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  className?: string;
  isLoading?: boolean;
  // 默认排序配置
  defaultSort?: {
    id: string; // 排序的列id
    desc: boolean; // 是否降序，true为降序，false为升序
  };
}

/**
 * 紧凑型数据表格
 *
 * 适合中国用户习惯的表格样式：
 * - 更小的内边距
 * - 网格线分隔
 * - 表头背景色区分
 * - 紧凑的行高
 */
export function CompactDataTable<TData, TValue>({
  columns,
  data,
  className,
  isLoading = false,
  defaultSort,
}: CompactDataTableProps<TData, TValue>) {
  const [sorting, setSorting] = useState<SortingState>(
    defaultSort ? [{ id: defaultSort.id, desc: defaultSort.desc }] : []
  );

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    state: {
      sorting,
    },
    initialState: {
      pagination: {
        pageSize: 10, // 默认每页显示10条
      },
    },
  });

  return (
    <Card
      className={cn(
        className,
        'border-0 shadow-none rounded-none',
        'bg-transparent'
      )}
    >
      <CardContent className="p-0">
        {/* 表格 */}
        <div>
          <Table className="border-collapse border border-border">
            <TableHeader className="bg-muted/50">
              {table.getHeaderGroups().map(headerGroup => (
                <TableRow
                  key={headerGroup.id}
                  className="border-b hover:bg-transparent"
                >
                  {headerGroup.headers.map(header => {
                    const columnMeta = header.column.columnDef.meta as { className?: string } | undefined;
                    return (
                      <TableHead
                        key={header.id}
                        className={cn(
                          "py-2 px-3 text-xs font-medium text-muted-foreground border-r last:border-r-0",
                          columnMeta?.className
                        )}
                      >
                        {header.isPlaceholder ? null : (
                          <div
                            className={cn(
                              'flex items-center gap-1',
                              header.column.getCanSort() &&
                                'cursor-pointer select-none'
                            )}
                            onClick={header.column.getToggleSortingHandler()}
                          >
                            {flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}

                            {header.column.getCanSort() && (
                              <div className="ml-1 flex-none">
                                {header.column.getIsSorted() === 'asc' ? (
                                  <ArrowUp className="h-3 w-3 text-primary" />
                                ) : header.column.getIsSorted() === 'desc' ? (
                                  <ArrowDown className="h-3 w-3 text-primary" />
                                ) : (
                                  <ArrowUpDown className="h-3 w-3 text-muted-foreground/50 hover:text-muted-foreground" />
                                )}
                              </div>
                            )}
                          </div>
                        )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center border-r-0"
                  >
                    <div className="flex flex-col items-center justify-center space-y-2">
                      <Loader2 className="h-6 w-6 animate-spin text-primary" />
                      <span className="text-xs text-muted-foreground">正在加载数据...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map(row => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && 'selected'}
                    className="border-b last:border-b-0 hover:bg-muted/30"
                  >
                    {row.getVisibleCells().map(cell => {
                      const columnMeta = cell.column.columnDef.meta as { className?: string } | undefined;
                      return (
                        <TableCell
                          key={cell.id}
                          className={cn(
                            "py-2 px-3 text-xs border-r last:border-r-0",
                            columnMeta?.className
                          )}
                        >
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      );
                    })}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center border-r-0"
                  >
                    <div className="flex flex-col items-center justify-center space-y-2">
                      <FileX className="h-6 w-6 text-muted-foreground" />
                      <span className="text-xs text-muted-foreground">暂无数据</span>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        {/* 分页 */}
        <div className="flex items-center justify-between space-x-2 py-3 px-3 bg-muted/20 border-t">
          <div className="text-xs text-muted-foreground">
            共 {table.getFilteredRowModel().rows.length} 条记录
            {table.getPageCount() > 1 && (
              <span className="ml-2">
                第 {table.getState().pagination.pageIndex + 1} / {table.getPageCount()} 页
              </span>
            )}
          </div>
          <div className="flex items-center space-x-1.5">
            <Button
              variant="outline"
              size="xs"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
              className="h-7"
            >
              上一页
            </Button>
            <Button
              variant="outline"
              size="xs"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
              className="h-7"
            >
              下一页
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * 简化版紧凑型数据表格
 * 不包含分页、排序等功能
 */
export function SimpleCompactDataTable<TData, TValue>({
  columns,
  data,
  className,
  defaultSort,
}: {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  className?: string;
  defaultSort?: {
    id: string;
    desc: boolean;
  };
}) {
  const [sorting, setSorting] = useState<SortingState>(
    defaultSort ? [{ id: defaultSort.id, desc: defaultSort.desc }] : []
  );

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    state: {
      sorting,
    },
  });

  return (
    <Card
      className={cn(
        className,
        'border-0 shadow-none rounded-none',
        'bg-transparent'
      )}
    >
      <CardContent>
        <Table className="border-collapse border border-border">
          <TableHeader className="bg-muted/50">
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow
                key={headerGroup.id}
                className="border-b hover:bg-transparent"
              >
                {headerGroup.headers.map(header => {
                  const columnMeta = header.column.columnDef.meta as { className?: string } | undefined;
                  return (
                    <TableHead
                      key={header.id}
                      className={cn(
                        "py-2 px-3 text-xs font-medium text-muted-foreground border-r last:border-r-0",
                        columnMeta?.className
                      )}
                    >
                      {header.isPlaceholder ? null : (
                        <div
                          className={cn(
                            'flex items-center gap-1',
                            header.column.getCanSort() &&
                              'cursor-pointer select-none'
                          )}
                          onClick={header.column.getToggleSortingHandler()}
                        >
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}

                          {header.column.getCanSort() && (
                            <div className="ml-1 flex-none">
                              {header.column.getIsSorted() === 'asc' ? (
                                <ArrowUp className="h-3 w-3 text-primary" />
                              ) : header.column.getIsSorted() === 'desc' ? (
                                <ArrowDown className="h-3 w-3 text-primary" />
                              ) : (
                                <ArrowUpDown className="h-3 w-3 text-muted-foreground/50 hover:text-muted-foreground" />
                              )}
                            </div>
                          )}
                        </div>
                      )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map(row => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  className="border-b last:border-b-0 hover:bg-muted/30"
                >
                  {row.getVisibleCells().map(cell => (
                    <TableCell
                      key={cell.id}
                      className="py-2 px-3 text-xs border-r last:border-r-0"
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  暂无数据
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
