'use client';

import { ReactNode } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

// 卡片项的基础接口
export interface MobileCardItem {
  id: string | number;
  [key: string]: any;
}

// 卡片渲染器接口
export interface MobileCardRenderer<T extends MobileCardItem> {
  // 渲染卡片头部（标题和操作按钮区域）
  renderHeader: (item: T) => ReactNode;
  // 渲染卡片主体内容
  renderContent: (item: T) => ReactNode;
  // 渲染卡片底部（可选）
  renderFooter?: (item: T) => ReactNode;
  // 自定义卡片样式（可选）
  cardClassName?: string;
  // 自定义内容样式（可选）
  contentClassName?: string;
}

// 通用移动端卡片列表组件
interface MobileCardListProps<T extends MobileCardItem> {
  data: T[];
  renderer: MobileCardRenderer<T>;
  className?: string;
  emptyState?: ReactNode;
  loading?: boolean;
  loadingCount?: number;
}

export function MobileCardList<T extends MobileCardItem>({
  data,
  renderer,
  className,
  emptyState,
  loading = false,
  loadingCount = 3,
}: MobileCardListProps<T>) {
  // 加载状态
  if (loading) {
    return (
      <div className={cn('space-y-3', className)}>
        {Array.from({ length: loadingCount }).map((_, index) => (
          <Card key={`loading-${index}`} className="border border-border">
            <CardContent className="p-4">
              <div className="animate-pulse space-y-3">
                <div className="flex justify-between items-start">
                  <div className="space-y-2 flex-1">
                    <div className="h-4 bg-muted rounded w-3/4"></div>
                    <div className="flex gap-2">
                      <div className="h-5 bg-muted rounded w-16"></div>
                      <div className="h-5 bg-muted rounded w-12"></div>
                    </div>
                  </div>
                  <div className="h-8 w-8 bg-muted rounded"></div>
                </div>
                <div className="grid grid-cols-2 gap-3">
                  <div className="space-y-1">
                    <div className="h-3 bg-muted rounded w-16"></div>
                    <div className="h-4 bg-muted rounded w-20"></div>
                  </div>
                  <div className="space-y-1">
                    <div className="h-3 bg-muted rounded w-20"></div>
                    <div className="h-4 bg-muted rounded w-16"></div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  // 空状态
  if (data.length === 0) {
    return emptyState || null;
  }

  // 渲染卡片列表
  return (
    <div className={cn('space-y-3', className)}>
      {data.map((item) => (
        <Card 
          key={item.id} 
          className={cn(
            'border border-border hover:bg-muted/30 transition-colors',
            renderer.cardClassName
          )}
        >
          <CardContent className={cn('p-4', renderer.contentClassName)}>
            {/* 头部区域 */}
            <div className="mb-3">
              {renderer.renderHeader(item)}
            </div>

            {/* 主体内容区域 */}
            <div>
              {renderer.renderContent(item)}
            </div>

            {/* 底部区域（可选） */}
            {renderer.renderFooter && (
              <div className="mt-3 pt-3 border-t border-border">
                {renderer.renderFooter(item)}
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

// 常用的卡片布局组件
export function MobileCardHeader({ 
  title, 
  subtitle, 
  badges, 
  actions 
}: {
  title: ReactNode;
  subtitle?: ReactNode;
  badges?: ReactNode[];
  actions?: ReactNode;
}) {
  return (
    <div className="flex items-start justify-between">
      <div className="flex-1 min-w-0">
        <h3 className="font-medium text-sm text-foreground truncate">
          {title}
        </h3>
        {subtitle && (
          <div className="text-xs text-muted-foreground mt-1">
            {subtitle}
          </div>
        )}
        {badges && badges.length > 0 && (
          <div className="flex items-center gap-2 mt-1">
            {badges}
          </div>
        )}
      </div>
      
      {actions && (
        <div className="ml-2 flex-shrink-0">
          {actions}
        </div>
      )}
    </div>
  );
}

// 网格内容组件
export function MobileCardGrid({ 
  items 
}: {
  items: Array<{
    label: string;
    value: ReactNode;
    className?: string;
  }>;
}) {
  return (
    <div className="grid grid-cols-2 gap-3 text-xs">
      {items.map((item, index) => (
        <div key={index} className={item.className}>
          <span className="text-muted-foreground">{item.label}</span>
          <div className="font-medium text-sm mt-0.5">
            {item.value}
          </div>
        </div>
      ))}
    </div>
  );
}

// 单行信息组件
export function MobileCardInfo({ 
  label, 
  value, 
  className 
}: {
  label: string;
  value: ReactNode;
  className?: string;
}) {
  return (
    <div className={cn('text-xs text-muted-foreground', className)}>
      {label}：{value}
    </div>
  );
}
