'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { GripVertical, X } from 'lucide-react';

import { useMemberDetail } from '@/hooks/use-members';
import {
  useMemberCardsByMember,
  useCardOperations,
} from '@/hooks/use-member-cards';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetClose,
} from '@/components/ui/sheet';

import { MemberInfoCard } from '@/components/tables/member-detail/member-info-card';
import { MemberCardList } from '@/components/tables/member-detail/member-card-list';
import { CompactDataTable } from '@/components/tables/common/compact-data-table';

import { createOperationColumns } from '@/components/tables/columns/member-detail-columns';
import { cn } from '@/lib/utils';

interface MemberDetailDrawerProps {
  memberId?: string;
  open: boolean;
  onClose: () => void;
}

export function MemberDetailDrawer({
  memberId,
  open,
  onClose,
}: MemberDetailDrawerProps) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<string>('all');
  const [drawerWidth, setDrawerWidth] = useState<number>(80); // 默认宽度80%
  const [isDragging, setIsDragging] = useState<boolean>(false);

  // 获取会员信息
  const {
    data: member,
    isLoading: isLoadingMember,
    error: memberError,
  } = useMemberDetail(memberId || '', !!memberId && open);

  // 获取会员卡列表
  const {
    data: memberCards,
    isLoading: isLoadingCards,
    error: cardsError,
  } = useMemberCardsByMember(memberId || '', !!memberId && open);

  // 根据选项卡设置操作类型过滤
  const getOperationTypes = (tab: string): string[] | undefined => {
    switch (tab) {
      case 'recharge':
        return ['recharge'];
      case 'consume':
        return [
          'direct_booking',
          'fixed_schedule_booking',
          'admin_booking',
          'manual_deduction',
        ];
      case 'income':
        return [
          'initial_binding',
          'recharge',
          'refund',
          'member_cancel_booking',
          'admin_cancel_booking',
        ];
      case 'booking':
        return ['direct_booking', 'fixed_schedule_booking', 'admin_booking'];
      case 'cancel':
        return ['member_cancel_booking', 'admin_cancel_booking'];
      default:
        return undefined;
    }
  };

  // 获取操作记录
  const {
    data: operations,
    isLoading: isLoadingOperations,
    error: operationsError,
  } = useCardOperations(
    {
      member_id: memberId || '',
      operation_types: getOperationTypes(activeTab),
    },
    !!memberId && open
  );

  // 处理错误
  useEffect(() => {
    if (memberError) {
      toast.error('获取会员信息失败');
    }
    if (cardsError) {
      toast.error('获取会员卡信息失败');
    }
    if (operationsError) {
      toast.error('获取操作记录失败');
    }
  }, [memberError, cardsError, operationsError]);

  // 处理编辑
  const handleEdit = () => {
    if (memberId) {
      router.push(`/members/${memberId}/edit`);
    }
  };

  // 处理Tab切换
  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  // 处理拖拽开始
  const handleDragStart = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);

    const handleMouseMove = (moveEvent: MouseEvent) => {
      const screenWidth = window.innerWidth;
      const position = moveEvent.clientX;
      const newWidth = 100 - (position / screenWidth) * 100;

      // 限制最小宽度30%，最大宽度90%
      const clampedWidth = Math.max(30, Math.min(90, newWidth));
      setDrawerWidth(clampedWidth);
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetContent
        side="right"
        className={cn(
          'w-[80%] max-w-none sm:max-w-none flex flex-col',
          isDragging ? 'select-none' : ''
        )}
        style={{ width: `${drawerWidth}%` }}
        hideCloseButton
        allowOverflow
        noPadding
      >
        {/* 拖拽手柄 - 改为只在中间位置显示，并且一半位于抽屉外部 */}
        <div
          className="absolute left-[-8px] top-1/2 -translate-y-1/2 h-[100px] cursor-col-resize flex items-center justify-center z-[60]"
          onMouseDown={handleDragStart}
        >
          <div className="flex items-center justify-center bg-background rounded-l-md shadow-md h-full w-3 border-l border-t border-b border-border">
            <GripVertical className="h-6 w-6 text-muted-foreground" />
          </div>
        </div>

        <SheetHeader className="px-6 py-4 border-b flex flex-row items-center justify-between">
          <SheetTitle className="text-xl">会员详情</SheetTitle>
          {/* 自定义关闭按钮，符合项目风格 */}
          <SheetClose asChild>
            <Button
              variant="outline"
              size="icon"
              className="rounded-full h-8 w-8"
            >
              <X className="h-4 w-4" />
            </Button>
          </SheetClose>
        </SheetHeader>

        <div className="flex-1 overflow-y-auto p-6 space-y-6">
          {/* 1. 基本信息卡片 - 传入编辑函数，让编辑按钮显示在用户名旁边 */}
          <MemberInfoCard
            member={member}
            isLoading={isLoadingMember}
            onEdit={handleEdit}
          />

          {/* 2. 会员卡区域 */}
          <Card className="border shadow-sm">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">会员卡</CardTitle>
            </CardHeader>
            <CardContent>
              <MemberCardList
                cards={memberCards || []}
                isLoading={isLoadingCards}
                member={member}
              />
            </CardContent>
          </Card>

          {/* 3. 操作记录 */}
          <Card className="border shadow-sm">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">操作记录</CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs
                defaultValue="all"
                value={activeTab}
                onValueChange={handleTabChange}
                className="w-full"
              >
                <TabsList className="mb-4 w-full sm:w-auto">
                  <TabsTrigger value="all">全部</TabsTrigger>
                  <TabsTrigger value="recharge">充值</TabsTrigger>
                  <TabsTrigger value="consume">消费</TabsTrigger>
                  <TabsTrigger value="income">收入</TabsTrigger>
                  <TabsTrigger value="booking">约课</TabsTrigger>
                  <TabsTrigger value="cancel">取消预约</TabsTrigger>
                </TabsList>

                <TabsContent value={activeTab}>
                  <CompactDataTable
                    columns={createOperationColumns(memberCards || [])}
                    data={operations || []}
                    isLoading={isLoadingOperations}
                  />
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </SheetContent>
    </Sheet>
  );
}
