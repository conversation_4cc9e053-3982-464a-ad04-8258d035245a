'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { FormDialog, FormField, QuickButtons } from '@/components/ui/form-dialog';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { cn } from '@/lib/utils';
import type { MemberCardRead, RechargeRequest, DeductRequest } from '@/types/api/member-card';
import type { MemberRead } from '@/types/api/member';
import { CardStatus, CardType } from '@/types/api/card-template';
import { PaymentMethod, PaymentMethodNames } from '@/types/api/member-card';
import { CreditCard, PlusCircle, Coins, Lock, Unlock } from 'lucide-react';
import { format } from 'date-fns';
import {
  useFreezeMemberCard,
  useUnfreezeMemberCard,
  useRechargeMemberCard,
  useDeductMemberCard,
} from '@/hooks/use-member-cards';

interface MemberCardListProps {
  cards: MemberCardRead[];
  isLoading: boolean;
  member?: MemberRead;
}

export function MemberCardList({
  cards,
  isLoading,
  member,
}: MemberCardListProps) {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {Array(3)
          .fill(0)
          .map((_, i) => (
            <MemberCardSkeleton key={i} />
          ))}
      </div>
    );
  }

  if (!cards.length) {
    return (
      <Card className="border-dashed border-2 h-40 flex items-center justify-center">
        <div className="text-center">
          <p className="text-muted-foreground mb-2">该会员暂无会员卡</p>
          <Button variant="outline">
            <PlusCircle className="h-4 w-4 mr-2" />
            添加会员卡
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {cards.map(card => (
        <MemberCard key={card.id} card={card} member={member} />
      ))}
    </div>
  );
}

function MemberCardSkeleton() {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex justify-between items-start">
          <div>
            <Skeleton className="h-5 w-32 mb-2" />
            <Skeleton className="h-7 w-20 mb-3" />
          </div>
          <Skeleton className="h-6 w-16" />
        </div>

        <div className="space-y-2 mb-4">
          <div className="flex justify-between">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-24" />
          </div>
          <div className="flex justify-between">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-24" />
          </div>
        </div>

        <div className="flex gap-2">
          <Skeleton className="h-9 w-20" />
          <Skeleton className="h-9 w-20" />
          <Skeleton className="h-9 w-20" />
        </div>
      </CardContent>
    </Card>
  );
}

interface MemberCardProps {
  card: MemberCardRead;
  member?: MemberRead;
}

function MemberCard({ card, member }: MemberCardProps) {
  const [rechargeOpen, setRechargeOpen] = useState(false);
  const [deductOpen, setDeductOpen] = useState(false);
  const [rechargeAmount, setRechargeAmount] = useState('');
  const [bonusAmount, setBonusAmount] = useState('');
  const [actualAmount, setActualAmount] = useState('');
  const [extendValidityDays, setExtendValidityDays] = useState('');
  const [deductAmount, setDeductAmount] = useState('');
  const [deductValidityDays, setDeductValidityDays] = useState('');
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>(
    PaymentMethod.MANUAL
  );
  const [notes, setNotes] = useState('');

  const freezeMutation = useFreezeMemberCard();
  const unfreezeMutation = useUnfreezeMemberCard();
  const rechargeMutation = useRechargeMemberCard();
  const deductMutation = useDeductMemberCard();

  // 当充值金额改变时，自动更新实收金额
  const handleRechargeAmountChange = (value: string) => {
    setRechargeAmount(value);
    setActualAmount(value); // 默认实收金额等于充值金额
  };

  const handleFreeze = async () => {
    await freezeMutation.mutateAsync({
      id: card.id.toString(),
      reason: '管理员手动冻结',
    });
  };

  const handleUnfreeze = async () => {
    await unfreezeMutation.mutateAsync(card.id.toString());
  };

  const handleRecharge = async () => {
    if (!rechargeAmount || isNaN(Number(rechargeAmount))) return;
    if (!actualAmount || isNaN(Number(actualAmount))) return;

    // 构建充值数据，只包含有值的字段
    const rechargeData: Partial<RechargeRequest> = {
      member_card_id: Number(card.id),
      amount: Number(rechargeAmount),
      actual_amount: actualAmount
        ? Number(actualAmount)
        : Number(rechargeAmount),
      payment_method: paymentMethod,
      notes: notes || '管理员手动充值',
    };

    // 只有当赠送金额有值且大于0时才添加到请求中
    if (bonusAmount && Number(bonusAmount) > 0) {
      rechargeData.bonus_amount = Number(bonusAmount);
    }

    // 只有当延长有效期有值且大于0时才添加到请求中
    if (extendValidityDays && Number(extendValidityDays) > 0) {
      rechargeData.extend_validity_days = Number(extendValidityDays);
    }

    await rechargeMutation.mutateAsync({
      id: card.id.toString(),
      data: rechargeData as RechargeRequest,
    });

    // 重置表单
    setRechargeOpen(false);
    setRechargeAmount('');
    setBonusAmount('');
    setActualAmount('');
    setExtendValidityDays('');
    setPaymentMethod(PaymentMethod.MANUAL);
    setNotes('');
  };

  const handleDeduct = async () => {
    if (!deductAmount || isNaN(Number(deductAmount))) return;

    // 构建扣费数据，只包含有值的字段
    const deductData: Partial<DeductRequest> = {
      member_card_id: Number(card.id),
      amount: Number(deductAmount),
      notes: notes || '管理员手动扣费',
    };

    // 只有当扣除有效期有值且大于0时才添加到请求中
    if (deductValidityDays && Number(deductValidityDays) > 0) {
      deductData.reduce_validity_days = Number(deductValidityDays);
    }

    await deductMutation.mutateAsync({
      id: card.id.toString(),
      data: deductData as DeductRequest,
    });

    // 重置表单
    setDeductOpen(false);
    setDeductAmount('');
    setDeductValidityDays('');
    setNotes('');
  };

  // 获取卡片状态对应的样式
  const getStatusBadge = () => {
    switch (card.status) {
      case CardStatus.ACTIVE:
        return <Badge variant="success">有效</Badge>;
      case CardStatus.FROZEN:
        return <Badge variant="warning">已冻结</Badge>;
      case CardStatus.EXPIRED:
        return <Badge variant="destructive">已过期</Badge>;
      case CardStatus.CANCELLED:
        return <Badge variant="secondary">已注销</Badge>;
      default:
        return <Badge>未知</Badge>;
    }
  };

  // 获取卡片类型名称
  const getCardTypeName = (type: CardType) => {
    switch (type) {
      case CardType.TIMES_LIMITED:
      case CardType.TIMES_UNLIMITED:
        return '次数卡';
      case CardType.VALUE_LIMITED:
      case CardType.VALUE_UNLIMITED:
        return '金额卡';
      default:
        return '会员卡';
    }
  };

  // 判断是否是次数卡
  const isCountCard =
    card.card_type === CardType.TIMES_LIMITED ||
    card.card_type === CardType.TIMES_UNLIMITED;

  const isCardActive = card.status === CardStatus.ACTIVE;
  const isCardFrozen = card.status === CardStatus.FROZEN;

  // 获取会员名称
  const getMemberName = () => {
    if (member) {
      return member.name || `会员 ${member.id}`;
    }
    return `会员 ${card.member_id}`;
  };

  return (
    <>
      <Card
        className={cn(
          'overflow-hidden',
          card.status === CardStatus.FROZEN && 'border-blue-500',
          card.status === CardStatus.EXPIRED && 'border-amber-500',
          card.status === CardStatus.CANCELLED && 'border-slate-500'
        )}
      >
        <CardContent className="p-6">
          <div className="flex justify-between items-start">
            <div>
              <div className="text-sm text-muted-foreground mb-1">
                {getCardTypeName(card.card_type)}
              </div>
              <h3 className="text-lg font-medium mb-2">
                {card.name || `会员卡: ${card.id}`}
              </h3>
            </div>
            {getStatusBadge()}
          </div>

          <div className="space-y-2 mb-4">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">余额</span>
              <span className="font-medium">
                {isCountCard ? `${card.balance}次` : `¥${card.balance}`}
              </span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">有效期</span>
              <span>
                {card.expires_at
                  ? format(new Date(card.expires_at), 'yyyy-MM-dd')
                  : '永久有效'}
              </span>
            </div>
          </div>

          <div className="flex gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => setRechargeOpen(true)}
              disabled={!isCardActive}
            >
              <Coins className="h-4 w-4 mr-1" />
              充值
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => setDeductOpen(true)}
              disabled={!isCardActive}
            >
              <CreditCard className="h-4 w-4 mr-1" />
              扣费
            </Button>
            {isCardActive ? (
              <Button
                size="sm"
                variant="outline"
                className="text-blue-500 hover:text-blue-600"
                onClick={handleFreeze}
                disabled={freezeMutation.isPending}
              >
                <Lock className="h-4 w-4 mr-1" />
                冻结
              </Button>
            ) : isCardFrozen ? (
              <Button
                size="sm"
                variant="outline"
                className="text-green-500 hover:text-green-600"
                onClick={handleUnfreeze}
                disabled={unfreezeMutation.isPending}
              >
                <Unlock className="h-4 w-4 mr-1" />
                解冻
              </Button>
            ) : (
              <Button size="sm" variant="outline" disabled>
                <Lock className="h-4 w-4 mr-1" />
                已禁用
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 充值对话框 */}
      <FormDialog
        open={rechargeOpen}
        onOpenChange={setRechargeOpen}
        title="会员卡充值"
        size="md"
        onConfirm={handleRecharge}
        confirmText="确认充值"
        isLoading={rechargeMutation.isPending}
        disabled={
          !rechargeAmount ||
          isNaN(Number(rechargeAmount)) ||
          !actualAmount ||
          isNaN(Number(actualAmount))
        }
      >
        <div className="space-y-4">
          <FormField label="会员卡">
            <span className="font-medium">
              {card.name || `会员卡 ${card.id}`}
            </span>
          </FormField>

          <FormField label="会员">
            <span className="font-medium">{getMemberName()}</span>
          </FormField>

          <FormField label="当前余额">
            <span className="font-medium">
              {isCountCard ? `${card.balance}次` : `¥${card.balance}`}
            </span>
          </FormField>
          <FormField label="充值金额" required>
            <Input
              type="number"
              placeholder={isCountCard ? "请输入充值次数" : "请输入充值金额"}
              value={rechargeAmount}
              onChange={e => handleRechargeAmountChange(e.target.value)}
            />
          </FormField>

          <FormField label="赠送金额">
            <Input
              type="number"
              placeholder={isCountCard ? "请输入赠送次数（可选）" : "请输入赠送金额（可选）"}
              value={bonusAmount}
              onChange={e => setBonusAmount(e.target.value)}
            />
          </FormField>

          <FormField label="实收金额" required>
            <Input
              type="number"
              placeholder="请输入实收金额"
              value={actualAmount}
              onChange={e => setActualAmount(e.target.value)}
            />
          </FormField>

          <FormField label="支付方式" required>
            <Select
              value={paymentMethod}
              onValueChange={value =>
                setPaymentMethod(value as PaymentMethod)
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="选择支付方式" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={PaymentMethod.MANUAL}>
                  {PaymentMethodNames[PaymentMethod.MANUAL]}
                </SelectItem>
                <SelectItem value={PaymentMethod.WECHAT}>
                  {PaymentMethodNames[PaymentMethod.WECHAT]}
                </SelectItem>
                <SelectItem value={PaymentMethod.ALIPAY}>
                  {PaymentMethodNames[PaymentMethod.ALIPAY]}
                </SelectItem>
                <SelectItem value={PaymentMethod.BANK_TRANSFER}>
                  {PaymentMethodNames[PaymentMethod.BANK_TRANSFER]}
                </SelectItem>
              </SelectContent>
            </Select>
          </FormField>
          <FormField
            label="延长有效期"
            help={(() => {
              // 如果输入了延长天数，显示延长后的日期
              if (extendValidityDays && Number(extendValidityDays) > 0) {
                const currentExpiry = card.expires_at
                  ? new Date(card.expires_at)
                  : new Date(); // 如果是永久有效，从今天开始计算

                const extendedDate = new Date(currentExpiry);
                extendedDate.setDate(extendedDate.getDate() + Number(extendValidityDays));

                return (
                  <span>
                    将延期至：
                    <span className="text-success font-medium">
                      {format(extendedDate, 'yyyy-MM-dd')}
                    </span>
                  </span>
                );
              }

              // 默认显示当前有效期
              return card.expires_at
                ? `当前有效期至：${format(new Date(card.expires_at), 'yyyy-MM-dd')}`
                : '当前为永久有效';
            })()}
          >
            <Input
              type="number"
              placeholder="请输入延长天数（可选）"
              value={extendValidityDays}
              onChange={e => setExtendValidityDays(e.target.value)}
            />
          </FormField>

          <FormField label="备注">
            <Input
              placeholder="请输入备注信息（可选）"
              value={notes}
              onChange={e => setNotes(e.target.value)}
            />
          </FormField>
        </div>
      </FormDialog>

      {/* 扣费对话框 */}
      <FormDialog
        open={deductOpen}
        onOpenChange={setDeductOpen}
        title="会员卡扣费"
        description="对会员卡进行扣费操作，请谨慎操作"
        size="md"
        variant="destructive"
        onConfirm={handleDeduct}
        confirmText="确认扣费"
        isLoading={deductMutation.isPending}
        disabled={!deductAmount || isNaN(Number(deductAmount))}
      >
        <div className="space-y-4">
          <FormField label="会员">
            <span className="font-medium">{getMemberName()}</span>
          </FormField>

          <FormField label="当前余额">
            <span className="font-medium">
              {isCountCard ? `${card.balance}次` : `¥${card.balance}`}
            </span>
          </FormField>

          <FormField label="扣除余额" required>
            <Input
              type="number"
              placeholder={isCountCard ? "请输入扣除次数" : "请输入扣除金额"}
              value={deductAmount}
              onChange={e => setDeductAmount(e.target.value)}
            />
          </FormField>

          <FormField
            label="扣除有效期"
            help={(() => {
              // 如果输入了扣除天数，显示扣除后的日期
              if (deductValidityDays && Number(deductValidityDays) > 0) {
                if (card.expires_at) {
                  const currentExpiry = new Date(card.expires_at);
                  const deductedDate = new Date(currentExpiry);
                  deductedDate.setDate(deductedDate.getDate() - Number(deductValidityDays));

                  return (
                    <span>
                      将缩短至：
                      <span className="text-destructive font-medium">
                        {format(deductedDate, 'yyyy-MM-dd')}
                      </span>
                    </span>
                  );
                } else {
                  return '当前为永久有效，无法扣除有效期';
                }
              }

              // 默认显示当前有效期
              return card.expires_at
                ? `当前有效期至：${format(new Date(card.expires_at), 'yyyy-MM-dd')}`
                : '当前为永久有效';
            })()}
          >
            <Input
              type="number"
              placeholder="请输入扣除天数（可选）"
              value={deductValidityDays}
              onChange={e => setDeductValidityDays(e.target.value)}
            />
          </FormField>
          <FormField label="备注">
            <div className="space-y-2">
              <Input
                placeholder="请输入备注信息（可选）"
                value={notes}
                onChange={e => setNotes(e.target.value)}
              />
              <QuickButtons
                buttons={[
                  { label: '退款', onClick: () => setNotes('退款') },
                  { label: '固定课额外扣费', onClick: () => setNotes('固定课额外扣费') },
                  { label: '额外补扣', onClick: () => setNotes('额外补扣') },
                ]}
              />
            </div>
          </FormField>
        </div>
      </FormDialog>
    </>
  );
}
