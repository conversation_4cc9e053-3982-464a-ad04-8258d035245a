'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar } from '@/components/ui/avatar';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { Pencil, CreditCard } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useCardTemplateList } from '@/hooks/use-card-templates';
import { useCreateMemberCard } from '@/hooks/use-member-cards';
import { toast } from 'sonner';
import type { MemberRead } from '@/types/api/member';
import { format } from 'date-fns';

interface MemberInfoCardProps {
  member?: MemberRead;
  isLoading: boolean;
  onEdit?: () => void;
}

export function MemberInfoCard({
  member,
  isLoading,
  onEdit,
}: MemberInfoCardProps) {
  const [isBindCardDialogOpen, setIsBindCardDialogOpen] = useState(false);
  const [selectedTemplateId, setSelectedTemplateId] = useState<string>('');

  // 获取会员卡模板列表
  const { data: cardTemplates, isLoading: isLoadingTemplates } =
    useCardTemplateList();

  // 创建会员卡
  const createMemberCardMutation = useCreateMemberCard();

  const handleBindCard = () => {
    setIsBindCardDialogOpen(true);
  };

  const handleConfirmBindCard = async () => {
    if (!member || !selectedTemplateId) return;

    try {
      await createMemberCardMutation.mutateAsync({
        member_id: Number(member.id),
        template_id: Number(selectedTemplateId),
      });
      setIsBindCardDialogOpen(false);
      setSelectedTemplateId('');
    } catch (error) {
      console.error('绑定会员卡失败:', error);
      // toast.error('绑定会员卡失败，请重试');
    }
  };

  if (isLoading) {
    return <MemberInfoCardSkeleton />;
  }

  if (!member) {
    return (
      <Card className="overflow-hidden">
        <CardContent className="p-6">
          <div className="text-center py-6">未找到会员信息</div>
        </CardContent>
      </Card>
    );
  }

  // 会员类型和状态映射暂时保留，但注释掉，以便将来可能恢复使用
  /*
  const memberTypeMap = {
    trial: { label: '体验会员', color: 'bg-amber-500' },
    formal: { label: '正式会员', color: 'bg-green-500' },
    vip: { label: 'VIP会员', color: 'bg-purple-500' },
  };

  const memberStatusMap = {
    active: { label: '活跃', color: 'bg-green-500' },
    silent: { label: '沉默', color: 'bg-amber-500' },
    frozen: { label: '已冻结', color: 'bg-slate-500' },
    cancelled: { label: '已注销', color: 'bg-red-500' },
  };

  const memberTypeInfo = memberTypeMap[member.member_type] || {
    label: '未知',
    color: 'bg-gray-500',
  };
  const memberStatusInfo = memberStatusMap[member.member_status] || {
    label: '未知',
    color: 'bg-gray-500',
  };
  */

  return (
    <>
      <Card className="overflow-hidden">
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row md:items-center gap-6">
            {/* 头像和基本信息 */}
            <div className="flex items-center gap-4">
              <Avatar className="h-20 w-20">
                <img
                  src={
                    member.avatar_url ||
                    member.wechat_avatar ||
                    '/placeholder-avatar.jpg'
                  }
                  alt={member.name}
                />
              </Avatar>
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <h3 className="text-lg font-medium">{member.name}</h3>
                  <div className="flex gap-2">
                    {onEdit && (
                      <Button
                        size="sm"
                        onClick={onEdit}
                        className="h-8"
                        title="编辑会员"
                      >
                        <Pencil className="h-3.5 w-3.5" />
                        编辑
                      </Button>
                    )}
                    <Button
                      size="sm"
                      variant="success"
                      onClick={handleBindCard}
                      className="h-8"
                      title="绑定会员卡"
                    >
                      <CreditCard className="h-3.5 w-3.5" />
                      绑定会员卡
                    </Button>
                  </div>
                </div>
                <div className="text-sm text-muted-foreground">
                  {member.phone}
                  {member.email && (
                    <span className="ml-2">| {member.email}</span>
                  )}
                </div>
                <div className="text-sm text-muted-foreground mt-1">
                  注册时间:{' '}
                  {format(new Date(member.registered_at), 'yyyy-MM-dd')}
                </div>
              </div>
            </div>

            {/* 数据统计 */}
            <div className="flex-1 grid grid-cols-2 sm:grid-cols-4 gap-4 mt-4 md:mt-0">
              <div className="bg-muted/30 p-3 rounded-md">
                <div className="text-sm text-muted-foreground">消费总额</div>
                <div className="text-lg font-semibold mt-1">
                  ¥{member.total_spent || 0}
                </div>
              </div>
              <div className="bg-muted/30 p-3 rounded-md">
                <div className="text-sm text-muted-foreground">充值总额</div>
                <div className="text-lg font-semibold mt-1">
                  ¥{member.total_recharged || 0}
                </div>
              </div>
              <div className="bg-muted/30 p-3 rounded-md">
                <div className="text-sm text-muted-foreground">上课总数</div>
                <div className="text-lg font-semibold mt-1">
                  {member.completed_classes || 0}节
                </div>
              </div>
              <div className="bg-muted/30 p-3 rounded-md">
                <div className="text-sm text-muted-foreground">未消课数</div>
                <div className="text-lg font-semibold mt-1">
                  {(member.total_classes || 0) -
                    (member.completed_classes || 0)}
                  节
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 绑定会员卡对话框 */}
      <Dialog
        open={isBindCardDialogOpen}
        onOpenChange={setIsBindCardDialogOpen}
      >
        <DialogContent className="sm:max-w-md space-y-0">
          <DialogHeader>
            <DialogTitle>绑定会员卡</DialogTitle>
            <DialogDescription>
              为会员 {member?.name} 绑定一张新的会员卡
            </DialogDescription>
          </DialogHeader>

          {/* 提示信息区域 */}
          <Alert variant="info" className="my-3">
            <AlertDescription>
              <div className="font-medium text-info mb-2">绑定说明：</div>
              <div className="space-y-1 text-sm">
                <div className="flex items-start gap-2">
                  <span className="text-info font-medium">1.</span>
                  <span>绑定会员卡后，会员可以使用该卡进行消费</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-info font-medium">2.</span>
                  <span>绑定后可能需要进行充值操作才能使用</span>
                </div>
              </div>
            </AlertDescription>
          </Alert>

          {/* 选择区域 */}
          <div className="space-y-4 py-2">
            <div className="grid grid-cols-4 items-center gap-4">
              <span className="text-right text-sm font-medium">会员姓名:</span>
              <div className="col-span-3 font-medium">{member?.name}</div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <span className="text-right text-sm font-medium">
                选择会员卡模板:
              </span>
              <div className="col-span-3">
                <Select
                  value={selectedTemplateId}
                  onValueChange={setSelectedTemplateId}
                  disabled={isLoadingTemplates}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择会员卡模板" />
                  </SelectTrigger>
                  <SelectContent>
                    {cardTemplates?.map(template => (
                      <SelectItem
                        key={template.id}
                        value={template.id.toString()}
                      >
                        {template.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsBindCardDialogOpen(false)}
            >
              取消
            </Button>
            <Button
              onClick={handleConfirmBindCard}
              disabled={
                !selectedTemplateId || createMemberCardMutation.isPending
              }
            >
              {createMemberCardMutation.isPending ? '绑定中...' : '确定绑定'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}

function MemberInfoCardSkeleton() {
  return (
    <Card className="overflow-hidden">
      <CardContent className="p-6">
        <div className="flex flex-col md:flex-row md:items-center gap-6">
          {/* 头像和基本信息骨架 */}
          <div className="flex items-center gap-4">
            <Skeleton className="h-20 w-20 rounded-full" />
            <div>
              <Skeleton className="h-6 w-40 mb-2" />
              <Skeleton className="h-4 w-32 mb-2" />
              <Skeleton className="h-4 w-48" />
            </div>
          </div>

          {/* 数据统计骨架 */}
          <div className="flex-1 grid grid-cols-2 sm:grid-cols-4 gap-4 mt-4 md:mt-0">
            {Array(4)
              .fill(0)
              .map((_, i) => (
                <div key={i} className="bg-muted/30 p-3 rounded-md">
                  <Skeleton className="h-4 w-16 mb-2" />
                  <Skeleton className="h-6 w-12" />
                </div>
              ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
