'use client';

import { ColumnDef } from '@tanstack/react-table';
import { MoreHorizontal, Edit, Trash2, Eye } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { formatDateTime } from '@/lib/utils/format';
import type { UserRead } from '@/types/api';

interface UserColumnsProps {
  onEdit: (user: UserRead) => void;
  onDelete: (user: UserRead) => void;
  onView: (user: UserRead) => void;
}

export function createUserColumns({
  onEdit,
  onDelete,
  onView,
}: UserColumnsProps): ColumnDef<UserRead>[] {
  return [
    {
      accessorKey: 'username',
      header: '用户',
      cell: ({ row }) => {
        const user = row.original;
        return (
          <div className="flex items-center gap-2">
            <Avatar className="h-6 w-6">
              <AvatarImage src={user.avatar_url || ''} alt={user.username} />
              <AvatarFallback className="text-xs">
                {user.username.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="font-medium text-sm">{user.username}</div>
              {user.real_name && (
                <div className="text-xs text-muted-foreground">
                  {user.real_name}
                </div>
              )}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'email',
      header: '邮箱',
      cell: ({ row }) => {
        const email = row.getValue('email') as string;
        return email ? (
          <div className="text-sm">{email}</div>
        ) : (
          <div className="text-xs text-muted-foreground">未设置</div>
        );
      },
    },
    {
      accessorKey: 'phone',
      header: '手机号',
      cell: ({ row }) => {
        const phone = row.getValue('phone') as string;
        return phone ? (
          <div className="text-sm">{phone}</div>
        ) : (
          <div className="text-xs text-muted-foreground">未设置</div>
        );
      },
    },
    {
      accessorKey: 'role',
      header: '角色',
      cell: ({ row }) => {
        const role = row.getValue('role') as string;
        const roleMap = {
          super_admin: { label: '超级管理员', variant: 'destructive' as const },
          admin: { label: '管理员', variant: 'info' as const },
          agent: { label: '代理商', variant: 'warning' as const },
          sale: { label: '销售', variant: 'secondary' as const },
        };
        const roleInfo = roleMap[role as keyof typeof roleMap] || {
          label: role,
          variant: 'outline' as const,
        };

        return (
          <Badge variant={roleInfo.variant} className="text-xs py-0.5">
            {roleInfo.label}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'is_active',
      header: '状态',
      cell: ({ row }) => {
        const isActive = row.getValue('is_active') as boolean;
        return (
          <Badge
            variant={isActive ? 'success' : 'secondary'}
            className="text-xs py-0.5"
          >
            {isActive ? '活跃' : '禁用'}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'created_at',
      header: '创建时间',
      cell: ({ row }) => {
        const createdAt = row.getValue('created_at') as string;
        return (
          <div className="text-xs text-muted-foreground">
            {formatDateTime(createdAt)}
          </div>
        );
      },
    },
    {
      accessorKey: 'last_login_at',
      header: '最后登录',
      cell: ({ row }) => {
        const lastLoginAt = row.getValue('last_login_at') as string;
        return lastLoginAt ? (
          <div className="text-xs text-muted-foreground">
            {formatDateTime(lastLoginAt)}
          </div>
        ) : (
          <div className="text-xs text-muted-foreground">从未登录</div>
        );
      },
    },
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }) => {
        const user = row.original;

        return (
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="xs"
              className="h-7 w-7 p-0"
              onClick={() => onView(user)}
              title="查看详情"
            >
              <Eye className="h-3.5 w-3.5" />
            </Button>
            <Button
              variant="ghost"
              size="xs"
              className="h-7 w-7 p-0"
              onClick={() => onEdit(user)}
              title="编辑"
            >
              <Edit className="h-3.5 w-3.5" />
            </Button>
            <Button
              variant="ghost"
              size="xs"
              className="h-7 w-7 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
              onClick={() => onDelete(user)}
              title="删除"
            >
              <Trash2 className="h-3.5 w-3.5" />
            </Button>
          </div>
        );
      },
    },
  ];
}
