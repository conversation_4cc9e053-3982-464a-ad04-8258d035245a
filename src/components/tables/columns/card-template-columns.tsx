'use client';

import { ColumnDef } from '@tanstack/react-table';
import { MoreHorizontal, CheckCircle, XCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { CardTemplateList, CardType, CardTypeNames } from '@/types/api';
import { formatDate } from '@/lib/utils';

// 操作按钮单元格
interface ActionCellProps {
  row: {
    original: CardTemplateList;
  };
  onEdit: (id: number) => void;
  onDelete: (id: number) => void;
  onToggleStatus: (id: number) => void;
}

function ActionCell({
  row,
  onEdit,
  onDelete,
  onToggleStatus,
}: ActionCellProps) {
  const template = row.original;

  return (
    <div className="text-right">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">打开菜单</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>操作</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => onEdit(template.id)}>
            编辑
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onToggleStatus(template.id)}>
            {template.is_active ? '停用' : '启用'}
          </DropdownMenuItem>
          <DropdownMenuItem
            className="text-destructive focus:text-destructive"
            onClick={() => onDelete(template.id)}
          >
            删除
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}

// 创建表格列定义函数
export function createCardTemplateColumns({
  onEdit,
  onDelete,
  onToggleStatus,
}: {
  onEdit: (id: number) => void;
  onDelete: (id: number) => void;
  onToggleStatus: (id: number) => void;
}): ColumnDef<CardTemplateList>[] {
  return [
    // 名称列
    {
      accessorKey: 'name',
      header: '模板名称',
      cell: ({ row }) => <div className="font-medium">{row.original.name}</div>,
    },
    // 卡片类型列
    {
      accessorKey: 'card_type',
      header: '卡片类型',
      cell: ({ row }) => {
        const cardType = row.original.card_type;
        return (
          <Badge variant="outline">
            {CardTypeNames[cardType as CardType] || cardType}
          </Badge>
        );
      },
    },
    // 售价列
    {
      accessorKey: 'sale_price',
      header: '售价(元)',
      cell: ({ row }) => (
        <div className="font-medium text-right">
          {row.original.sale_price.toFixed(2)}
        </div>
      ),
    },
    // 可用余额列
    {
      accessorKey: 'available_balance',
      header: '可用余额/次数',
      cell: ({ row }) => {
        const balance = row.original.available_balance;
        const cardType = row.original.card_type;

        if (balance === null || balance === undefined) {
          return <div className="text-muted-foreground text-center">不限</div>;
        }

        const isTimesCard =
          cardType === CardType.TIMES_LIMITED ||
          cardType === CardType.TIMES_UNLIMITED;
        return (
          <div className="text-center">
            {balance} {isTimesCard ? '次' : '元'}
          </div>
        );
      },
    },
    // 有效期列
    {
      accessorKey: 'validity_days',
      header: '有效期(天)',
      cell: ({ row }) => {
        const days = row.original.validity_days;
        return (
          <div className="text-center">
            {days ? days : <span className="text-muted-foreground">不限</span>}
          </div>
        );
      },
    },
    // 代理专售列
    {
      accessorKey: 'is_agent_exclusive',
      header: '代理专售',
      cell: ({ row }) => {
        const isExclusive = row.original.is_agent_exclusive;
        return (
          <div className="flex justify-center">
            {isExclusive ? (
              <CheckCircle className="h-5 w-5 text-green-500" />
            ) : (
              <XCircle className="h-5 w-5 text-muted-foreground" />
            )}
          </div>
        );
      },
    },
    // 状态列
    {
      accessorKey: 'is_active',
      header: '状态',
      cell: ({ row }) => {
        const isActive = row.original.is_active;
        return (
          <div className="flex justify-center">
            <Badge variant={isActive ? 'default' : 'secondary'}>
              {isActive ? '启用' : '停用'}
            </Badge>
          </div>
        );
      },
    },
    // 创建时间列
    {
      accessorKey: 'created_at',
      header: '创建时间',
      cell: ({ row }) => (
        <div className="text-muted-foreground">
          {formatDate(row.original.created_at)}
        </div>
      ),
    },
    // 操作列
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }) => (
        <ActionCell
          row={row}
          onEdit={onEdit}
          onDelete={onDelete}
          onToggleStatus={onToggleStatus}
        />
      ),
    },
  ];
}
