'use client';

import { ColumnDef } from '@tanstack/react-table';
import { MoreHorizontal, Edit, Trash2, Eye, Tags } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { formatDateTime } from '@/lib/utils/format';
import type { TagCategoryList } from '@/types/api';

interface TagCategoryColumnsProps {
  onEdit: (category: TagCategoryList) => void;
  onDelete: (category: TagCategoryList) => void;
  onView: (category: TagCategoryList) => void;
}

export function createTagCategoryColumns({
  onEdit,
  onDelete,
  onView,
}: TagCategoryColumnsProps): ColumnDef<TagCategoryList>[] {
  return [
    {
      accessorKey: 'name',
      header: '分类名称',
      cell: ({ row }) => {
        const category = row.original;
        return (
          <div className="flex items-center space-x-2">
            <Tags className="h-4 w-4 text-muted-foreground" />
            <div>
              <div className="font-medium">{category.name}</div>
              {category.description && (
                <div className="text-xs text-muted-foreground">
                  {category.description}
                </div>
              )}
            </div>
          </div>
        );
      },
      meta: { className: 'min-w-[200px]' },
    },
    {
      accessorKey: 'tag_count',
      header: '标签数量',
      cell: ({ row }) => {
        const count = row.getValue('tag_count') as number;
        return (
          <Badge variant="secondary" className="font-mono">
            {count || 0}
          </Badge>
        );
      },
      meta: { className: 'w-[100px]' },
    },
    {
      accessorKey: 'created_at',
      header: '创建时间',
      cell: ({ row }) => {
        const date = row.getValue('created_at') as string;
        return (
          <div className="text-xs text-muted-foreground">
            {formatDateTime(date)}
          </div>
        );
      },
      meta: { className: 'w-[140px]' },
    },
    {
      accessorKey: 'updated_at',
      header: '更新时间',
      cell: ({ row }) => {
        const date = row.getValue('updated_at') as string;
        return (
          <div className="text-xs text-muted-foreground">
            {formatDateTime(date)}
          </div>
        );
      },
      meta: { className: 'w-[140px]' },
    },
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }) => {
        const category = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">打开菜单</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>操作</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => onView(category)}>
                <Eye className="mr-2 h-4 w-4" />
                查看标签
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => onEdit(category)}>
                <Edit className="mr-2 h-4 w-4" />
                编辑
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onDelete(category)}
                className="text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                删除
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
      meta: { className: 'w-[70px]' },
    },
  ];
}
