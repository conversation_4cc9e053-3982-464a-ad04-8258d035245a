// 教师表格列定义

'use client';

import { ColumnDef } from '@tanstack/react-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MoreHorizontal, Edit, Trash2, Eye, UserCheck, UserX, Tags } from 'lucide-react';
import {
  TeacherRegionNames,
  TeacherStatusNames,
  GenderNames,
  TeacherStatusColors,
  GenderColors,
} from '@/constants/teachers';
import type { TeacherList } from '@/types/api';

interface TeacherColumnsProps {
  onEdit: (teacher: TeacherList) => void;
  onDelete: (teacher: TeacherList) => void;
  onView: (teacher: TeacherList) => void;
  onStatusToggle: (teacher: TeacherList) => void;
  onShowToMembersToggle: (teacher: TeacherList) => void;
  onManageTags: (teacher: TeacherList) => void;

}

export function createTeacherColumns({
  onEdit,
  onDelete,
  onView,
  onStatusToggle,
  onShowToMembersToggle,
  onManageTags,

}: TeacherColumnsProps): ColumnDef<TeacherList>[] {
  return [
    {
      accessorKey: 'name',
      header: '教师信息',
      cell: ({ row }) => {
        const teacher = row.original;
        return (
          <div className="flex items-center space-x-3">
            <Avatar className="h-10 w-10">
              <AvatarImage src={teacher.avatar} alt={teacher.name} />
              <AvatarFallback>
                {teacher.name.slice(0, 2)}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="font-medium">{teacher.name}</div>
              {teacher.display_code && (
                <div className="text-sm text-muted-foreground">
                  编号: {teacher.display_code}
                </div>
              )}
              {teacher.gender && (
                <Badge variant={GenderColors[teacher.gender]} className="text-xs mt-1">
                  {GenderNames[teacher.gender]}
                </Badge>
              )}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'phone',
      header: '手机号',
      cell: ({ row }) => {
        const teacher = row.original;
        return (
          <div className="text-sm">
            {teacher.phone || '-'}
          </div>
        );
      },
    },
    {
      accessorKey: 'email',
      header: '邮箱',
      cell: ({ row }) => {
        const teacher = row.original;
        return (
          <div className="text-sm">
            {teacher.email || '-'}
          </div>
        );
      },
    },
    {
      accessorKey: 'region',
      header: '区域',
      cell: ({ row }) => {
        const region = row.getValue('region') as string;
        return (
          <Badge variant="secondary">
            {TeacherRegionNames[region as keyof typeof TeacherRegionNames]}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'price_per_class',
      header: '课时费',
      cell: ({ row }) => {
        const price = row.getValue('price_per_class') as number;
        return (
          <div className="font-medium">
            ¥{price}
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      header: '状态',
      cell: ({ row }) => {
        const teacher = row.original;
        const status = teacher.status;
        const statusKey = status as keyof typeof TeacherStatusNames;

        return (
          <Badge
            variant={TeacherStatusColors[statusKey]}
            className="cursor-pointer hover:opacity-80 transition-opacity"
            onClick={() => onStatusToggle(teacher)}
          >
            {TeacherStatusNames[statusKey]}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'show_to_members',
      header: '会员可见',
      cell: ({ row }) => {
        const teacher = row.original;
        const showToMembers = teacher.show_to_members;

        return (
          <Badge
            variant={showToMembers ? 'success' : 'secondary'}
            className="cursor-pointer hover:opacity-80 transition-opacity"
            onClick={() => onShowToMembersToggle(teacher)}
          >
            {showToMembers ? '展示' : '隐藏'}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'priority_level',
      header: '优先级',
      cell: ({ row }) => {
        const priority = row.getValue('priority_level') as number;
        return (
          <div className="text-center">
            <span className="font-medium">{priority}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'teaching_experience',
      header: '经验',
      cell: ({ row }) => {
        const experience = row.getValue('teaching_experience') as number | undefined;
        return (
          <div className="text-center">
            {experience ? `${experience}年` : '-'}
          </div>
        );
      },
    },
    {
      accessorKey: 'tag_count',
      header: '标签',
      cell: ({ row }) => {
        const teacher = row.original;
        return (
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-xs">
              {teacher.tag_count}个
            </Badge>
            {teacher.tags && teacher.tags.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {teacher.tags.slice(0, 2).map((tag) => (
                  <Badge key={tag.id} variant="secondary" className="text-xs">
                    {tag.name}
                  </Badge>
                ))}
                {teacher.tags.length > 2 && (
                  <Badge variant="outline" className="text-xs">
                    +{teacher.tags.length - 2}
                  </Badge>
                )}
              </div>
            )}
          </div>
        );
      },
    },
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }) => {
        const teacher = row.original;
        
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">打开菜单</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>操作</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => onView(teacher)}>
                <Eye className="mr-2 h-4 w-4" />
                查看详情
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onEdit(teacher)}>
                <Edit className="mr-2 h-4 w-4" />
                编辑
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onManageTags(teacher)}>
                <Tags className="mr-2 h-4 w-4" />
                管理标签
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => onStatusToggle(teacher)}>
                {teacher.status === 'active' ? (
                  <>
                    <UserX className="mr-2 h-4 w-4" />
                    停用
                  </>
                ) : (
                  <>
                    <UserCheck className="mr-2 h-4 w-4" />
                    激活
                  </>
                )}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => onDelete(teacher)}
                className="text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                删除
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];
}
