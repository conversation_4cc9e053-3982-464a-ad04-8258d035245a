'use client';

import { ColumnDef } from '@tanstack/react-table';
import { MoreHorizontal, Edit, Trash2, Tag } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { formatDateTime } from '@/lib/utils/format';
import { TagStatusNames, TagStatusColors } from '@/constants/tags';
import type { TagWithCategory, TagList } from '@/types/api';

interface TagColumnsProps {
  onEdit: (tag: TagWithCategory | TagList) => void;
  onDelete: (tag: TagWithCategory | TagList) => void;
  onStatusToggle?: (tag: TagWithCategory | TagList) => void;
  showCategory?: boolean;
}

export function createTagColumns({
  onEdit,
  onDelete,
  onStatusToggle,
  showCategory = true,
}: TagColumnsProps): ColumnDef<TagWithCategory | TagList>[] {
  const columns: ColumnDef<TagWithCategory | TagList>[] = [
    {
      accessorKey: 'name',
      header: '标签名称',
      cell: ({ row }) => {
        const tag = row.original;
        return (
          <div className="flex items-center space-x-2">
            <div
              className="w-3 h-3 rounded-full border"
              style={{
                backgroundColor: tag.color || '#6B7280',
                borderColor: tag.color || '#6B7280',
              }}
            />
            <div>
              <div className="font-medium flex items-center space-x-1">
                <Tag className="h-3 w-3 text-muted-foreground" />
                <span>{tag.name}</span>
              </div>
              {tag.description && (
                <div className="text-xs text-muted-foreground">
                  {tag.description}
                </div>
              )}
            </div>
          </div>
        );
      },
      meta: { className: 'min-w-[200px]' },
    },
  ];

  // 如果需要显示分类信息
  if (showCategory) {
    columns.push({
      accessorKey: 'category',
      header: '所属分类',
      cell: ({ row }) => {
        const tag = row.original as TagWithCategory;
        return tag.category ? (
          <Badge variant="outline">{tag.category.name}</Badge>
        ) : (
          <span className="text-muted-foreground">-</span>
        );
      },
      meta: { className: 'w-[120px]' },
    });
  }

  columns.push(
    {
      accessorKey: 'status',
      header: '状态',
      cell: ({ row }) => {
        const status = row.getValue('status') as string;
        const statusKey = status as keyof typeof TagStatusNames;
        const variant = TagStatusColors[statusKey] as 'success' | 'secondary';
        
        return (
          <Badge variant={variant}>
            {TagStatusNames[statusKey] || status}
          </Badge>
        );
      },
      meta: { className: 'w-[80px]' },
    },
    {
      accessorKey: 'created_at',
      header: '创建时间',
      cell: ({ row }) => {
        const date = row.getValue('created_at') as string;
        return (
          <div className="text-xs text-muted-foreground">
            {formatDateTime(date)}
          </div>
        );
      },
      meta: { className: 'w-[140px]' },
    },
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }) => {
        const tag = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">打开菜单</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>操作</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => onEdit(tag)}>
                <Edit className="mr-2 h-4 w-4" />
                编辑
              </DropdownMenuItem>
              {onStatusToggle && (
                <DropdownMenuItem onClick={() => onStatusToggle(tag)}>
                  <Tag className="mr-2 h-4 w-4" />
                  {tag.status === 'active' ? '停用' : '激活'}
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => onDelete(tag)}
                className="text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                删除
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
      meta: { className: 'w-[70px]' },
    }
  );

  return columns;
}
