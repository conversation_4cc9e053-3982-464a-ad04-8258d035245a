'use client';

import { ColumnDef } from '@tanstack/react-table';
import { EyeIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  MemberCardOperationRead,
  MemberCardOperationTypeNames,
  PaymentMethod,
  PaymentMethodNames,
} from '@/types/api';
import { formatDate } from '@/lib/utils';

// 创建会员卡操作记录表格列定义
export function createCardOperationColumns({
  onViewDetail,
}: {
  onViewDetail?: (operation: MemberCardOperationRead) => void;
} = {}): ColumnDef<MemberCardOperationRead>[] {
  return [
    // 操作ID
    {
      accessorKey: 'id',
      header: '操作ID',
      cell: ({ row }) => <div className="font-medium">{row.original.id}</div>,
      size: 80,
    },

    // 操作类型
    {
      accessorKey: 'operation_type',
      header: '操作类型',
      cell: ({ row }) => {
        const operationType = row.original.operation_type;
        return (
          <Badge variant="outline">
            {MemberCardOperationTypeNames[operationType] || operationType}
          </Badge>
        );
      },
      size: 100,
    },

    // 操作描述
    {
      accessorKey: 'operation_description',
      header: '操作描述',
      cell: ({ row }) => (
        <div className="text-sm">{row.original.operation_description}</div>
      ),
      size: 200,
    },

    // 余额变化
    {
      accessorKey: 'amount_change',
      header: '余额变化',
      cell: ({ row }) => {
        const change = row.original.amount_change;

        if (change === null || change === undefined) {
          return <div className="text-center">-</div>;
        }

        return (
          <div
            className={`text-right ${change > 0 ? 'text-green-600' : change < 0 ? 'text-red-600' : ''}`}
          >
            {change > 0 ? `+${change}` : change}
          </div>
        );
      },
      size: 100,
    },

    // 状态变化
    {
      id: 'status_change',
      header: '状态变化',
      cell: ({ row }) => {
        const { status_before, status_after } = row.original;

        if (!status_before && !status_after) {
          return <div className="text-center">-</div>;
        }

        return (
          <div className="text-sm">
            {status_before ? (
              <Badge variant="outline" className="mr-2">
                {status_before}
              </Badge>
            ) : null}
            {status_before && status_after ? '→' : ''}
            {status_after ? (
              <Badge variant="outline" className="ml-2">
                {status_after}
              </Badge>
            ) : null}
          </div>
        );
      },
      size: 150,
    },

    // 支付方式
    {
      accessorKey: 'payment_method',
      header: '支付方式',
      cell: ({ row }) => {
        const paymentMethod = row.original
          .payment_method as PaymentMethod | null;

        if (!paymentMethod) {
          return <div className="text-center">-</div>;
        }

        return <div>{PaymentMethodNames[paymentMethod] || paymentMethod}</div>;
      },
      size: 100,
    },

    // 备注
    {
      accessorKey: 'notes',
      header: '备注',
      cell: ({ row }) => (
        <div className="truncate max-w-xs">{row.original.notes || '-'}</div>
      ),
      size: 200,
    },

    // 操作人
    {
      accessorKey: 'operator_name',
      header: '操作人',
      cell: ({ row }) => {
        const { operator_name, operator_type } = row.original;

        if (!operator_name) {
          return <div className="text-center">-</div>;
        }

        return (
          <div className="flex flex-col">
            <span>{operator_name}</span>
            {operator_type && (
              <span className="text-xs text-muted-foreground">
                {operator_type}
              </span>
            )}
          </div>
        );
      },
      size: 100,
    },

    // 操作时间
    {
      accessorKey: 'created_at',
      header: '操作时间',
      cell: ({ row }) => (
        <div className="text-muted-foreground whitespace-nowrap">
          {formatDate(row.original.created_at)}
        </div>
      ),
      size: 150,
    },

    // 操作按钮
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }) => {
        if (!onViewDetail) return null;

        return (
          <Button
            variant="ghost"
            size="icon"
            onClick={() => onViewDetail(row.original)}
            title="查看详情"
          >
            <EyeIcon className="h-4 w-4" />
          </Button>
        );
      },
      size: 60,
    },
  ];
}
