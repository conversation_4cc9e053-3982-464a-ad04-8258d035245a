'use client';

import { ColumnDef } from '@tanstack/react-table';
import { PencilIcon } from 'lucide-react';
import { format, differenceInMonths, differenceInYears } from 'date-fns';
import { zhCN } from 'date-fns/locale';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

import { MemberRead, MemberType, MemberStatus } from '@/types/api';

interface MemberColumnsProps {
  onView?: (member: MemberRead) => void;
  onUnbindWechat?: (member: MemberRead) => void;
  onDelete?: (member: MemberRead) => void;
  onChangeSales?: (member: MemberRead) => void;
  salesmen?: Array<{ id: number; username: string }>;
}

// 会员类型标签样式
const getMemberTypeVariant = (type: MemberType) => {
  switch (type) {
    case MemberType.TRIAL:
      return 'warning'; // 试听用警告色
    case MemberType.FORMAL:
      return 'info'; // 正式会员用信息色
    case MemberType.VIP:
      return 'success'; // VIP用成功色
    default:
      return 'secondary';
  }
};

// 会员状态标签样式
const getMemberStatusVariant = (status: MemberStatus) => {
  switch (status) {
    case MemberStatus.ACTIVE:
      return 'default';
    case MemberStatus.SILENT:
      return 'secondary';
    case MemberStatus.FROZEN:
      return 'outline';
    case MemberStatus.CANCELLED:
      return 'destructive';
    default:
      return 'secondary';
  }
};

// 会员卡状态颜色
const getMemberCardColor = (member: MemberRead) => {
  // 根据会员卡信息和课程数据判断状态
  const hasCard = member.primary_member_card_id;
  const totalClasses = member.total_classes;
  const completedClasses = member.completed_classes;
  const remainingClasses = totalClasses - completedClasses;

  if (!hasCard || member.member_status === MemberStatus.FROZEN) {
    return 'bg-muted border-border'; // 冻结
  }

  if (remainingClasses <= 0) {
    return 'bg-destructive/10 border-destructive/30 text-destructive'; // 过期/无余额
  }

  if (remainingClasses <= 5) {
    return 'bg-warning/10 border-warning/30 text-warning'; // 即将过期或余额<5次
  }

  return 'bg-success/10 border-success/30 text-success'; // 正常
};

// 会员类型中文映射
const memberTypeLabels = {
  [MemberType.TRIAL]: '试听',
  [MemberType.FORMAL]: '正式',
  [MemberType.VIP]: 'VIP',
};

// 会员状态中文映射
const memberStatusLabels = {
  [MemberStatus.ACTIVE]: '活跃',
  [MemberStatus.SILENT]: '沉默',
  [MemberStatus.FROZEN]: '冻结',
  [MemberStatus.CANCELLED]: '已取消',
};

// 计算年龄
const calculateAge = (birthday: string) => {
  const birthDate = new Date(birthday);
  const now = new Date();
  const years = differenceInYears(now, birthDate);
  const months = differenceInMonths(now, birthDate) % 12;

  return `${years}岁${months}月`;
};

export function createMemberColumns({
  onView,
  onUnbindWechat,
  onDelete,
  onChangeSales,
  salesmen = [],
}: MemberColumnsProps): ColumnDef<MemberRead>[] {
  return [
    {
      accessorKey: 'name',
      header: '会员基本信息',
      cell: ({ row }) => {
        const member = row.original;
        return (
          <div
            className={`flex items-center gap-2 p-1.5 rounded-md border dark:border-border ${getMemberCardColor(member)}`}
          >
            <Avatar className="h-7 w-7">
              <AvatarImage
                src={member.wechat_avatar || member.avatar_url || ''}
                alt={member.name}
              />
              <AvatarFallback className="text-xs">
                {member.name.charAt(0)}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="font-medium text-sm">{member.name}</div>
              <div className="text-xs text-muted-foreground">
                {member.gender && `${member.gender === 'male' ? '男' : '女'}`}
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'phone',
      header: '手机号',
      cell: ({ row }) => {
        const member = row.original;
        return <div className="text-primary font-bold">{member.phone}</div>;
      },
    },
    {
      accessorKey: 'birthday',
      header: '生日',
      cell: ({ row }) => {
        const member = row.original;
        if (!member.birthday)
          return <div className="text-muted-foreground text-xs">未设置</div>;

        const formattedDate = format(new Date(member.birthday), 'yyyy-MM-dd', {
          locale: zhCN,
        });
        const age = calculateAge(member.birthday);

        return (
          <div className="space-y-0.5">
            <div className="text-xs">{formattedDate}</div>
            <div className="text-primary font-medium text-xs">{age}</div>
          </div>
        );
      },
    },
    {
      accessorKey: 'member_card',
      header: '会员卡信息',
      cell: ({ row }) => {
        const member = row.original;
        const totalClasses = member.total_classes;
        const completedClasses = member.completed_classes;
        const remainingClasses = totalClasses - completedClasses;

        return (
          <div className="space-y-0.5">
            <div className="text-xs">
              <span className="font-medium">卡名:</span>{' '}
              {member.primary_member_card_name || '无'}
            </div>
            <div className="text-xs">
              <span className="font-medium">剩余:</span> {remainingClasses}/
              {totalClasses} 课节
            </div>
            <div className="text-xs">
              <span className="font-medium">消费:</span> ¥{member.total_spent}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'course_data',
      header: '课程数据',
      cell: ({ row }) => {
        const member = row.original;
        // 这里假设1对1和团课数据包含在统计中，实际可能需要额外的API字段
        const oneOnOneCompleted = Math.floor(member.completed_classes * 0.7); // 假设70%是1对1
        const oneOnOneTotal = Math.floor(member.total_classes * 0.7);
        const oneOnOneRemaining = oneOnOneTotal - oneOnOneCompleted;

        const groupCompleted = member.completed_classes - oneOnOneCompleted;
        const groupTotal = member.total_classes - oneOnOneTotal;
        const groupRemaining = groupTotal - groupCompleted;

        // 高亮条件：只要未开课/总课数不为0/0
        const shouldHighlight = oneOnOneTotal > 0 || oneOnOneCompleted > 0;

        return (
          <div className="space-y-0.5">
            <div className="text-xs">
              <span>1对1(未开课/总课数):</span>{' '}
              <span
                className={
                  shouldHighlight ? 'text-destructive font-medium' : ''
                }
              >
                {oneOnOneRemaining}/{oneOnOneTotal}
              </span>
            </div>
            <div className="text-xs">
              <span>团课(未开课/总课数):</span> {groupRemaining}/{groupTotal}
            </div>
            <div className="text-xs">
              <span>评价老师次数:</span> {member.rating_count}次
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'member_type',
      header: '会员属性',
      cell: ({ row }) => {
        const member = row.original;
        // 根据会员的agent_id找到对应的销售人员名称
        const salesName = member.agent_id
          ? salesmen.find(s => s.id === member.agent_id)?.username || '未知销售'
          : '未分配';

        return (
          <div className="space-y-1">
            <Badge
              variant={getMemberTypeVariant(member.member_type)}
              className="text-xs py-0.5"
            >
              {memberTypeLabels[member.member_type]}
            </Badge>
            <div className="text-xs flex items-center gap-1">
              <span>销售:</span>{' '}
              <span className="text-destructive font-medium">{salesName}</span>
              {onChangeSales && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-5 w-5"
                  onClick={e => {
                    e.stopPropagation();
                    onChangeSales(member);
                  }}
                >
                  <PencilIcon className="h-3 w-3" />
                </Button>
              )}
            </div>
            <div className="text-xs">
              <span>注册:</span>{' '}
              {format(new Date(member.registered_at), 'yyyy-MM-dd', {
                locale: zhCN,
              })}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'member_status',
      header: '状态',
      cell: ({ row }) => {
        const member = row.original;
        return (
          <div className="space-y-1">
            <Badge
              variant={getMemberStatusVariant(member.member_status)}
              className="text-xs py-0.5"
            >
              {memberStatusLabels[member.member_status]}
            </Badge>
            <div className="text-xs text-muted-foreground">
              {member.wechat_openid ? '已绑定微信' : '未绑定微信'}
            </div>
            {member.last_login_at && (
              <div className="text-xs text-muted-foreground">
                最后登录:{' '}
                {format(new Date(member.last_login_at), 'MM-dd HH:mm', {
                  locale: zhCN,
                })}
              </div>
            )}
          </div>
        );
      },
    },
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }) => {
        const member = row.original;

        return (
          <div className="flex items-center gap-1.5">
            {onView && (
              <Button
                variant="outline"
                size="xs"
                onClick={() => onView(member)}
                title="查看详情"
              >
                查看
              </Button>
            )}
            {onUnbindWechat && member.wechat_openid && (
              <Button
                variant="outline"
                size="xs"
                onClick={() => onUnbindWechat(member)}
                title="解绑微信"
              >
                解绑微信
              </Button>
            )}
            {onDelete && (
              <Button
                variant="outline"
                size="xs"
                onClick={() => onDelete(member)}
                title="删除"
              >
                删除
              </Button>
            )}
          </div>
        );
      },
    },
  ];
}
