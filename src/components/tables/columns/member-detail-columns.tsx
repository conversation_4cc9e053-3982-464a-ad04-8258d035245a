'use client';

import { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { AlertCircle } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

import {
  MemberCardOperationRead,
  MemberCardOperationType,
  MemberCardOperationTypeNames,
} from '@/types/api/member-card';
import { MemberCardRead } from '@/types/api';

// 操作类型对应的徽章变体
const operationTypeBadgeVariant = (
  type: MemberCardOperationType
):
  | 'default'
  | 'secondary'
  | 'outline'
  | 'destructive'
  | 'success'
  | 'warning'
  | 'info' => {
  switch (type) {
    case MemberCardOperationType.RECHARGE:
      return 'success';
    case MemberCardOperationType.DIRECT_BOOKING:
    case MemberCardOperationType.FIXED_SCHEDULE_BOOKING:
    case MemberCardOperationType.ADMIN_BOOKING:
      return 'info';
    case MemberCardOperationType.MANUAL_DEDUCTION:
      return 'warning';
    case MemberCardOperationType.MEMBER_CANCEL_BOOKING:
    case MemberCardOperationType.ADMIN_CANCEL_BOOKING:
      return 'destructive';
    case MemberCardOperationType.FREEZE_CARD:
      return 'secondary';
    case MemberCardOperationType.UNFREEZE_CARD:
      return 'success';
    default:
      return 'outline';
  }
};

// 会员卡操作记录表格列定义
export const createOperationColumns = (
  memberCards: MemberCardRead[] = []
): ColumnDef<MemberCardOperationRead>[] => [
  {
    accessorKey: 'member_card_name',
    header: '会员卡名',
    cell: ({ row }) => {
      const cardName = row.getValue('member_card_name') as string | null;
      const cardId = row.original.member_card_id;

      // 如果有会员卡名，直接使用
      if (cardName) return <span>{cardName}</span>;

      // 如果没有会员卡名但有会员卡ID，从已获取的会员卡数据中查找
      if (cardId && memberCards.length > 0) {
        // 将两边都转为字符串比较
        const cardIdStr = String(cardId);
        const matchingCard = memberCards.find(
          card => String(card.id) === cardIdStr
        );
        if (matchingCard) {
          return <span>{matchingCard.name || `会员卡 #${cardId}`}</span>;
        }
      }

      // 默认显示
      return (
        <span className="text-muted-foreground">未知卡 #{cardId || '?'}</span>
      );
    },
  },
  {
    accessorKey: 'created_at',
    header: '操作时间',
    cell: ({ row }) => {
      const created_at = row.getValue('created_at') as string;
      return <span>{format(new Date(created_at), 'yyyy-MM-dd HH:mm')}</span>;
    },
  },
  {
    accessorKey: 'operation_type',
    header: '操作类型',
    cell: ({ row }) => {
      const operation_type = row.getValue(
        'operation_type'
      ) as MemberCardOperationType;
      const variant = operationTypeBadgeVariant(operation_type);

      return (
        <Badge variant={variant} className="font-normal">
          {MemberCardOperationTypeNames[operation_type] || operation_type}
        </Badge>
      );
    },
  },
  {
    accessorKey: 'amount_change',
    header: '余额变化',
    cell: ({ row }) => {
      const amountChange = row.getValue('amount_change') as number | null;

      if (amountChange === null)
        return <span className="text-muted-foreground">-</span>;

      const isPositive = amountChange > 0;

      return (
        <span
          className={cn(
            'font-medium',
            isPositive ? 'text-success' : 'text-destructive'
          )}
        >
          {isPositive ? '+' : ''}
          {amountChange}
        </span>
      );
    },
  },
  {
    id: 'balance',
    header: '原始余额/操作后余额',
    cell: ({ row }) => {
      const balanceBefore = row.original.balance_before;
      const balanceAfter = row.original.balance_after;
      const amountChange = row.original.amount_change;

      // 检查余额计算是否一致
      const isBalanceConsistent =
        balanceBefore !== null &&
        balanceAfter !== null &&
        amountChange !== null &&
        balanceBefore + amountChange === balanceAfter;

      return (
        <div className="flex items-center">
          <span>
            {balanceBefore !== null ? balanceBefore : '-'} /{' '}
            {balanceAfter !== null ? balanceAfter : '-'}
          </span>
          {!isBalanceConsistent &&
            balanceBefore !== null &&
            balanceAfter !== null &&
            amountChange !== null && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <AlertCircle className="h-4 w-4 ml-1 text-warning" />
                  </TooltipTrigger>
                  <TooltipContent className="bg-warning/10 border-warning/30">
                    <p className="text-warning font-medium">余额计算不一致</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
        </div>
      );
    },
  },
  {
    accessorKey: 'actual_amount',
    header: '实收金额',
    cell: ({ row }) => {
      const actualAmount = row.getValue('actual_amount') as number | null;
      return <span>{actualAmount !== null ? `¥${actualAmount}` : '-'}</span>;
    },
  },
  {
    accessorKey: 'bonus_amount',
    header: '赠送金额',
    cell: ({ row }) => {
      const bonusAmount = row.getValue('bonus_amount') as number | null;
      return <span>{bonusAmount !== null ? `¥${bonusAmount}` : '-'}</span>;
    },
  },
  {
    accessorKey: 'operator_name',
    header: '操作人',
    cell: ({ row }) => {
      const operatorName = row.getValue('operator_name') as string | null;
      return <span>{operatorName || '系统'}</span>;
    },
  },
  {
    accessorKey: 'notes',
    header: '备注',
    cell: ({ row }) => {
      const notes = row.getValue('notes') as string | null;
      return (
        <span className={notes ? '' : 'text-muted-foreground'}>
          {notes || '-'}
        </span>
      );
    },
  },
];
