import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { AuthState, AdminUser, LoginRequest } from '@/types/auth';
import { setTokenCookie, clearTokenCookie, clearStoredAuth } from '@/lib/auth/utils';

interface AuthStore extends AuthState {
  // 操作方法
  login: (credentials: LoginRequest) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
  setUser: (user: AdminUser | null) => void;
  setToken: (token: string | null) => void;
  setLoading: (loading: boolean) => void;
}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,

      // 登录
      login: async (credentials: LoginRequest) => {
        set({ isLoading: true });
        try {
          const { adminLogin } = await import('@/lib/api/auth');
          const response = await adminLogin(credentials);

          // 设置 Cookie 用于 SSR
          setTokenCookie(response.access_token);

          set({
            user: response.user,
            token: response.access_token,
            isAuthenticated: true,
            isLoading: false,
          });
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      // 登出
      logout: async () => {
        try {
          const { logout: apiLogout } = await import('@/lib/api/auth');
          await apiLogout();
        } catch (error) {
          console.error('Logout error:', error);
        } finally {
          // 清除存储的认证信息
          clearStoredAuth();
          clearTokenCookie();

          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
          });
        }
      },

      // 刷新令牌
      refreshToken: async () => {
        try {
          const { refreshToken: apiRefreshToken } = await import('@/lib/api/auth');
          const response = await apiRefreshToken();

          // 更新 Cookie
          setTokenCookie(response.access_token);

          set({
            user: response.user,
            token: response.access_token,
            isAuthenticated: true,
          });
        } catch (error) {
          console.error('Refresh token error:', error);
          // 刷新失败，清除认证状态
          get().logout();
          throw error;
        }
      },

      // 设置用户
      setUser: (user: AdminUser | null) => {
        set({
          user,
          isAuthenticated: !!user,
        });
      },

      // 设置令牌
      setToken: (token: string | null) => {
        set({ token });
      },

      // 设置加载状态
      setLoading: (isLoading: boolean) => {
        set({ isLoading });
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
