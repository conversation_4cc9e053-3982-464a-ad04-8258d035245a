import { NextRequest, NextResponse } from 'next/server';

// 公开路由（不需要认证）
const publicRoutes = ['/login'];

// 认证路由（已登录用户不应访问）
const authRoutes = ['/login'];

// 受保护的路由前缀
const protectedRoutes = ['/dashboard', '/users', '/members', '/settings'];

/**
 * 检查路由是否为公开路由
 */
function isPublicRoute(pathname: string): boolean {
  return publicRoutes.some(route => pathname === route || pathname.startsWith(`${route}/`));
}

/**
 * 检查路由是否为认证路由
 */
function isAuthRoute(pathname: string): boolean {
  return authRoutes.some(route => pathname === route || pathname.startsWith(`${route}/`));
}

/**
 * 检查路由是否为受保护路由
 */
function isProtectedRoute(pathname: string): boolean {
  return protectedRoutes.some(route => pathname.startsWith(route));
}

/**
 * 从请求中获取认证令牌
 */
function getTokenFromRequest(request: NextRequest): string | null {
  // 从 Cookie 中获取令牌
  const tokenFromCookie = request.cookies.get('auth-token')?.value;
  if (tokenFromCookie) {
    return tokenFromCookie;
  }

  // 从 Authorization 头中获取令牌
  const authHeader = request.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  return null;
}

/**
 * 验证令牌是否有效
 */
function isValidToken(token: string): boolean {
  // TODO: 实现真实的令牌验证逻辑
  // 这里可以解析 JWT 并验证其有效性
  
  // 临时实现：检查令牌是否存在且不为空
  return Boolean(token && token.length > 0);
}

/**
 * 认证中间件
 */
export function authMiddleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const token = getTokenFromRequest(request);
  const isAuthenticated = token ? isValidToken(token) : false;

  // 如果是公开路由，直接允许访问
  if (isPublicRoute(pathname)) {
    return NextResponse.next();
  }

  // 如果是认证路由（如登录页）且用户已登录，重定向到仪表盘
  if (isAuthRoute(pathname) && isAuthenticated) {
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  // 如果是受保护路由且用户未登录，重定向到登录页
  if (isProtectedRoute(pathname) && !isAuthenticated) {
    const loginUrl = new URL('/login', request.url);
    loginUrl.searchParams.set('redirect', pathname);
    return NextResponse.redirect(loginUrl);
  }

  // 默认允许访问
  return NextResponse.next();
}

/**
 * 中间件配置
 */
export const config = {
  matcher: [
    /*
     * 匹配所有路径除了：
     * - api 路由
     * - _next/static (静态文件)
     * - _next/image (图片优化)
     * - favicon.ico
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
