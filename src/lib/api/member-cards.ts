import apiClient from './client';
import { API_ENDPOINTS } from '@/constants/api';
import type {
  MemberCardCreate,
  MemberCardUpdate,
  MemberCardList,
  MemberCardRead,
  MemberCardOperationRead,
  RechargeRequest,
  RechargeResponse,
  DeductRequest,
  DeductResponse,
} from '@/types/api';
import type { ApiResponse } from '@/types/global';

// 定义响应类型别名
type MemberCardListResponse = ApiResponse<MemberCardList[]>;
type MemberCardDetailResponse = ApiResponse<MemberCardRead>;
type MemberCardOperationListResponse = ApiResponse<MemberCardOperationRead[]>;
type RechargeResponseType = ApiResponse<RechargeResponse>;
type DeductResponseType = ApiResponse<DeductResponse>;

// 查询参数类型
export interface CardQueryParams {
  member_id?: number | string;
  card_type?: string;
  status?: string;
  template_id?: number | string;
  search_keyword?: string;
  page?: number;
  size?: number;
  sort_by?: string;
  sort_order?: string;
  [key: string]: unknown;
}

/**
 * 获取会员卡列表
 */
export async function getMemberCards(
  params?: CardQueryParams
): Promise<MemberCardList[]> {
  const response = await apiClient.get<MemberCardListResponse>(
    API_ENDPOINTS.MEMBER_CARDS.LIST,
    { params }
  );

  return response.data.data || [];
}

/**
 * 根据会员ID获取会员卡列表
 */
export async function getMemberCardsByMember(
  memberId: string
): Promise<MemberCardRead[]> {
  const response = await apiClient.get<ApiResponse<MemberCardRead[]>>(
    API_ENDPOINTS.MEMBER_CARDS.GET_BY_MEMBER(memberId)
  );

  return response.data.data || [];
}

/**
 * 获取会员卡详情
 */
export async function getMemberCardDetail(id: string): Promise<MemberCardRead> {
  const response = await apiClient.get<MemberCardDetailResponse>(
    API_ENDPOINTS.MEMBER_CARDS.GET(id)
  );

  return response.data.data!;
}

/**
 * 创建会员卡
 */
export async function createMemberCard(
  data: MemberCardCreate
): Promise<MemberCardRead> {
  const response = await apiClient.post<MemberCardDetailResponse>(
    API_ENDPOINTS.MEMBER_CARDS.CREATE,
    data
  );

  return response.data.data!;
}

/**
 * 更新会员卡
 */
export async function updateMemberCard(
  id: string,
  data: MemberCardUpdate
): Promise<MemberCardRead> {
  const response = await apiClient.put<MemberCardDetailResponse>(
    API_ENDPOINTS.MEMBER_CARDS.UPDATE(id),
    data
  );

  return response.data.data!;
}

/**
 * 冻结会员卡
 */
export async function freezeMemberCard(
  id: string,
  reason?: string
): Promise<MemberCardRead> {
  const response = await apiClient.patch<MemberCardDetailResponse>(
    API_ENDPOINTS.MEMBER_CARDS.FREEZE(id),
    {},
    { params: { reason } }
  );

  return response.data.data!;
}

/**
 * 解冻会员卡
 */
export async function unfreezeMemberCard(id: string): Promise<MemberCardRead> {
  const response = await apiClient.patch<MemberCardDetailResponse>(
    API_ENDPOINTS.MEMBER_CARDS.UNFREEZE(id)
  );

  return response.data.data!;
}

/**
 * 注销会员卡
 */
export async function cancelMemberCard(
  id: string,
  reason?: string
): Promise<MemberCardRead> {
  const response = await apiClient.patch<MemberCardDetailResponse>(
    API_ENDPOINTS.MEMBER_CARDS.CANCEL(id),
    {},
    { params: { reason } }
  );

  return response.data.data!;
}

/**
 * 会员卡充值
 */
export async function rechargeMemberCard(
  id: string,
  data: RechargeRequest
): Promise<RechargeResponse> {
  const response = await apiClient.post<RechargeResponseType>(
    API_ENDPOINTS.MEMBER_CARDS.RECHARGE(id),
    data
  );

  return response.data.data!;
}

/**
 * 会员卡扣费
 */
export async function deductMemberCard(
  id: string,
  data: DeductRequest
): Promise<DeductResponse> {
  const response = await apiClient.post<DeductResponseType>(
    API_ENDPOINTS.MEMBER_CARDS.DEDUCT(id),
    data
  );

  return response.data.data!;
}

/**
 * 获取会员卡充值历史
 */
export async function getMemberCardRechargeHistory(
  id: string,
  limit?: number
): Promise<MemberCardOperationRead[]> {
  const response = await apiClient.get<MemberCardOperationListResponse>(
    API_ENDPOINTS.MEMBER_CARDS.RECHARGE_HISTORY(id),
    { params: { limit } }
  );

  return response.data.data || [];
}

/**
 * 获取会员卡消费历史
 */
export async function getMemberCardConsumptionHistory(
  id: string,
  limit?: number
): Promise<MemberCardOperationRead[]> {
  const response = await apiClient.get<MemberCardOperationListResponse>(
    API_ENDPOINTS.MEMBER_CARDS.CONSUMPTION_HISTORY(id),
    { params: { limit } }
  );

  return response.data.data || [];
}

// 卡片操作记录查询参数类型
export interface CardOperationQueryParams {
  member_id?: number | string;
  member_card_id?: number | string;
  operation_type?: string;
  operation_types?: string[]; // 修改为字符串数组类型
  scheduled_class_id?: number | string;
  operator_id?: number | string;
  date_from?: string;
  date_to?: string;
  page?: number;
  size?: number;
  sort_by?: string;
  sort_order?: string;
  [key: string]: unknown;
}

/**
 * 获取会员卡操作记录
 */
export async function getCardOperations(
  params?: CardOperationQueryParams
): Promise<MemberCardOperationRead[]> {
  const response = await apiClient.get<MemberCardOperationListResponse>(
    API_ENDPOINTS.CARD_OPERATIONS.LIST,
    { params }
  );

  return response.data.data || [];
}

/**
 * 获取会员卡操作记录详情
 */
export async function getCardOperationDetail(
  id: string
): Promise<MemberCardOperationRead> {
  const response = await apiClient.get<ApiResponse<MemberCardOperationRead>>(
    API_ENDPOINTS.CARD_OPERATIONS.GET(id)
  );

  return response.data.data!;
}
