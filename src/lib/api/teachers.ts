// 教师管理 API 函数

import apiClient from './client';
import { API_ENDPOINTS } from '@/constants/api';
import type {
  TeacherCreate,
  TeacherUpdate,
  TeacherRead,

  TeacherQueryParams,
  TeacherSearchParams,
  TeacherAvailableParams,
  TeacherStatusUpdate,
  TeacherTagAssign,
  TeacherTagBatch,
  TeacherResponse,
  TeacherDetailResponse,
  TeacherListResponse,
  TeacherSimpleListResponse,
  TeacherStatisticsResponse,
} from '@/types/api';

// ==================== 基础 CRUD 操作 ====================

/**
 * 获取教师列表
 */
export async function getTeachers(params?: TeacherQueryParams): Promise<TeacherListResponse> {
  const response = await apiClient.get<TeacherListResponse>(
    API_ENDPOINTS.TEACHERS.LIST,
    { params }
  );
  return response.data;
}

/**
 * 获取所有教师
 */
export async function getAllTeachers(): Promise<TeacherSimpleListResponse> {
  const response = await apiClient.get<TeacherSimpleListResponse>(
    API_ENDPOINTS.TEACHERS.LISTALL,
  );
  return response.data;
}

/**
 * 获取教师详情
 */
export async function getTeacher(id: string): Promise<TeacherDetailResponse> {
  const response = await apiClient.get<TeacherDetailResponse>(
    API_ENDPOINTS.TEACHERS.DETAIL(id)
  );
  return response.data;
}

/**
 * 创建教师
 */
export async function createTeacher(data: TeacherCreate): Promise<TeacherRead> {
  const response = await apiClient.post<TeacherResponse>(
    API_ENDPOINTS.TEACHERS.LIST,
    data
  );
  return response.data.data!;
}

/**
 * 更新教师信息
 */
export async function updateTeacher(id: string, data: TeacherUpdate): Promise<TeacherRead> {
  const response = await apiClient.post<TeacherResponse>(
    API_ENDPOINTS.TEACHERS.UPDATE(id),
    data
  );
  return response.data.data!;
}

/**
 * 删除教师
 */
export async function deleteTeacher(id: string): Promise<void> {
  await apiClient.post(API_ENDPOINTS.TEACHERS.DELETE(id));
}

// ==================== 状态管理 ====================

/**
 * 更新教师状态
 */
export async function updateTeacherStatus(id: string, data: TeacherStatusUpdate): Promise<TeacherRead> {
  const response = await apiClient.post<TeacherResponse>(
    API_ENDPOINTS.TEACHERS.STATUS(id),
    data
  );
  return response.data.data!;
}

/**
 * 激活教师
 */
export async function activateTeacher(id: string): Promise<TeacherRead> {
  const response = await apiClient.post<TeacherResponse>(
    API_ENDPOINTS.TEACHERS.ACTIVATE(id)
  );
  return response.data.data!;
}

/**
 * 停用教师
 */
export async function deactivateTeacher(id: string): Promise<TeacherRead> {
  const response = await apiClient.post<TeacherResponse>(
    API_ENDPOINTS.TEACHERS.DEACTIVATE(id)
  );
  return response.data.data!;
}

// ==================== 统计信息 ====================

/**
 * 更新教师统计信息
 */
export async function updateTeacherStats(
  id: string,
  params: {
    class_completed?: boolean;
    class_cancelled?: boolean;
    class_no_show?: boolean;
    earnings_added?: number;
  }
): Promise<TeacherRead> {
  const response = await apiClient.post<TeacherResponse>(
    API_ENDPOINTS.TEACHERS.UPDATE_STATS(id),
    null,
    { params }
  );
  return response.data.data!;
}

/**
 * 获取教师统计信息
 */
export async function getTeacherStatistics(): Promise<TeacherStatisticsResponse> {
  const response = await apiClient.get<TeacherStatisticsResponse>(
    API_ENDPOINTS.TEACHERS.STATISTICS
  );
  return response.data;
}

// ==================== 特殊查询 ====================

/**
 * 按优先级获取教师
 */
export async function getTeachersByPriority(limit: number = 10): Promise<TeacherSimpleListResponse> {
  const response = await apiClient.get<TeacherSimpleListResponse>(
    API_ENDPOINTS.TEACHERS.PRIORITY,
    { params: { limit } }
  );
  return response.data;
}

/**
 * 获取对会员可见的教师
 */
export async function getAvailableTeachers(params?: TeacherAvailableParams): Promise<TeacherSimpleListResponse> {
  const response = await apiClient.get<TeacherSimpleListResponse>(
    API_ENDPOINTS.TEACHERS.AVAILABLE,
    { params }
  );
  return response.data;
}

/**
 * 搜索教师
 */
export async function searchTeachers(params: TeacherSearchParams): Promise<TeacherSimpleListResponse> {
  const response = await apiClient.get<TeacherSimpleListResponse>(
    API_ENDPOINTS.TEACHERS.SEARCH,
    { params }
  );
  return response.data;
}

// ==================== 标签管理 ====================

/**
 * 获取教师标签列表
 */
export async function getTeacherTagList(id: string): Promise<Array<{ id: number; name: string; category_name: string; status: string }>> {
  const response = await apiClient.get<{ success: boolean; data: Array<{ id: number; name: string; category_name: string; status: string }>; message: string }>(
    API_ENDPOINTS.TEACHERS.TAGS.LIST(id)
  );
  return response.data.data!;
}

/**
 * 为教师分配标签
 */
export async function assignTeacherTags(id: string, data: TeacherTagAssign): Promise<void> {
  await apiClient.post(API_ENDPOINTS.TEACHERS.TAGS.ASSIGN(id), data);
}

/**
 * 移除教师标签
 */
export async function removeTeacherTags(id: string, tagIds: number[]): Promise<void> {
  await apiClient.post(
    API_ENDPOINTS.TEACHERS.TAGS.REMOVE(id),
    null,
    { params: { tag_ids: tagIds.join(',') } }
  );
}

/**
 * 批量管理教师标签
 */
export async function batchManageTeacherTagsOperation(data: TeacherTagBatch): Promise<void> {
  await apiClient.post(API_ENDPOINTS.TEACHERS.TAGS.BATCH, data);
}

// ==================== 头像管理 ====================

/**
 * 上传教师头像
 */
export async function uploadTeacherAvatar(id: string, file: File): Promise<{ avatar_url: string }> {
  const formData = new FormData();
  formData.append('file', file);

  const response = await apiClient.post<{ success: boolean; data: { avatar_url: string }; message: string }>(
    API_ENDPOINTS.TEACHERS.AVATAR(id),
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }
  );
  return response.data.data!;
}

/**
 * 删除教师头像
 */
export async function deleteTeacherAvatar(id: string): Promise<void> {
  await apiClient.post(API_ENDPOINTS.TEACHERS.AVATAR_DELETE(id));
}
