import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { toast } from 'sonner';
import { env } from '@/config/env';
import type { ApiResponse, ApiError } from '@/types/global';

// 全局锁，防止重复跳转登录页
let isRedirectingToLogin = false;

// 创建 Axios 实例
const apiClient: AxiosInstance = axios.create({
  baseURL: env.API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
  // 自定义参数序列化，使数组参数不带[]后缀
  paramsSerializer: {
    indexes: null, // 不使用索引方式 (即不使用 a[0]=b&a[1]=c 的形式)
  },
});

// 请求拦截器
apiClient.interceptors.request.use(
  config => {
    // 添加认证头
    const token = getAuthToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // 添加请求日志
    if (env.NODE_ENV === 'development') {
      console.log(
        `🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`,
        {
          data: config.data,
          params: config.params,
        }
      );
    }

    return config;
  },
  error => {
    console.error('❌ Request Error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    // 添加响应日志
    if (env.NODE_ENV === 'development') {
      console.log(
        `✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url}`,
        {
          status: response.status,
          data: response.data,
        }
      );
    }

    // 检查业务状态码 - 业务错误不在这里处理toast，让上层Hook处理
    if (!response.data.success) {
      const error = response.data as ApiError;
      // 只记录日志，不显示toast，避免与Hook层重复
      console.error('API Business Error:', error);
      return Promise.reject(new Error(error.message));
    }

    return response;
  },
  error => {
    console.error('❌ Response Error:', error);

    // 1. 处理网络错误 - 增强版
    if (!error.response) {
      // 检查是否为离线状态
      if (typeof navigator !== 'undefined' && !navigator.onLine) {
        const networkError = new Error('网络连接已断开，请检查网络设置');
        networkError.name = 'NetworkError';
        networkError.stack = error.stack;
        toast.error('网络连接已断开，请检查网络设置');
        return Promise.reject(networkError);
      }

      // 其他网络错误（超时、DNS解析失败等）
      const networkError = new Error('网络连接失败，请检查网络设置');
      networkError.name = 'NetworkError';
      networkError.stack = error.stack;
      toast.error('网络连接失败，请检查网络设置');
      return Promise.reject(networkError);
    }

    // 处理HTTP状态码错误 - 只处理系统级错误
    const { status } = error.response;

    switch (status) {
      case 401:
        // 2. 认证错误防重复跳转
        if (!isRedirectingToLogin) {
          isRedirectingToLogin = true;
          handleUnauthorized();
          // 5秒后重置锁，防止长时间锁定
          setTimeout(() => {
            isRedirectingToLogin = false;
          }, 5000);
        }
        break;
      case 403:
        // 权限错误直接显示，因为这是系统级错误
        toast.error('权限不足');
        break;
      case 500:
        // 服务器错误直接显示，因为这是系统级错误
        toast.error('服务器内部错误');
        break;
      case 404:
      case 422:
      default:
        // 其他错误（404、422、业务错误等）让上层Hook处理
        // 避免重复显示toast
        break;
    }

    // 3. 保留错误堆栈信息 - 创建增强的错误对象
    let errorMessage = '请求失败';

    // 尝试从响应数据中提取API错误消息
    if (error.response?.data) {
      const apiErrorData = error.response.data;
      if (apiErrorData.message) {
        errorMessage = apiErrorData.message;
      }
    }

    const apiError = new Error(errorMessage);
    apiError.name = 'ApiError';
    apiError.stack = error.stack;
    // 保留原始响应信息，便于调试
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (apiError as any).response = error.response;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (apiError as any).config = error.config;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (apiError as any).code = error.code;

    return Promise.reject(apiError);
  }
);

// 获取认证令牌
function getAuthToken(): string | null {
  // 从 localStorage 或其他存储中获取令牌
  try {
    const authStorage = localStorage.getItem('auth-storage');
    if (authStorage) {
      const parsed = JSON.parse(authStorage);
      return parsed.state?.token || null;
    }
  } catch (error) {
    console.error('Failed to get auth token:', error);
  }
  return null;
}

// 处理API错误 - 已移除，业务错误由Hook层统一处理
// function handleApiError 已删除，避免重复的错误提示

// 处理未授权错误
function handleUnauthorized() {
  toast.error('登录已过期，请重新登录');

  // 清除认证信息
  localStorage.removeItem('auth-storage');

  // 重定向到登录页
  if (typeof window !== 'undefined') {
    window.location.href = '/login';
  }
}

// 处理验证错误 - 已移除，422错误由Hook层统一处理
// function handleValidationError 已删除，避免重复的错误提示

export default apiClient;
