import apiClient from './client';
import { API_ENDPOINTS } from '@/constants/api';
import type { AdminLoginRequest, AdminLoginResponse } from '@/types/api';
import type { ApiResponse } from '@/types/global';

// 定义响应类型别名
type LoginApiResponse = ApiResponse<AdminLoginResponse>;

/**
 * 管理员登录
 */
export async function adminLogin(
  credentials: AdminLoginRequest
): Promise<AdminLoginResponse> {
  const response = await apiClient.post<LoginApiResponse>(
    API_ENDPOINTS.AUTH.LOGIN,
    credentials
  );

  return response.data.data!;
}

/**
 * 刷新令牌
 */
export async function refreshToken(): Promise<AdminLoginResponse> {
  const response = await apiClient.post<LoginApiResponse>(
    API_ENDPOINTS.AUTH.REFRESH
  );

  return response.data.data!;
}

/**
 * 登出
 */
export async function logout(): Promise<void> {
  await apiClient.post(API_ENDPOINTS.AUTH.LOGOUT);
}
