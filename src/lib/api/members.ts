import apiClient from './client';
import { API_ENDPOINTS } from '@/constants/api';
import type {
  MemberCreate,
  MemberUpdate,
  MemberRead,
  MemberType,
  MemberStatus,
} from '@/types/api';
import type { ApiResponse, PaginationResponse } from '@/types/global';
import type { PaginationParams } from '@/types/global';

// 定义会员列表查询参数
export interface MemberListParams extends PaginationParams {
  member_type?: MemberType | null;
  member_status?: MemberStatus | null;
  agent_id?: number | null;
  search_keyword?: string | null;
}

// 定义响应类型别名
type MemberListResponse = ApiResponse<PaginationResponse<MemberRead>>;
type MemberDetailResponse = ApiResponse<MemberRead>;

/**
 * 获取会员列表
 */
export async function getMemberList(params?: MemberListParams) {
  const response = await apiClient.get<MemberListResponse>(
    API_ENDPOINTS.MEMBERS.LIST,
    { params }
  );

  return response.data;
}

/**
 * 获取会员详情
 */
export async function getMemberDetail(id: string): Promise<MemberRead> {
  const response = await apiClient.get<MemberDetailResponse>(
    API_ENDPOINTS.MEMBERS.GET(id)
  );

  return response.data.data!;
}

/**
 * 创建会员
 */
export async function createMember(data: MemberCreate): Promise<MemberRead> {
  const response = await apiClient.post<MemberDetailResponse>(
    API_ENDPOINTS.MEMBERS.CREATE,
    data
  );

  return response.data.data!;
}

/**
 * 更新会员
 */
export async function updateMember(
  id: string,
  data: MemberUpdate
): Promise<MemberRead> {
  const response = await apiClient.post<MemberDetailResponse>(
    API_ENDPOINTS.MEMBERS.UPDATE(id),
    data
  );

  return response.data.data!;
}

/**
 * 删除会员
 */
export async function deleteMember(id: string): Promise<void> {
  await apiClient.post(API_ENDPOINTS.MEMBERS.DELETE(id));
}
