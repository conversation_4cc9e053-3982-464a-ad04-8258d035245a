import apiClient from './client';
import { API_ENDPOINTS } from '@/constants/api';
import type {
  UserCreate,
  UserUpdate,
  UserRead,
  ApiListResponse,
  ApiDetailResponse,
} from '@/types/api';
import type { PaginationParams } from '@/types/global';

// 定义用户列表查询参数
export interface UserListParams extends PaginationParams {
  role?: string;
  search?: string;
}

// 定义响应类型别名
type UserListResponse = ApiListResponse<UserRead>;
type UserDetailResponse = ApiDetailResponse<UserRead>;

/**
 * 获取用户列表
 */
export async function getUserList(params?: UserListParams) {
  const response = await apiClient.get<UserListResponse>(
    API_ENDPOINTS.USERS.LIST,
    { params }
  );

  return response.data;
}

/**
 * 获取用户详情
 */
export async function getUserDetail(id: string): Promise<UserRead> {
  const response = await apiClient.get<UserDetailResponse>(
    API_ENDPOINTS.USERS.GET(id)
  );

  return response.data.data!;
}

/**
 * 创建用户
 */
export async function createUser(data: UserCreate): Promise<UserRead> {
  const response = await apiClient.post<UserDetailResponse>(
    API_ENDPOINTS.USERS.CREATE,
    data
  );

  return response.data.data!;
}

/**
 * 更新用户
 */
export async function updateUser(
  id: string,
  data: UserUpdate
): Promise<UserRead> {
  const response = await apiClient.post<UserDetailResponse>(
    API_ENDPOINTS.USERS.UPDATE(id),
    data
  );

  return response.data.data!;
}

/**
 * 删除用户
 */
export async function deleteUser(id: string): Promise<void> {
  await apiClient.post(API_ENDPOINTS.USERS.DELETE(id));
}

/**
 * 获取销售人员列表
 */
export async function getSalesmenList() {
  const response = await apiClient.get<UserListResponse>(
    API_ENDPOINTS.USERS.SALESMEN
  );

  return response.data;
}
