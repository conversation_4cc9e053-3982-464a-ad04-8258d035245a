import apiClient from './client';
import { API_ENDPOINTS } from '@/constants/api';
import type {
  CardTemplateCreate,
  CardTemplateUpdate,
  CardTemplateRead,
  CardTemplateList,
} from '@/types/api';
import type { ApiResponse } from '@/types/global';

// 定义响应类型别名
type CardTemplateListResponse = ApiResponse<CardTemplateList[]>;
type CardTemplateDetailResponse = ApiResponse<CardTemplateRead>;

/**
 * 获取会员卡模板列表
 */
export async function getCardTemplateList(): Promise<CardTemplateList[]> {
  const response = await apiClient.get<CardTemplateListResponse>(
    API_ENDPOINTS.CARD_TEMPLATES.LIST
  );

  return response.data.data || [];
}

/**
 * 获取会员卡模板详情
 */
export async function getCardTemplateDetail(
  id: string
): Promise<CardTemplateRead> {
  const response = await apiClient.get<CardTemplateDetailResponse>(
    API_ENDPOINTS.CARD_TEMPLATES.GET(id)
  );

  return response.data.data!;
}

/**
 * 创建会员卡模板
 */
export async function createCardTemplate(
  data: CardTemplateCreate
): Promise<CardTemplateRead> {
  const response = await apiClient.post<CardTemplateDetailResponse>(
    API_ENDPOINTS.CARD_TEMPLATES.CREATE,
    data
  );

  return response.data.data!;
}

/**
 * 更新会员卡模板
 */
export async function updateCardTemplate(
  id: string,
  data: CardTemplateUpdate
): Promise<CardTemplateRead> {
  const response = await apiClient.put<CardTemplateDetailResponse>(
    API_ENDPOINTS.CARD_TEMPLATES.UPDATE(id),
    data
  );

  return response.data.data!;
}

/**
 * 删除会员卡模板
 */
export async function deleteCardTemplate(id: string): Promise<void> {
  await apiClient.delete(API_ENDPOINTS.CARD_TEMPLATES.DELETE(id));
}

/**
 * 切换会员卡模板状态
 */
export async function toggleCardTemplateStatus(
  id: string
): Promise<CardTemplateRead> {
  const response = await apiClient.patch<CardTemplateDetailResponse>(
    API_ENDPOINTS.CARD_TEMPLATES.TOGGLE_STATUS(id)
  );

  return response.data.data!;
}
