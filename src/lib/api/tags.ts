import apiClient from './client';
import { API_ENDPOINTS } from '@/constants/api';
import type {
  TagCategoryCreate,
  TagCategoryUpdate,
  TagCategoryRead,
  TagCategoryList,
  TagCategoryQueryParams,
  TagCreate,
  TagUpdate,
  TagRead,
  TagList,
  TagWithTeacherCount,
  TagQueryParams,
  TagBatchCreate,
  TagBatchUpdate,
  TeacherTagAssign,
  TeacherTagBatch,
} from '@/types/api';
import type { ApiResponse, ApiListResponse } from '@/types/global';

// 定义响应类型别名
type TagCategoryListResponse = ApiListResponse<TagCategoryList>;
type TagCategoryDetailResponse = ApiResponse<TagCategoryRead>;
type TagListResponse = ApiListResponse<TagList>;
type TagWithCategoryListResponse = ApiListResponse<TagWithTeacherCount>;
type TagDetailResponse = ApiResponse<TagRead>;
type TagBatchResponse = ApiResponse<TagRead[]>;

// ==================== 标签分类相关 ====================

/**
 * 获取标签分类列表
 */
export async function getTagCategories(params?: TagCategoryQueryParams) {
  const response = await apiClient.get<TagCategoryListResponse>(
    API_ENDPOINTS.TAGS.CATEGORIES,
    { params }
  );
  return response.data;
}

/**
 * 获取标签分类详情
 */
export async function getTagCategory(id: string): Promise<TagCategoryRead> {
  const response = await apiClient.get<TagCategoryDetailResponse>(
    API_ENDPOINTS.TAGS.CATEGORY_DETAIL(id)
  );
  return response.data.data!;
}

/**
 * 创建标签分类
 */
export async function createTagCategory(data: TagCategoryCreate): Promise<TagCategoryRead> {
  const response = await apiClient.post<TagCategoryDetailResponse>(
    API_ENDPOINTS.TAGS.CATEGORIES,
    data
  );
  return response.data.data!;
}

/**
 * 更新标签分类
 */
export async function updateTagCategory(
  id: string,
  data: TagCategoryUpdate
): Promise<TagCategoryRead> {
  const response = await apiClient.post<TagCategoryDetailResponse>(
    API_ENDPOINTS.TAGS.CATEGORY_DETAIL(id),
    data
  );
  return response.data.data!;
}

/**
 * 删除标签分类
 */
export async function deleteTagCategory(id: string): Promise<void> {
  await apiClient.delete(API_ENDPOINTS.TAGS.CATEGORY_DETAIL(id));
}

/**
 * 获取指定分类下的标签
 */
export async function getTagsByCategory(
  categoryId: string,
  params?: { status?: string }
) {
  const response = await apiClient.get<TagListResponse>(
    API_ENDPOINTS.TAGS.CATEGORY_TAGS(categoryId),
    { params }
  );
  return response.data;
}

// ==================== 标签相关 ====================

/**
 * 获取标签列表
 */
export async function getTags(params?: TagQueryParams) {
  const response = await apiClient.get<TagWithCategoryListResponse>(
    API_ENDPOINTS.TAGS.LIST,
    { params }
  );
  return response.data;
}

/**
 * 获取激活状态的标签
 */
export async function getActiveTags() {
  const response = await apiClient.get<TagListResponse>(
    API_ENDPOINTS.TAGS.ACTIVE_TAGS
  );
  return response.data;
}

/**
 * 获取标签详情
 */
export async function getTag(id: string): Promise<TagRead> {
  const response = await apiClient.get<TagDetailResponse>(
    API_ENDPOINTS.TAGS.DETAIL(id)
  );
  return response.data.data!;
}

/**
 * 创建标签
 */
export async function createTag(data: TagCreate): Promise<TagRead> {
  const response = await apiClient.post<TagDetailResponse>(
    API_ENDPOINTS.TAGS.CREATE,
    data
  );
  return response.data.data!;
}

/**
 * 更新标签
 */
export async function updateTag(id: string, data: TagUpdate): Promise<TagRead> {
  const response = await apiClient.post<TagDetailResponse>(
    API_ENDPOINTS.TAGS.DETAIL(id),
    data
  );
  return response.data.data!;
}

/**
 * 删除标签
 */
export async function deleteTag(id: string): Promise<void> {
  await apiClient.delete(API_ENDPOINTS.TAGS.DETAIL(id));
}

/**
 * 批量创建标签
 */
export async function batchCreateTags(data: TagBatchCreate): Promise<TagRead[]> {
  const response = await apiClient.post<TagBatchResponse>(
    API_ENDPOINTS.TAGS.BATCH_CREATE,
    data
  );
  return response.data.data!;
}

/**
 * 批量更新标签
 */
export async function batchUpdateTags(data: TagBatchUpdate): Promise<TagRead[]> {
  const response = await apiClient.post<TagBatchResponse>(
    API_ENDPOINTS.TAGS.BATCH_UPDATE,
    data
  );
  return response.data.data!;
}

// ==================== 教师标签关联 ====================

/**
 * 获取教师的标签列表
 */
export async function getTeacherTags(teacherId: string) {
  const response = await apiClient.get<ApiListResponse<TagRead>>(
    API_ENDPOINTS.TAGS.TEACHER_TAGS(teacherId)
  );
  return response.data;
}

/**
 * 为教师分配标签
 */
export async function assignTagsToTeacher(
  teacherId: string,
  data: TeacherTagAssign
): Promise<void> {
  await apiClient.post(API_ENDPOINTS.TAGS.TEACHER_TAGS(teacherId), data);
}

/**
 * 移除教师标签
 */
export async function removeTagsFromTeacher(
  teacherId: string,
  tagIds: number[]
): Promise<void> {
  const tagIdsStr = tagIds.join(',');
  await apiClient.post(
    API_ENDPOINTS.TAGS.TEACHER_TAG_REMOVE(teacherId),
    null,
    { params: { tag_ids: tagIdsStr } }
  );
}

/**
 * 批量管理教师标签
 */
export async function batchManageTeacherTags(data: TeacherTagBatch): Promise<void> {
  await apiClient.post(API_ENDPOINTS.TAGS.TEACHER_TAG_BATCH, data);
}

// ==================== 查询参数类型 ====================

export type TagCategoryListParams = TagCategoryQueryParams;

export type TagListParams = TagQueryParams;

export interface TagsByCategoryParams {
  status?: string;
}
