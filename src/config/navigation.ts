import {
  LucideIcon,
  Home,
  Users,
  UserCheck,
  Settings,
  Bug,
  Code,
  CreditCard,
  Tags,
  GraduationCap,
} from 'lucide-react';
import { env } from './env';

export interface NavItem {
  title: string;
  href: string;
  icon: LucideIcon;
  children?: NavItem[];
  hasNotification?: boolean;
}

// 基础导航项
const baseNavigation: NavItem[] = [
  {
    title: '仪表盘',
    href: '/dashboard',
    icon: Home,
  },
  {
    title: '用户管理',
    href: '/users',
    icon: Users,
    children: [
      {
        title: '用户列表',
        href: '/users',
        icon: Users,
      },
      {
        title: '新增用户',
        href: '/users/create',
        icon: Users,
      },
    ],
  },
  {
    title: '会员管理',
    href: '/members',
    icon: UserCheck,
    children: [
      {
        title: '会员列表',
        href: '/members',
        icon: UserCheck,
      },
      {
        title: '新增会员',
        href: '/members/create',
        icon: UserCheck,
      },
    ],
  },
  {
    title: '会员卡管理',
    href: '/card-templates',
    icon: CreditCard,
    children: [
      {
        title: '卡片模板',
        href: '/card-templates',
        icon: CreditCard,
      },
      {
        title: '新增模板',
        href: '/card-templates/create',
        icon: CreditCard,
      },
    ],
  },
  {
    title: '标签管理',
    href: '/tags',
    icon: Tags,
  },
  {
    title: '教师管理',
    href: '/teachers',
    icon: GraduationCap,
  },
  {
    title: '设置',
    href: '/settings',
    icon: Settings,
  },
];

// 开发环境专用导航项
const devNavigation: NavItem[] = [
  {
    title: '错误测试',
    href: '/error-test',
    icon: Bug,
  },
  {
    title: '功能示例',
    href: '/examples/ui-style',
    icon: Code,
    hasNotification: true,
    children: [
      {
        title: 'UI风格示例',
        href: '/examples/ui-style',
        icon: Code,
      },
      {
        title: 'UI设计系统',
        href: '/examples/ui-design-system',
        icon: Code,
      },
      {
        title: 'UI自定义组件',
        href: '/examples/dialogs',
        icon: Code,
      },
    ],
  },
];

// 根据环境导出不同的导航配置
export const navigation: NavItem[] =
  env.NODE_ENV === 'development'
    ? [...baseNavigation, ...devNavigation]
    : baseNavigation;
