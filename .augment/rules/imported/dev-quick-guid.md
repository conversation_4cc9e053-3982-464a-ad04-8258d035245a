---
type: "agent_requested"
description: "快速开发指南，用于指导常规页面开发任务"
---
# 中后台管理系统开发文档

## 🏗️ 核心项目目录结构

```
aug-admin/
├── src/
│   ├── app/                    # Next.js App Router页面
│   │   ├── (auth)/login/      # 登录页面
│   │   ├── dashboard/         # 仪表盘
│   │   ├── users/             # 用户管理
│   │   ├── members/           # 会员管理
│   │   └── layout.tsx         # 根布局
│   ├── components/            # UI组件
│   │   ├── ui/               # shadcn/ui基础组件 (永远只放 shadcn/ui 原始组件)
│   │   ├── layout/           # 布局组件(sidebar/header/breadcrumb)
│   │   ├── forms/            # 表单组件
│   │   ├── tables/           # 表格组件
│   │   │   ├── common/       # 通用表格组件
│   │   │   ├── columns/      # 表格列定义
│   │   │   ├── mobile/       # 移动端专用组件
│   │   │   └── index.ts      # 统一导出
│   │   └── providers/        # 上下文提供者
│   ├── lib/                  # 核心库
│   │   ├── api/              # API交互层

│   │   ├── auth/             # 认证模块
│   │   ├── stores/           # 状态管理(Zustand)
│   │   └── utils/            # 工具函数
│   ├── hooks/                # 自定义hooks
│   ├── types/                # TypeScript类型定义
│   ├── constants/            # 常量定义(API端点/路由/消息)
│   └── config/               # 配置文件(导航/站点/环境变量)
├── middleware.ts             # 路由保护中间件
├── tailwind.config.ts        # Tailwind CSS配置
├── tsconfig.json            # TypeScript配置
└── package.json             # 项目依赖
```

## 🛠️ 技术栈

- **框架**: Next.js 14 App Router + TypeScript
- **UI**: Tailwind CSS + shadcn/ui + lucide-react
- **状态管理**: TanStack Query + Zustand
- **表单**: React Hook Form + Zod
- **表格**: TanStack Table
- **HTTP**: Axios
- **主题**: next-themes

## 🎨 UI开发规范

### 1. 设计系统
- 基于 shadcn/ui 组件库，统一设计语言
- 使用 Tailwind CSS 原子化类，禁止自定义 CSS
- 支持亮色/暗色/系统主题切换
- 响应式设计优先(mobile-first)

#### 具体规范：

- 样式 ：100 % Tailwind 原子类   

  目的：与 shadcn/ui 同构，AI、开发同事都能一眼看懂

- 主题：强制使用 next-themes <ThemeProvider>	

  目的：保证所有自定义组件自动继承暗黑/亮色

- 响应式：强制 mobile-first

  用 shadcn/ui 的 `sm:`、`md:` 断点即可

- 单文件组件：一个组件一个文件，index.ts 统一导出

  目的：方便 tree-shaking

- 关于 `components/ui` 的铁律： `components/ui` 是 shadcn/ui 的圣域，禁止手改

  - **永远只放 shadcn/ui 原始组件**（Button、Dialog 等）。

  - **不允许放任何业务自定义组件**，哪怕只是 wrapper。

### 2. 布局规范
```tsx
// 页面布局结构
<MainLayout>
  {/* 页面内容 */}
</MainLayout>

// 布局层级
- 侧边栏: fixed, w-64/w-16, z-40
- 顶部栏: sticky top-0, h-16, z-30  
- 面包屑: sticky top-16, z-20
- 内容区: p-4, 自适应宽度
```

### 3. 组件开发规范
- 组件文件使用 PascalCase 命名
- 优先使用函数式组件和 hooks
- 组件 props 使用 TypeScript 接口定义
- 保持组件单一职责原则

### 4. 组件快速使用

#### 弹框组件
```tsx
// 确认弹框 - 增强渐变头部设计
import { ConfirmDialog } from '@/components/ui/confirm-dialog';

<ConfirmDialog
  open={deleteOpen}
  onOpenChange={setDeleteOpen}
  title="确认删除"
  description="确定要删除这个用户吗？此操作无法撤销。"
  variant="destructive"
  onConfirm={handleDelete}
  isLoading={isDeleting}
/>

// 表单弹框 - 现代化卡片分组设计
import { FormDialog, FormField } from '@/components/ui/form-dialog';

<FormDialog
  open={formOpen}
  onOpenChange={setFormOpen}
  title="编辑信息"
  description="请填写相关信息"
  size="md"
  onConfirm={handleSubmit}
  disabled={!isValid}
>
  <FormField label="姓名" required>
    <Input {...register('name')} />
  </FormField>
</FormDialog>

// 步骤式确认弹框 - 复杂操作专用
import { StepConfirmDialog } from '@/components/ui/confirm-dialog';

<StepConfirmDialog
  open={stepOpen}
  onOpenChange={setStepOpen}
  title="批量删除确认"
  description="此操作将执行以下步骤："
  steps={[
    { title: '删除用户数据', description: '包括基本信息', checked: true },
    { title: '清除关联记录', description: '删除相关数据', checked: true },
  ]}
  variant="destructive"
  onConfirm={handleBatchDelete}
/>
```

#### 数据状态组件
```tsx
import { DataStateWrapper, LoadingSpinner, EmptyState, ErrorState } from '@/components/ui/loading-states';

// 统一数据状态管理（推荐）
<DataStateWrapper
  isLoading={isLoading}
  isEmpty={data.length === 0}
  isError={hasError}
  emptyTitle="暂无数据"
  emptyDescription="点击按钮添加第一条记录"
  emptyActionLabel="添加数据"
  onEmptyAction={handleAdd}
  onRetry={refetch}
>
  <YourDataComponent data={data} />
</DataStateWrapper>

// 独立状态组件
<LoadingSpinner size="md" text="加载中..." />
<EmptyState title="暂无数据" action={{ label: "添加", onClick: handleAdd }} />
<ErrorState title="加载失败" onRetry={refetch} />
```

#### 表格组件
```tsx
import { CompactDataTable, DataTable, MobileCardList } from '@/components/tables';

// 紧凑型表格（推荐）
<CompactDataTable
  columns={columns}
  data={data}
  isLoading={isLoading}
  defaultSort={{ id: 'created_at', desc: true }}
/>

// 标准表格（带搜索和操作）
<DataTable
  columns={columns}
  data={data}
  title="用户列表"
  searchKey="name"
  onAdd={handleAdd}
  addButtonText="新增用户"
/>

// 移动端卡片列表
<MobileCardList
  data={data}
  renderer={{
    renderHeader: (item) => <div>{item.name}</div>,
    renderContent: (item) => <div>{item.description}</div>,
  }}
/>
```

### 5. 表格开发规范

#### 桌面端表格
```tsx
// 使用紧凑型表格组件
import { CompactDataTable } from '@/components/tables';

<CompactDataTable
  columns={columns}
  data={data}
  isLoading={isLoading}
/>
```

#### 移动端卡片布局 除非有明确指示，否则不要自动实现该布局
```tsx
// 使用通用移动端卡片组件
import { MobileCardList } from '@/components/tables';

const renderer = {
  renderHeader: (item) => <MobileCardHeader title={item.name} />,
  renderContent: (item) => <MobileCardGrid items={[...]} />,
};

<MobileCardList data={data} renderer={renderer} />
```

#### 响应式布局
```tsx
// 桌面端表格 + 移动端卡片
<div className="hidden sm:block">
  <CompactDataTable columns={columns} data={data} />
</div>
<div className="block sm:hidden">
  <MyDataMobile data={data} onEdit={onEdit} onDelete={onDelete} />
</div>
```

#### 列定义函数
```tsx
export function createXxxColumns({ onEdit, onDelete }) {
  return [
    { accessorKey: 'name', header: '名称' },
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }) => <ActionCell row={row} onEdit={onEdit} onDelete={onDelete} />
    }
  ];
}
```

### 5. 表单开发规范
```tsx
// 使用React Hook Form + Zod
const schema = z.object({
  name: z.string().min(2, '名称至少2个字符'),
});

const { register, handleSubmit, formState: { errors } } = useForm({
  resolver: zodResolver(schema),
});

// 表单容器使用Card
<Card>
  <CardHeader>
    <CardTitle>表单标题</CardTitle>
  </CardHeader>
  <CardContent>
    <form onSubmit={handleSubmit(onSubmit)}>
      {/* 表单字段 */}
    </form>
  </CardContent>
</Card>
```

## 🔄 数据流架构

```
页面组件 → 自定义hooks → API层 → 后端
    ↓
状态管理: 认证状态(Zustand) + 服务端状态(TanStack Query)
    ↓
类型安全: 全链路TypeScript类型定义
```

### API交互规范
```tsx
// 自定义hooks封装API调用
export function useUserList() {
  return useApiQuery(['users', 'list'], getUserList);
}

export function useCreateUser() {
  return useApiMutation(createUser, {
    successMessage: '创建成功',
    errorMessage: '创建失败', // Hook层统一处理错误
    invalidateQueries: [['users', 'list']],
  });
}

// 页面组件中的使用 - 避免重复错误处理
const handleSubmit = async (data) => {
  await createUserMutation.mutateAsync(data);
  router.push('/users');
  // 成功和错误消息由Hook统一处理，避免重复toast
};

// 页面中使用
const { data, isLoading } = useUserList();
const createMutation = useCreateUser();
```

## 🚀 快速开发流程

### 1. 环境准备
```bash
npm install
npm run dev  # 启动开发服务器(端口3005)
```

### 2. 新增功能模块
1. 在 `src/app` 下创建路由页面
2. 在 `src/components` 下添加相关组件  
3. 在 `src/lib/api` 下添加API函数
4. 在 `src/hooks` 下创建自定义hooks
5. 在 `src/types` 下定义类型
6. 在 `src/config/navigation.ts` 中添加导航项

### 3. 开发最佳实践
- 使用 TypeScript 严格模式
- 遵循 ESLint + Prettier 代码规范
- 合理使用 React.memo 和 useMemo 优化性能
- 统一的错误处理和用户提示
- 适当的加载状态和骨架屏

## 🔐 认证系统

- JWT Token 自动管理和刷新
- 路由保护中间件
- Zustand 状态持久化
- 安全的登录/登出流程

## 📝 开发示例

### 新增页面
```tsx
'use client';

import { MainLayout } from '@/components/layout/main-layout';

export default function MyPage() {
  return (
    <MainLayout>
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">我的页面</h1>
        {/* 页面内容 */}
      </div>
    </MainLayout>
  );
}
```

### API集成
```tsx
// 1. 添加API端点 (src/constants/api.ts)
MY_FEATURE: {
  LIST: '/api/v1/my-feature/',
  CREATE: '/api/v1/my-feature/',
}

// 2. 创建API函数 (src/lib/api/my-feature.ts)
export async function getMyFeatureList() {
  const response = await apiClient.get(API_ENDPOINTS.MY_FEATURE.LIST);
  return response.data;
}

// 3. 创建hooks (src/hooks/use-my-feature.ts)
export function useMyFeatureList() {
  return useApiQuery(['my-feature', 'list'], getMyFeatureList);
}
```

## 🎯 项目特色

- **现代化技术栈**: 基于最新的 React 生态系统
- **类型安全**: 全链路 TypeScript 类型定义
- **开发体验**: 完善的开发工具链和热重载
- **生产就绪**: 完整的认证、错误处理、性能优化
- **可扩展性**: 模块化设计，易于扩展新功能
- **响应式设计**: 完美适配桌面端和移动端

---

这个项目是一个高质量的中后台管理系统脚手架，代码结构清晰，功能完整，可以作为企业级项目的基础框架使用。
